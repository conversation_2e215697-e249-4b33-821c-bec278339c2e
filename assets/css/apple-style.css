/* Apple风格统一样式文件 */

/* 基础样式 */
body {
    background-color: #f2f2f7;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
    color: #1d1d1f;
    line-height: 1.47059;
    font-weight: 400;
}

.container-fluid {
    padding: 24px;
}

/* 页面头部样式 */
.page-header {
    background: #ffffff;
    border-radius: 12px;
    padding: 24px 32px;
    margin-bottom: 24px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.04);
    border: 1px solid rgba(0, 0, 0, 0.04);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.page-header h3 {
    font-size: 28px;
    font-weight: 600;
    color: #1d1d1f;
    margin: 0;
    letter-spacing: -0.003em;
}

/* 卡片样式 */
.card {
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.04);
    border: 1px solid rgba(0, 0, 0, 0.04);
    margin-bottom: 24px;
}

.card-header {
    background: #ffffff;
    border-bottom: 1px solid rgba(0, 0, 0, 0.04);
    padding: 20px 24px;
    font-size: 17px;
    font-weight: 600;
    color: #1d1d1f;
    letter-spacing: -0.022em;
    border-radius: 12px 12px 0 0;
}

.card-body {
    padding: 24px;
}

/* 按钮样式 */
.btn {
    border-radius: 8px;
    font-size: 15px;
    font-weight: 500;
    padding: 8px 16px;
    transition: all 0.2s ease-in-out;
    border: none;
    letter-spacing: -0.022em;
}

.btn-primary {
    background: #007aff;
    color: white;
    box-shadow: 0 4px 12px rgba(0, 122, 255, 0.3);
}

.btn-primary:hover {
    background: #0051d5;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(0, 122, 255, 0.4);
}

.btn-success {
    background: #34c759;
    color: white;
    box-shadow: 0 4px 12px rgba(52, 199, 89, 0.3);
}

.btn-success:hover {
    background: #248a3d;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(52, 199, 89, 0.4);
}

.btn-warning {
    background: #ff9500;
    color: white;
    box-shadow: 0 4px 12px rgba(255, 149, 0, 0.3);
}

.btn-warning:hover {
    background: #cc7700;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(255, 149, 0, 0.4);
}

.btn-danger {
    background: #ff3b30;
    color: white;
    box-shadow: 0 4px 12px rgba(255, 59, 48, 0.3);
}

.btn-danger:hover {
    background: #d70015;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(255, 59, 48, 0.4);
}

.btn-secondary {
    background: #f2f2f7;
    color: #1d1d1f;
    border: 1px solid rgba(0, 0, 0, 0.04);
}

.btn-secondary:hover {
    background: #e5e5ea;
    color: #1d1d1f;
    transform: translateY(-2px);
}

.btn-sm {
    padding: 6px 12px;
    font-size: 13px;
}

.btn-lg {
    padding: 12px 24px;
    font-size: 17px;
}

/* 表格样式 */
.table {
    background: #ffffff;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.04);
    border: 1px solid rgba(0, 0, 0, 0.04);
    margin-bottom: 0;
}

.table th,
.table thead th {
    background: #f9f9f9 !important;
    border-bottom: 1px solid rgba(0, 0, 0, 0.04) !important;
    border-top: none !important;
    color: #1d1d1f !important;
    font-weight: 600 !important;
    font-size: 15px !important;
    padding: 16px 20px !important;
    text-align: center !important;
    vertical-align: middle !important;
    letter-spacing: -0.022em !important;
}

.table td {
    border-bottom: 1px solid rgba(0, 0, 0, 0.04);
    color: #1d1d1f;
    font-size: 15px;
    padding: 16px 20px;
    text-align: center;
    vertical-align: middle;
}

.table tbody tr:hover {
    background-color: #f9f9f9;
}

.table tbody tr:last-child td {
    border-bottom: none;
}

/* 覆盖Bootstrap默认的表头样式 - 使用最高优先级 */
table.table thead.thead-dark th,
table.table thead.thead-light th,
table.table thead.table-dark th,
.table thead.thead-dark th,
.table thead.thead-light th,
.table thead.table-dark th,
.table-striped thead.thead-dark th,
.table-striped thead.thead-light th,
.table-bordered thead.thead-dark th,
.table-bordered thead.thead-light th,
.table-hover thead.thead-dark th,
.table-hover thead.thead-light th,
.table-striped thead tr th,
.table-bordered thead tr th,
.table-hover thead tr th,
.thead-light th,
.thead-dark th,
.table-dark th,
.table-dark thead th {
    background: #f9f9f9 !important;
    color: #1d1d1f !important;
    border-color: rgba(0, 0, 0, 0.04) !important;
    font-weight: 600 !important;
    font-size: 15px !important;
    padding: 16px 20px !important;
    text-align: center !important;
    vertical-align: middle !important;
    letter-spacing: -0.022em !important;
}

/* 强制覆盖所有可能的表头样式 */
thead th,
.table thead th,
.table-striped thead th,
.table-bordered thead th,
.table-hover thead th,
.table-sm thead th {
    background-color: #f9f9f9 !important;
    background-image: none !important;
    color: #1d1d1f !important;
    border-color: rgba(0, 0, 0, 0.04) !important;
}

/* 确保所有表格都使用统一样式 */
table thead th,
table.table thead th,
.table thead th,
.table-striped thead th,
.table-bordered thead th,
.table-hover thead th {
    background-color: #f9f9f9 !important;
    background-image: none !important;
    color: #1d1d1f !important;
    border-bottom: 1px solid rgba(0, 0, 0, 0.04) !important;
    border-top: none !important;
    font-weight: 600 !important;
    font-size: 15px !important;
    padding: 16px 20px !important;
    text-align: center !important;
    vertical-align: middle !important;
    letter-spacing: -0.022em !important;
}

/* 表单样式 */
.form-control {
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    padding: 12px 16px;
    font-size: 15px;
    color: #1d1d1f;
    background: #ffffff;
    transition: all 0.2s ease;
}

.form-control:focus {
    border-color: #007aff;
    box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
    outline: none;
}

/* 下拉框特殊样式 */
select.form-control {
    padding: 8px 12px;
    height: auto;
    line-height: 1.5;
}

/* 小尺寸表单控件 */
.form-control-sm {
    padding: 6px 10px !important;
    font-size: 14px !important;
    height: auto !important;
    line-height: 1.4 !important;
}

select.form-control-sm {
    padding: 4px 8px !important;
    height: 32px !important;
    line-height: 1.4 !important;
}

/* 确保下拉框选项正常显示 */
select.form-control option,
select.form-control-sm option {
    padding: 4px 8px;
    color: #1d1d1f;
    background: #ffffff;
}

/* 修复筛选区域的下拉框 */
.card-body select.form-control,
.card-body select.form-control-sm {
    min-width: 120px;
    width: 100%;
}

/* 修复分页区域的显示 */
.pagination .page-link {
    padding: 8px 12px;
    font-size: 14px;
}

/* 修复每页显示条数选择器 */
.page-size-selector select {
    min-width: 80px;
    padding: 4px 8px !important;
    height: 32px !important;
}

.form-group label {
    font-weight: 500;
    color: #1d1d1f;
    font-size: 15px;
    margin-bottom: 8px;
}

/* 搜索框样式 */
.search-box {
    background: #ffffff;
    border-radius: 12px;
    padding: 20px 24px;
    margin-bottom: 24px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.04);
    border: 1px solid rgba(0, 0, 0, 0.04);
}

.search-box .input-group {
    border-radius: 8px;
    overflow: hidden;
    border: 1px solid rgba(0, 0, 0, 0.1);
}

.search-box .input-group-prepend {
    margin-right: 0;
}

.search-box .input-group-text {
    background-color: #f9f9f9;
    border: none;
    border-right: 1px solid rgba(0, 0, 0, 0.1);
    color: #86868b;
    font-size: 15px;
    padding: 12px 16px;
    border-radius: 0;
}

.search-box .form-control {
    border: none;
    border-radius: 0;
    padding: 12px 16px;
    font-size: 15px;
}

.search-box .form-control:focus {
    border: none;
    box-shadow: none;
    outline: none;
}

.search-box .input-group:focus-within {
    border-color: #007aff;
    box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
}

.search-box .input-group-append {
    margin-left: 0;
}

.search-box .input-group-append .btn {
    border-radius: 0;
    border: none;
    border-left: 1px solid rgba(0, 0, 0, 0.1);
}

/* 确保input-group的完整性 */
.search-box .input-group-prepend:first-child .input-group-text {
    border-top-left-radius: 8px;
    border-bottom-left-radius: 8px;
}

.search-box .input-group-append:last-child .btn:last-child {
    border-top-right-radius: 8px;
    border-bottom-right-radius: 8px;
}

/* 修复可能的Bootstrap冲突 */
.search-box .input-group > .input-group-prepend > .input-group-text {
    border-right: 1px solid rgba(0, 0, 0, 0.1) !important;
    background-color: #f9f9f9 !important;
}

.search-box .input-group > .form-control {
    border: none !important;
}

.search-box .input-group > .input-group-append > .btn {
    border-left: 1px solid rgba(0, 0, 0, 0.1) !important;
}

/* 分页样式 */
.pagination {
    justify-content: center;
    margin-top: 24px;
}

.page-link {
    border: 1px solid rgba(0, 0, 0, 0.04);
    color: #1d1d1f;
    padding: 8px 12px;
    margin: 0 2px;
    border-radius: 8px;
    font-size: 15px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.page-link:hover {
    background: #f2f2f7;
    color: #1d1d1f;
    border-color: rgba(0, 0, 0, 0.08);
}

.page-item.active .page-link {
    background: #007aff;
    border-color: #007aff;
    color: white;
    box-shadow: 0 4px 12px rgba(0, 122, 255, 0.3);
}

/* 徽章样式 */
.badge {
    padding: 4px 8px;
    font-size: 11px;
    font-weight: 500;
    border-radius: 6px;
    letter-spacing: 0.06em;
}

.badge-primary {
    background-color: rgba(0, 122, 255, 0.1);
    color: #007aff;
}

.badge-success {
    background-color: rgba(52, 199, 89, 0.1);
    color: #34c759;
}

.badge-warning {
    background-color: rgba(255, 149, 0, 0.1);
    color: #ff9500;
}

.badge-danger {
    background-color: rgba(255, 59, 48, 0.1);
    color: #ff3b30;
}

.badge-secondary {
    background-color: rgba(142, 142, 147, 0.1);
    color: #8e8e93;
}

/* 模态框样式 */
.modal-content {
    border-radius: 12px;
    border: none;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.modal-header {
    border-bottom: 1px solid rgba(0, 0, 0, 0.04);
    padding: 20px 24px;
    border-radius: 12px 12px 0 0;
}

.modal-body {
    padding: 24px;
}

.modal-footer {
    border-top: 1px solid rgba(0, 0, 0, 0.04);
    padding: 20px 24px;
    border-radius: 0 0 12px 12px;
}

/* 警告框样式 */
.alert {
    border-radius: 12px;
    border: none;
    padding: 16px 20px;
    font-size: 15px;
    margin-bottom: 24px;
}

.alert-success {
    background-color: rgba(52, 199, 89, 0.1);
    color: #34c759;
}

.alert-warning {
    background-color: rgba(255, 149, 0, 0.1);
    color: #ff9500;
}

.alert-danger {
    background-color: rgba(255, 59, 48, 0.1);
    color: #ff3b30;
}

.alert-info {
    background-color: rgba(0, 122, 255, 0.1);
    color: #007aff;
}

/* 工具栏样式 */
.toolbar {
    background: #ffffff;
    border-radius: 12px;
    padding: 20px 24px;
    margin-bottom: 24px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.04);
    border: 1px solid rgba(0, 0, 0, 0.04);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 12px;
}

.toolbar .btn-group {
    display: flex;
    gap: 8px;
}

/* 统计卡片样式 */
.stat-card {
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.04);
    border: 1px solid rgba(0, 0, 0, 0.04);
    padding: 20px;
    text-align: center;
    transition: all 0.2s ease-in-out;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.08);
}

.stat-card .stat-number {
    font-size: 32px;
    font-weight: 600;
    color: #1d1d1f;
    margin-bottom: 4px;
    letter-spacing: -0.003em;
}

.stat-card .stat-label {
    font-size: 13px;
    color: #86868b;
    font-weight: 500;
}

/* 列表组样式 */
.list-group {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.04);
    border: 1px solid rgba(0, 0, 0, 0.04);
}

.list-group-item {
    border: none;
    border-bottom: 1px solid rgba(0, 0, 0, 0.04);
    padding: 16px 20px;
    background: #ffffff;
    color: #1d1d1f;
    font-size: 15px;
    transition: background-color 0.2s ease;
}

.list-group-item:hover {
    background-color: #f9f9f9;
}

.list-group-item:last-child {
    border-bottom: none;
}

/* 进度条样式 */
.progress {
    height: 8px;
    border-radius: 4px;
    background-color: #f2f2f7;
    overflow: hidden;
}

.progress-bar {
    border-radius: 4px;
    transition: width 0.3s ease;
}

.progress-bar-primary {
    background-color: #007aff;
}

.progress-bar-success {
    background-color: #34c759;
}

.progress-bar-warning {
    background-color: #ff9500;
}

.progress-bar-danger {
    background-color: #ff3b30;
}

/* 导航标签样式 */
.nav-tabs {
    border-bottom: 1px solid rgba(0, 0, 0, 0.04);
    margin-bottom: 24px;
}

.nav-tabs .nav-link {
    border: none;
    border-radius: 8px 8px 0 0;
    color: #86868b;
    font-weight: 500;
    padding: 12px 20px;
    margin-right: 4px;
    transition: all 0.2s ease;
}

.nav-tabs .nav-link:hover {
    color: #1d1d1f;
    background-color: #f9f9f9;
}

.nav-tabs .nav-link.active {
    color: #007aff;
    background-color: #ffffff;
    border-bottom: 2px solid #007aff;
}

/* 下拉菜单样式 */
.dropdown-menu {
    border-radius: 12px;
    border: 1px solid rgba(0, 0, 0, 0.04);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    padding: 8px 0;
    margin-top: 4px;
}

.dropdown-item {
    padding: 8px 16px;
    font-size: 15px;
    color: #1d1d1f;
    transition: background-color 0.2s ease;
}

.dropdown-item:hover {
    background-color: #f2f2f7;
    color: #1d1d1f;
}

.dropdown-divider {
    margin: 4px 0;
    border-color: rgba(0, 0, 0, 0.04);
}

/* 输入组样式 */
.input-group {
    border-radius: 8px;
    overflow: hidden;
    border: 1px solid rgba(0, 0, 0, 0.1);
}

.input-group-prepend {
    margin-right: 0;
}

.input-group-append {
    margin-left: 0;
}

.input-group-text {
    background-color: #f9f9f9;
    border: none;
    color: #86868b;
    font-size: 15px;
    padding: 12px 16px;
    border-radius: 0;
}

.input-group-prepend .input-group-text {
    border-right: 1px solid rgba(0, 0, 0, 0.1);
}

.input-group-append .input-group-text {
    border-left: 1px solid rgba(0, 0, 0, 0.1);
}

.input-group .form-control {
    border: none;
    border-radius: 0;
}

.input-group .form-control:focus {
    border: none;
    box-shadow: none;
    outline: none;
}

.input-group:focus-within {
    border-color: #007aff;
    box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
}

.input-group .btn {
    border-radius: 0;
    border: none;
}

.input-group-append .btn {
    border-left: 1px solid rgba(0, 0, 0, 0.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container-fluid {
        padding: 16px;
    }

    .page-header {
        padding: 20px 24px;
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;
    }

    .page-header h3 {
        font-size: 24px;
    }

    .toolbar {
        padding: 16px 20px;
        flex-direction: column;
        align-items: stretch;
    }

    .toolbar .btn-group {
        justify-content: center;
    }

    .table th,
    .table td {
        padding: 12px 16px;
        font-size: 14px;
    }

    .btn {
        font-size: 14px;
        padding: 10px 16px;
    }

    .btn-sm {
        padding: 8px 12px;
        font-size: 13px;
    }
}

/* 加载动画 */
.loading {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid rgba(0, 122, 255, 0.2);
    border-radius: 50%;
    border-top-color: #007aff;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* 空状态样式 */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #86868b;
}

.empty-state i {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
}

.empty-state h5 {
    font-size: 17px;
    font-weight: 500;
    margin-bottom: 8px;
    color: #1d1d1f;
}

.empty-state p {
    font-size: 15px;
    margin: 0;
}

/* 工具提示样式 */
.tooltip {
    font-size: 13px;
}

.tooltip-inner {
    background-color: #1d1d1f;
    border-radius: 6px;
    padding: 6px 10px;
}

/* 开关样式 */
.custom-switch .custom-control-label::before {
    border-radius: 16px;
    background-color: #f2f2f7;
    border: 1px solid rgba(0, 0, 0, 0.1);
}

.custom-switch .custom-control-input:checked ~ .custom-control-label::before {
    background-color: #34c759;
    border-color: #34c759;
}

.custom-switch .custom-control-label::after {
    border-radius: 50%;
    background-color: #ffffff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
