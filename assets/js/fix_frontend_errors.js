/**
 * 修复前端JavaScript错误
 * 
 * 这个脚本用于修复Chrome扩展错误
 */

// 立即执行函数
(function() {
    // 捕获错误
    window.addEventListener('error', function(event) {
        if (event.message && (event.message.includes('Receiving end does not exist') ||
                             event.message.includes('Cannot set properties of null') ||
                             event.message.includes('The message port closed before a response was received') ||
                             event.message.includes('runtime.lastError'))) {
            event.preventDefault();
            event.stopPropagation();
            return true;
        }
    }, true);

    // 捕获Promise错误
    window.addEventListener('unhandledrejection', function(event) {
        if (event.reason && event.reason.message &&
            (event.reason.message.includes('Receiving end does not exist') ||
             event.reason.message.includes('Cannot set properties of null') ||
             event.reason.message.includes('The message port closed before a response was received') ||
             event.reason.message.includes('runtime.lastError'))) {
            event.preventDefault();
            event.stopPropagation();
            return true;
        }
    });
    
    // 修复Chrome扩展冲突
    if (typeof chrome !== 'undefined' && chrome.runtime) {
        try {
            // 重写chrome.runtime.lastError
            Object.defineProperty(chrome.runtime, 'lastError', {
                get: function() { return null; },
                configurable: true
            });

            // 重写chrome.runtime.sendMessage以避免错误
            const originalSendMessage = chrome.runtime.sendMessage;
            chrome.runtime.sendMessage = function(...args) {
                try {
                    return originalSendMessage.apply(this, args);
                } catch(e) {
                    // 静默处理扩展错误
                    return;
                }
            };
        } catch(e) {
            // 静默处理任何设置错误
        }
    }

    // 监听键盘事件，特别是Ctrl键，防止扩展错误
    document.addEventListener('keydown', function(event) {
        if (event.ctrlKey) {
            // 延迟清理可能的扩展错误
            setTimeout(function() {
                if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.lastError) {
                    try {
                        chrome.runtime.lastError = null;
                    } catch(e) {}
                }
            }, 10);
        }
    }, true);
    
    // 如果使用jQuery，修复jQuery的AJAX
    if (typeof jQuery !== 'undefined') {
        jQuery(document).ajaxError(function(event, jqXHR, ajaxSettings, thrownError) {
            if (thrownError && (thrownError.toString().includes('Receiving end does not exist') ||
                               thrownError.toString().includes('The message port closed before a response was received') ||
                               thrownError.toString().includes('runtime.lastError'))) {
                event.preventDefault();
                event.stopPropagation();
                return true;
            }
        });
    }
})(); 