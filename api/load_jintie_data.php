<?php
// 设置响应头
header('Content-Type: application/json; charset=utf-8');

// 使用config.php的数据库配置
require_once '../includes/config.php';

// 检查是否登录
if(!isset($_SESSION['user'])) {
    echo json_encode(['success' => false, 'message' => '未登录']);
    exit;
}

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => '方法不允许']);
    exit;
}

try {
    $recordDate = $_GET['record_date'] ?? date('Y-m-d');

    // 查询参数 - 检查表是否存在
    $params = null;
    try {
        $paramSql = "SELECT * FROM jintie_params WHERE record_date = ?";
        $paramStmt = $pdo->prepare($paramSql);
        $paramStmt->execute([$recordDate]);
        $params = $paramStmt->fetch(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        // 如果表不存在，使用默认参数
        $params = [
            'workers_count' => 15,
            'jintie_total' => 10000,
            'distribution' => 0
        ];
    }

    // 查询员工数据 - 转换字段名以匹配前端期望的格式
    $employeeSql = "SELECT
        id,
        record_date,
        month_year,
        name,
        dept,
        fixed_amount as fixed,
        floating_amount as floating,
        fixed_actual,
        floating_actual,
        days,
        deduction,
        personal_bonus,
        general_bonus,
        total_amount as total,
        is_fixed_bonus,
        floating_ratio,
        remark
    FROM jintie_records WHERE record_date = ? ORDER BY id";
    $employeeStmt = $pdo->prepare($employeeSql);
    $employeeStmt->execute([$recordDate]);
    $employees = $employeeStmt->fetchAll(PDO::FETCH_ASSOC);

    // 查询岗位设置 - 使用正确的表名
    $settings = null;
    try {
        $settingSql = "SELECT * FROM jintie_dept_settings ORDER BY id DESC LIMIT 1";
        $settingStmt = $pdo->prepare($settingSql);
        $settingStmt->execute();
        $settings = $settingStmt->fetch(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        // 如果表不存在，使用默认设置
        $settings = [
            'jintie_total' => 10000,
            'monitoring_base_amount' => 2500,
            'cb26_base_amount' => 400
        ];
    }

    echo json_encode([
        'success' => true,
        'data' => [
            'params' => $params,
            'employees' => $employees,
            'settings' => $settings,
            'record_date' => $recordDate
        ]
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    error_log("加载津贴数据失败: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => '加载失败: ' . $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine()
    ], JSON_UNESCAPED_UNICODE);
}
?>
