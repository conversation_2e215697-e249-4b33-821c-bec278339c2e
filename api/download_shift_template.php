<?php
// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 设置正确的路径
$rootPath = dirname(__DIR__); // 获取上级目录 (zdh)
require_once $rootPath . '/includes/config.php';

// 加载vendor autoload
$vendorPath = dirname($rootPath) . '/vendor/autoload.php';

if (!file_exists($vendorPath)) {
    http_response_code(500);
    exit('PhpSpreadsheet库未安装，请联系管理员');
}

try {
    require_once $vendorPath;
} catch (Exception $e) {
    http_response_code(500);
    exit('加载PhpSpreadsheet库失败：' . $e->getMessage());
}

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;

// 验证PhpSpreadsheet是否正确加载
if (!class_exists('PhpOffice\PhpSpreadsheet\Spreadsheet')) {
    http_response_code(500);
    exit('PhpSpreadsheet库加载失败');
}

// 检查用户权限
if (!isset($_SESSION['user'])) {
    http_response_code(403);
    exit('未授权访问');
}

$user = $_SESSION['user'];
$hasPermission = $user['role'] === 'admin';

if (!$hasPermission) {
    http_response_code(403);
    exit('权限不足，只有管理员可以下载模板');
}

try {
    // 创建新的Spreadsheet对象
    $spreadsheet = new Spreadsheet();
    $sheet = $spreadsheet->getActiveSheet();
    
    // 设置工作表名称
    $sheet->setTitle('倒班导入模板');
    
    // 设置标题行
    $currentRow = 1;
    $sheet->setCellValue('A' . $currentRow, '倒班时间导入模板');
    $sheet->mergeCells('A' . $currentRow . ':E' . $currentRow);
    $sheet->getStyle('A' . $currentRow)->getFont()->setBold(true)->setSize(16);
    $sheet->getStyle('A' . $currentRow)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
    
    $currentRow++;
    $sheet->setCellValue('A' . $currentRow, '请按照以下格式填写排班数据，然后保存为Excel文件进行导入');
    $sheet->mergeCells('A' . $currentRow . ':E' . $currentRow);
    $sheet->getStyle('A' . $currentRow)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
    $sheet->getStyle('A' . $currentRow)->getFont()->setItalic(true);
    
    $currentRow += 2; // 空行
    
    // 设置表头
    $headers = [
        '班次', '开始日期', '结束日期'
    ];
    
    $col = 'A';
    foreach ($headers as $header) {
        $sheet->setCellValue($col . $currentRow, $header);
        $col++;
    }
    
    // 表头样式
    $sheet->getStyle('A' . $currentRow . ':C' . $currentRow)->getFont()->setBold(true);
    $sheet->getStyle('A' . $currentRow . ':C' . $currentRow)->getFill()
        ->setFillType(Fill::FILL_SOLID)
        ->getStartColor()->setRGB('D3D3D3');
    $sheet->getStyle('A' . $currentRow . ':C' . $currentRow)->getAlignment()
        ->setHorizontal(Alignment::HORIZONTAL_CENTER)
        ->setVertical(Alignment::VERTICAL_CENTER);
    
    // 添加示例数据
    $examples = [
        ['一站', '2025-01-01', '2025-01-15'],
        ['二站', '2025-01-16', '2025-01-31'],
        ['一站', '2025-02-01', '2025-02-14'],
        ['二站', '2025-02-15', '2025-02-28'],
        ['一站', '2025-07-31', '2025-08-15'] // 跨月示例，按开始日期归类到7月
    ];
    
    foreach ($examples as $example) {
        $currentRow++;
        $sheet->setCellValue('A' . $currentRow, $example[0]); // 班次
        $sheet->setCellValue('B' . $currentRow, $example[1]); // 开始日期
        $sheet->setCellValue('C' . $currentRow, $example[2]); // 结束日期

        // 示例数据样式
        $sheet->getStyle('A' . $currentRow . ':C' . $currentRow)->getAlignment()
            ->setHorizontal(Alignment::HORIZONTAL_CENTER)
            ->setVertical(Alignment::VERTICAL_CENTER);

        // 根据班次设置背景颜色
        if ($example[0] === '一站') {
            $backgroundColor = 'E8F4FD'; // 浅蓝色
        } else {
            $backgroundColor = 'E8F5E8'; // 浅绿色
        }

        $sheet->getStyle('A' . $currentRow . ':C' . $currentRow)->getFill()
            ->setFillType(Fill::FILL_SOLID)
            ->getStartColor()->setRGB($backgroundColor);
    }
    
    // 添加说明信息
    $currentRow += 2;
    $sheet->setCellValue('A' . $currentRow, '填写说明：（以下为说明文字，导入时会自动跳过）');
    $sheet->getStyle('A' . $currentRow)->getFont()->setBold(true);
    $sheet->getStyle('A' . $currentRow)->getFill()
        ->setFillType(Fill::FILL_SOLID)
        ->getStartColor()->setRGB('FFE6CC');
    
    $instructions = [
        '1. 班次：只能填写"一站"或"二站"',
        '2. 开始日期：支持多种格式',
        '   - YYYY-MM-DD（如：2025-01-01）',
        '   - YYYY/MM/DD（如：2025/01/01）',
        '   - 或直接在Excel中输入日期',
        '3. 结束日期：格式同开始日期',
        '4. 系统会根据开始日期自动识别年份和月份',
        '5. 跨月排班按开始日期的月份归类（如：7/31-8/15归类到7月）',
        '6. 系统会自动计算天数，无需手动填写',
        '7. 请删除示例数据，填写实际排班信息',
        '8. 保存文件后，在系统中选择"导入Excel"进行上传',
        '',
        '注意：导入时系统会自动跳过说明行和空行，只处理有效的数据行'
    ];
    
    foreach ($instructions as $instruction) {
        $currentRow++;
        $sheet->setCellValue('A' . $currentRow, $instruction);
        $sheet->mergeCells('A' . $currentRow . ':E' . $currentRow);
        // 为说明行添加浅橙色背景
        $sheet->getStyle('A' . $currentRow . ':E' . $currentRow)->getFill()
            ->setFillType(Fill::FILL_SOLID)
            ->getStartColor()->setRGB('FFF2E8');
    }
    
    // 设置列宽
    $columnWidths = [
        'A' => 15, // 班次
        'B' => 18, // 开始日期
        'C' => 18  // 结束日期
    ];
    
    foreach ($columnWidths as $col => $width) {
        $sheet->getColumnDimension($col)->setWidth($width);
    }
    
    // 设置边框
    $styleArray = [
        'borders' => [
            'allBorders' => [
                'borderStyle' => Border::BORDER_THIN,
                'color' => ['rgb' => '000000'],
            ],
        ],
    ];
    
    // 为表头和示例数据添加边框
    $dataStartRow = 4; // 表头行
    $dataEndRow = 9;   // 示例数据结束行（现在有5行示例）
    $sheet->getStyle('A' . $dataStartRow . ':C' . $dataEndRow)->applyFromArray($styleArray);
    
    // 设置文件名和输出
    $filename = '倒班导入模板_' . date('Y-m-d') . '.xlsx';
    
    // 记录操作日志
    if (function_exists('logAction')) {
        logAction('下载倒班导入模板', 'shift_schedules', [
            'filename' => $filename
        ]);
    }
    
    header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Cache-Control: max-age=0');
    
    $writer = new Xlsx($spreadsheet);
    $writer->save('php://output');
    exit;

} catch (Exception $e) {
    http_response_code(500);
    exit('下载失败：' . $e->getMessage());
}
?>
