# 禁止直接浏览器访问 API 文件夹
# Deny direct browser access to API folder

# 禁止直接通过浏览器访问（检查 User-Agent 和 Referer）
# Block direct browser access (check User-Agent and Referer)
RewriteEngine On

# 只阻止明显的浏览器直接访问
# Only block obvious direct browser access
RewriteCond %{HTTP_USER_AGENT} "Mozilla.*Chrome|Mozilla.*Firefox|Mozilla.*Safari" [NC]
RewriteCond %{HTTP_REFERER} ^$
RewriteCond %{REQUEST_METHOD} ^GET$ [NC]
RewriteRule ^.*\.php$ - [F,L]

# 允许 POST 请求和来自合法来源的请求
# Allow POST requests and requests from legitimate sources
<FilesMatch "\.(php)$">
    # 允许 POST 请求（微信小程序主要使用 POST）
    # Allow POST requests (WeChat Mini Program mainly uses POST)
    <RequireAny>
        Require method POST
        Require method OPTIONS
        # 允许来自你的域名的请求
        Require expr "%{HTTP_REFERER} =~ m#^https?://(.*\.)?sunxiyue\.com#i"
    </RequireAny>
</FilesMatch>

# 禁止显示目录列表
# Disable directory browsing
Options -Indexes

# 禁止访问敏感文件
# Deny access to sensitive files
<FilesMatch "\.(log|txt|md|sql|bak|backup)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# 设置错误页面（可选）
# Set custom error page (optional)
ErrorDocument 403 "Access Denied - API files cannot be accessed directly"
