<?php
// 启动会话
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// 设置响应头
header('Content-Type: application/json; charset=utf-8');

// 生产环境下关闭错误显示
error_reporting(0);
ini_set('display_errors', 0);

try {
    // 使用config.php的数据库配置
    require_once '../includes/config.php';

    // 检查PDO连接是否成功
    if (!isset($pdo)) {
        throw new Exception('数据库连接对象不存在');
    }

    // 检查是否登录
    if(!isset($_SESSION['user'])) {
        echo json_encode(['success' => false, 'message' => '未登录']);
        exit;
    }

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => '初始化失败: ' . $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine()
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => '方法不允许']);
    exit;
}

try {
    $year = $_GET['year'] ?? date('Y');

    // 验证年份格式
    if (!preg_match('/^\d{4}$/', $year)) {
        echo json_encode(['success' => false, 'message' => '无效的年份格式: ' . $year]);
        exit;
    }

    // 记录请求日志
    // error_log("获取历史记录列表 - 年份: " . $year);
    
    // 查询指定年份的历史记录，按日期分组
    // 先检查表是否存在
    $tableCheck = $pdo->query("SHOW TABLES LIKE 'jintie_records'");
    if ($tableCheck->rowCount() == 0) {
        // 表不存在，返回空数据
        echo json_encode([
            'success' => true,
            'data' => [],
            'year' => $year,
            'total_records' => 0,
            'message' => '暂无历史记录表'
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }

    $sql = "SELECT
                record_date,
                DATE_FORMAT(record_date, '%Y-%m') as month_year,
                COUNT(*) as record_count,
                SUM(total_amount) as jintie_total
            FROM jintie_records
            WHERE YEAR(record_date) = ?
            GROUP BY record_date, DATE_FORMAT(record_date, '%Y-%m')
            ORDER BY record_date DESC";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$year]);
    $records = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // 如果没有记录，返回空数组
    if (empty($records)) {
        echo json_encode([
            'success' => true,
            'data' => [],
            'year' => $year,
            'total_records' => 0
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 格式化数据
    $formattedRecords = [];
    foreach ($records as $record) {
        $formattedRecords[] = [
            'record_date' => $record['record_date'],
            'month_year' => $record['month_year'],
            'record_count' => intval($record['record_count']),
            'jintie_total' => floatval($record['jintie_total'])
        ];
    }
    
    echo json_encode([
        'success' => true,
        'data' => $formattedRecords,
        'year' => $year,
        'total_records' => count($formattedRecords)
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    error_log("获取历史记录列表失败: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false, 
        'message' => '获取历史记录失败: ' . $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine()
    ], JSON_UNESCAPED_UNICODE);
}
?>
