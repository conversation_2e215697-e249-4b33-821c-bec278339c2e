<?php
// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 设置正确的路径
$rootPath = dirname(__DIR__); // 获取上级目录 (zdh)
require_once $rootPath . '/includes/config.php';

// 加载vendor autoload
$vendorPath = dirname($rootPath) . '/vendor/autoload.php';

if (!file_exists($vendorPath)) {
    http_response_code(500);
    exit('PhpSpreadsheet库未安装，请联系管理员');
}

try {
    require_once $vendorPath;
} catch (Exception $e) {
    http_response_code(500);
    exit('加载PhpSpreadsheet库失败：' . $e->getMessage());
}

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;

// 验证PhpSpreadsheet是否正确加载
if (!class_exists('PhpOffice\PhpSpreadsheet\Spreadsheet')) {
    http_response_code(500);
    exit('PhpSpreadsheet库加载失败');
}

// 检查用户权限
if (!isset($_SESSION['user'])) {
    http_response_code(403);
    exit('未授权访问');
}

$user = $_SESSION['user'];
$hasPermission = $user['role'] === 'admin';

if (!$hasPermission) {
    http_response_code(403);
    exit('权限不足，只有管理员可以导出数据');
}

try {
    // 获取参数
    $year = $_GET['year'] ?? date('Y');
    $month = $_GET['month'] ?? '';
    $station = $_GET['station'] ?? '';
    
    // 构建查询条件
    $where = ["year = ?"];
    $params = [$year];
    
    if (!empty($month)) {
        $where[] = "month = ?";
        $params[] = $month;
    }
    
    if (!empty($station)) {
        $where[] = "station = ?";
        $params[] = $station;
    }
    
    $whereClause = implode(' AND ', $where);
    
    // 获取排班数据
    $stmt = $pdo->prepare("
        SELECT id, year, month, station, 
               DATE_FORMAT(start_date, '%Y-%m-%d') as start_date,
               DATE_FORMAT(end_date, '%Y-%m-%d') as end_date,
               days,
               DATE_FORMAT(created_at, '%Y-%m-%d %H:%i') as created_at
        FROM shift_schedules 
        WHERE {$whereClause}
        ORDER BY year ASC, month ASC, start_date ASC
    ");
    $stmt->execute($params);
    $schedules = $stmt->fetchAll();
    
    if (empty($schedules)) {
        http_response_code(404);
        exit('没有找到符合条件的排班数据');
    }
    
    // 创建新的Spreadsheet对象
    $spreadsheet = new Spreadsheet();
    $sheet = $spreadsheet->getActiveSheet();
    
    // 设置工作表名称和标题
    $titleText = $year . '年倒班时间表';
    if (!empty($month)) {
        $titleText = $year . '年' . $month . '月倒班时间表';
    }
    if (!empty($station)) {
        $titleText .= '(' . $station . ')';
    }
    
    $sheet->setTitle('倒班时间表');
    
    // 设置标题行
    $currentRow = 1;
    $sheet->setCellValue('A' . $currentRow, $titleText);
    $sheet->mergeCells('A' . $currentRow . ':H' . $currentRow);
    $sheet->getStyle('A' . $currentRow)->getFont()->setBold(true)->setSize(16);
    $sheet->getStyle('A' . $currentRow)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
    
    $currentRow++;
    $sheet->setCellValue('A' . $currentRow, '导出时间：' . date('Y-m-d H:i:s'));
    $sheet->mergeCells('A' . $currentRow . ':H' . $currentRow);
    $sheet->getStyle('A' . $currentRow)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
    
    $currentRow++;
    $sheet->setCellValue('A' . $currentRow, '数据统计：共 ' . count($schedules) . ' 条排班记录');
    $sheet->mergeCells('A' . $currentRow . ':H' . $currentRow);
    $sheet->getStyle('A' . $currentRow)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
    
    $currentRow += 2; // 空行
    
    // 设置表头
    $headers = [
        '序号', '年份', '月份', '班次', '开始日期', '结束日期', '天数', '创建时间'
    ];
    
    $col = 'A';
    foreach ($headers as $header) {
        $sheet->setCellValue($col . $currentRow, $header);
        $col++;
    }
    
    // 表头样式
    $sheet->getStyle('A' . $currentRow . ':H' . $currentRow)->getFont()->setBold(true);
    $sheet->getStyle('A' . $currentRow . ':H' . $currentRow)->getFill()
        ->setFillType(Fill::FILL_SOLID)
        ->getStartColor()->setRGB('D3D3D3');
    $sheet->getStyle('A' . $currentRow . ':H' . $currentRow)->getAlignment()
        ->setHorizontal(Alignment::HORIZONTAL_CENTER)
        ->setVertical(Alignment::VERTICAL_CENTER);
    
    // 数据行
    foreach ($schedules as $index => $schedule) {
        $currentRow++;
        $sheet->setCellValue('A' . $currentRow, $index + 1);
        $sheet->setCellValue('B' . $currentRow, $schedule['year']);
        $sheet->setCellValue('C' . $currentRow, $schedule['month'] . '月');
        $sheet->setCellValue('D' . $currentRow, $schedule['station']);
        $sheet->setCellValue('E' . $currentRow, $schedule['start_date']);
        $sheet->setCellValue('F' . $currentRow, $schedule['end_date']);
        $sheet->setCellValue('G' . $currentRow, $schedule['days'] . '天');
        $sheet->setCellValue('H' . $currentRow, $schedule['created_at']);
    
        // 数据行样式
        $sheet->getStyle('A' . $currentRow . ':H' . $currentRow)->getAlignment()
            ->setHorizontal(Alignment::HORIZONTAL_CENTER)
            ->setVertical(Alignment::VERTICAL_CENTER);
    
        // 根据班次设置不同的背景颜色
        $station = $schedule['station'];
        $backgroundColor = '';

        if (strpos($station, '一站') !== false) {
            $backgroundColor = 'E8F4FD'; // 浅蓝色 - 一站
        } elseif (strpos($station, '二站') !== false) {
            $backgroundColor = 'E8F5E8'; // 浅绿色 - 二站
        } else {
            $backgroundColor = 'E8F4FD'; // 默认使用一站颜色
        }
    
        // 应用背景颜色
        if ($backgroundColor) {
            $sheet->getStyle('A' . $currentRow . ':H' . $currentRow)->getFill()
                ->setFillType(Fill::FILL_SOLID)
                ->getStartColor()->setRGB($backgroundColor);
        }
    }
    
    // 设置列宽
    $columnWidths = [
        'A' => 8,  // 序号
        'B' => 10, // 年份
        'C' => 10, // 月份
        'D' => 12, // 班次
        'E' => 15, // 开始日期
        'F' => 15, // 结束日期
        'G' => 10, // 天数
        'H' => 20  // 创建时间
    ];
    
    foreach ($columnWidths as $col => $width) {
        $sheet->getColumnDimension($col)->setWidth($width);
    }
    
    // 设置边框
    $styleArray = [
        'borders' => [
            'allBorders' => [
                'borderStyle' => Border::BORDER_THIN,
                'color' => ['rgb' => '000000'],
            ],
        ],
    ];
    
    // 为整个数据区域添加边框
    $dataStartRow = $currentRow - count($schedules);
    $dataEndRow = $currentRow;
    $sheet->getStyle('A' . $dataStartRow . ':H' . $dataEndRow)->applyFromArray($styleArray);
    
    // 添加统计信息
    $currentRow += 2;
    $sheet->setCellValue('A' . $currentRow, '统计信息：');
    $sheet->getStyle('A' . $currentRow)->getFont()->setBold(true);
    $sheet->getStyle('A' . $currentRow)->getFill()
        ->setFillType(Fill::FILL_SOLID)
        ->getStartColor()->setRGB('E9ECEF');

    // 计算详细统计
    $station1Days = 0;
    $station2Days = 0;
    $totalDays = 0;
    $stationCounts = [];

    foreach ($schedules as $schedule) {
        $station = $schedule['station'];
        $days = intval($schedule['days']);

        // 累计天数
        $totalDays += $days;
        if (strpos($station, '一站') !== false) {
            $station1Days += $days;
        } elseif (strpos($station, '二站') !== false) {
            $station2Days += $days;
        }

        // 累计次数
        if (!isset($stationCounts[$station])) {
            $stationCounts[$station] = 0;
        }
        $stationCounts[$station]++;
    }

    // 显示天数统计
    $currentRow++;
    $sheet->setCellValue('A' . $currentRow, '一站总天数：' . $station1Days . ' 天');
    $sheet->getStyle('A' . $currentRow)->getFill()
        ->setFillType(Fill::FILL_SOLID)
        ->getStartColor()->setRGB('E8F4FD');

    $currentRow++;
    $sheet->setCellValue('A' . $currentRow, '二站总天数：' . $station2Days . ' 天');
    $sheet->getStyle('A' . $currentRow)->getFill()
        ->setFillType(Fill::FILL_SOLID)
        ->getStartColor()->setRGB('E8F5E8');

    $currentRow++;
    $sheet->setCellValue('A' . $currentRow, '总天数：' . $totalDays . ' 天');
    $sheet->getStyle('A' . $currentRow)->getFont()->setBold(true);

    $currentRow++;
    $sheet->setCellValue('A' . $currentRow, '总排班次数：' . count($schedules) . ' 次');
    $sheet->getStyle('A' . $currentRow)->getFont()->setBold(true);

    // 显示班次统计
    $currentRow++;
    $sheet->setCellValue('A' . $currentRow, '班次分布：');
    $sheet->getStyle('A' . $currentRow)->getFont()->setBold(true);

    foreach ($stationCounts as $station => $count) {
        $currentRow++;
        $sheet->setCellValue('A' . $currentRow, '  ' . $station . '：' . $count . ' 次');
    }
    
    // 设置文件名和输出
    $filename = '倒班时间表_' . $year . '年';
    if (!empty($month)) {
        $filename .= $month . '月';
    }
    if (!empty($station)) {
        $filename .= '_' . $station;
    }
    $filename .= '_' . date('Y-m-d') . '.xlsx';
    
    // 记录操作日志
    if (function_exists('logAction')) {
        logAction('导出倒班Excel', 'shift_schedules', [
            'year' => $year,
            'month' => $month,
            'station' => $station,
            'count' => count($schedules)
        ]);
    }
    
    header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Cache-Control: max-age=0');
    
    $writer = new Xlsx($spreadsheet);
    $writer->save('php://output');
    exit;

} catch (Exception $e) {
    http_response_code(500);
    exit('导出失败：' . $e->getMessage());
}
?>
