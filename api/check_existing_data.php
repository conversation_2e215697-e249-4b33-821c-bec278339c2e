<?php
// 检查指定日期是否存在数据
header('Content-Type: application/json; charset=utf-8');

// 使用config.php的数据库配置
require_once '../includes/config.php';

try {
    // 检查请求方法
    if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
        echo json_encode(['success' => false, 'message' => '方法不允许']);
        exit;
    }
    
    // 获取日期参数
    $recordDate = $_GET['record_date'] ?? '';
    
    if (empty($recordDate)) {
        echo json_encode(['success' => false, 'message' => '缺少日期参数']);
        exit;
    }
    
    // 查询该日期的记录数量
    $sql = "SELECT COUNT(*) as count FROM jintie_records WHERE record_date = ?";
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$recordDate]);
    $result = $stmt->fetch();
    
    $count = $result['count'] ?? 0;
    
    echo json_encode([
        'success' => true,
        'exists' => $count > 0,
        'count' => $count,
        'record_date' => $recordDate
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => '检查失败: ' . $e->getMessage()
    ]);
}
?>
