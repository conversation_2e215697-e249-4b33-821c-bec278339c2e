<?php
require '../includes/config.php';
header('Content-Type: application/json');

// 设置时区为北京时间
date_default_timezone_set('Asia/Shanghai');

// 允许跨域请求
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST');
header('Access-Control-Allow-Headers: Content-Type');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// 获取请求参数
$input = json_decode(file_get_contents('php://input'), true);
if (!$input) {
    $input = $_POST;
}

// 创建必要的表结构
createRequiredTables();

// 如果没有action参数，返回错误
if (!isset($input['action'])) {
    echo json_encode(['status' => 'error', 'message' => '缺少action参数']);
    exit;
}

// 根据action参数执行相应的操作
switch ($input['action']) {
    case 'query_rcp':
        queryRcp($input);
        break;
    case 'query_junction':
        queryJunction($input);
        break;
    case 'query_tag':
        queryTag($input);
        break;
    case 'query_valve':
        queryValve($input);
        break;
    case 'login':
        userLogin($input);
        break;
    case 'check_login':
        checkLoginStatus($input);
        break;
    case 'change_password':
        changePassword($input);
        break;
    case 'get_users':
        getUsers($input);
        break;
    case 'add_user':
        addUser($input);
        break;
    case 'reset_password':
        resetPassword($input);
        break;
    case 'change_role':
        changeRole($input);
        break;
    case 'delete_user':
        deleteUser($input);
        break;
    case 'logout':
        handleLogout($input);
        break;
    default:
        echo json_encode(['status' => 'error', 'message' => '无效的action参数']);
        break;
}

// 获取客户端IP地址
function getClientIP() {
    if (!empty($_SERVER['HTTP_CLIENT_IP'])) {
        return $_SERVER['HTTP_CLIENT_IP'];
    } elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
        return $_SERVER['HTTP_X_FORWARDED_FOR'];
    } else {
        return $_SERVER['REMOTE_ADDR'];
    }
}

// 创建必要的数据表
function createRequiredTables() {
    global $pdo;
    
    try {
        // 记录当前服务器时间
        $serverTime = date('Y-m-d H:i:s');
        error_log("API 调用时间: " . $serverTime . " [时区: " . date_default_timezone_get() . "]");
        
        // 获取数据库时间
        $stmt = $pdo->query("SELECT NOW() as db_time");
        $dbTimeRow = $stmt->fetch(PDO::FETCH_ASSOC);
        if ($dbTimeRow) {
            error_log("数据库时间: " . $dbTimeRow['db_time']);
        }
        
        // 创建登录尝试表
        $pdo->exec("CREATE TABLE IF NOT EXISTS login_attempts (
            id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(255) NOT NULL,
            ip_address VARCHAR(45) NOT NULL,
            attempt_time DATETIME NOT NULL,
            is_success TINYINT(1) DEFAULT 0,
            module VARCHAR(50) DEFAULT 'tag_query',
            INDEX idx_username_time (username, attempt_time),
            INDEX idx_ip_time (ip_address, attempt_time)
        )");
    } catch (PDOException $e) {
        error_log("创建表失败: " . $e->getMessage());
    }
}

// 检查登录尝试次数，如果超过限制则锁定
function checkLoginAttempts($username) {
    global $pdo;
    
    $ip = getClientIP();
    $lockoutTime = 3600; // 1小时锁定时间（秒）
    $maxAttempts = 5; // 最大尝试次数
    
    try {
        // 先验证表是否存在
        $pdo->exec("CREATE TABLE IF NOT EXISTS login_attempts (
            id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(255) NOT NULL,
            ip_address VARCHAR(45) NOT NULL,
            attempt_time DATETIME NOT NULL,
            is_success TINYINT(1) DEFAULT 0,
            module VARCHAR(50) DEFAULT 'tag_query',
            INDEX idx_username_time (username, attempt_time),
            INDEX idx_ip_time (ip_address, attempt_time)
        )");
        
        // 使用数据库的时间函数，检查过去一小时内的失败尝试
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM login_attempts 
                              WHERE username = ? 
                              AND is_success = 0 
                              AND attempt_time > DATE_SUB(NOW(), INTERVAL 1 HOUR)");
        $stmt->execute([$username]);
        $userAttempts = $stmt->fetchColumn();
        
        // 检查IP锁定，同样使用数据库的时间函数
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM login_attempts 
                              WHERE ip_address = ? 
                              AND is_success = 0 
                              AND attempt_time > DATE_SUB(NOW(), INTERVAL 1 HOUR)");
        $stmt->execute([$ip]);
        $ipAttempts = $stmt->fetchColumn();
        
        if ($userAttempts >= $maxAttempts) {
            $remainingTime = getRemainingLockTime($username, 'username');
            return [
                'locked' => true, 
                'message' => "账号已锁定，请{$remainingTime}分钟后再试"
            ];
        }
        
        if ($ipAttempts >= $maxAttempts) {
            $remainingTime = getRemainingLockTime($ip, 'ip_address');
            return [
                'locked' => true, 
                'message' => "IP已锁定，请{$remainingTime}分钟后再试"
            ];
        }
        
        return ['locked' => false];
    } catch (PDOException $e) {
        // 如果出错，为了安全不锁定用户
        error_log("Login attempts check error: " . $e->getMessage());
        return ['locked' => false];
    }
}

// 记录登录尝试
function recordLoginAttempt($username, $isSuccess = false) {
    global $pdo;
    
    $ip = getClientIP();
    
    try {
        // 先验证表是否存在
        $pdo->exec("CREATE TABLE IF NOT EXISTS login_attempts (
            id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(255) NOT NULL,
            ip_address VARCHAR(45) NOT NULL,
            attempt_time DATETIME NOT NULL,
            is_success TINYINT(1) DEFAULT 0,
            module VARCHAR(50) DEFAULT 'tag_query',
            INDEX idx_username_time (username, attempt_time),
            INDEX idx_ip_time (ip_address, attempt_time)
        )");
        
        // 使用数据库自身的NOW()函数，而不是PHP的时间函数
        $stmt = $pdo->prepare("INSERT INTO login_attempts (username, ip_address, attempt_time, is_success, module)
                              VALUES (?, ?, NOW(), ?, 'tag_query')");
        $stmt->execute([$username, $ip, $isSuccess ? 1 : 0]);
        return true;
    } catch (PDOException $e) {
        error_log("Record login attempt error: " . $e->getMessage());
        return false;
    }
}

// 重置登录尝试次数
function resetLoginAttempts($username) {
    global $pdo;
    
    $ip = getClientIP();
    
    try {
        // 先验证表是否存在
        $pdo->exec("CREATE TABLE IF NOT EXISTS login_attempts (
            id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(255) NOT NULL,
            ip_address VARCHAR(45) NOT NULL,
            attempt_time DATETIME NOT NULL,
            is_success TINYINT(1) DEFAULT 0,
            module VARCHAR(50) DEFAULT 'tag_query',
            INDEX idx_username_time (username, attempt_time),
            INDEX idx_ip_time (ip_address, attempt_time)
        )");
        
        // 更新登录尝试状态
        $stmt = $pdo->prepare("UPDATE login_attempts 
                                SET is_success = 1 
                                WHERE username = ? OR ip_address = ?");
        $stmt->execute([$username, $ip]);
        return true;
    } catch (PDOException $e) {
        error_log("Reset login attempts error: " . $e->getMessage());
        return false;
    }
}

// 获取剩余锁定时间（分钟）
function getRemainingLockTime($value, $type = 'username') {
    global $pdo;
    
    $lockoutTime = 3600; // 1小时锁定时间（秒）
    
    try {
        // 先验证表是否存在
        $pdo->exec("CREATE TABLE IF NOT EXISTS login_attempts (
            id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(255) NOT NULL,
            ip_address VARCHAR(45) NOT NULL,
            attempt_time DATETIME NOT NULL,
            is_success TINYINT(1) DEFAULT 0,
            module VARCHAR(50) DEFAULT 'tag_query',
            INDEX idx_username_time (username, attempt_time),
            INDEX idx_ip_time (ip_address, attempt_time)
        )");
        
        $column = ($type == 'username') ? 'username' : 'ip_address';
        
        // 查询最早的失败尝试记录，使用数据库时间函数
        $stmt = $pdo->prepare("SELECT 
                                MIN(attempt_time) as oldest_attempt,
                                TIMESTAMPDIFF(SECOND, NOW(), DATE_ADD(MIN(attempt_time), INTERVAL 1 HOUR)) as remaining_seconds
                              FROM login_attempts 
                              WHERE {$column} = ? 
                              AND is_success = 0 
                              AND attempt_time > DATE_SUB(NOW(), INTERVAL 1 HOUR)");
        $stmt->execute([$value]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        // 如果有结果且剩余时间大于0
        if ($result && $result['remaining_seconds'] > 0) {
            // 转换为分钟并向上取整
            return ceil(abs($result['remaining_seconds']) / 60); 
        }
        
        return 60; // 默认返回60分钟
    } catch (PDOException $e) {
        error_log("Get remaining lock time error: " . $e->getMessage());
        return 60; // 出错时默认返回60分钟
    }
}

// 查询RCP柜信息
function queryRcp($input) {
    global $pdo;
    
    if (!isset($input['name']) || empty($input['name'])) {
        echo json_encode(['status' => 'error', 'message' => '缺少RCP柜名称']);
        exit;
    }
    
    $name = trim($input['name']);
    
    try {
        // 查询所有匹配的RCP柜
        $stmt = $pdo->prepare("
            SELECT r.*, p.name AS power_name 
            FROM rcp_cabinet r
            JOIN power_cabinet p ON r.power_id = p.id
            WHERE r.name LIKE ?
            ORDER BY r.name ASC
        ");
        $stmt->execute(["%$name%"]);
        $rcps = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (empty($rcps)) {
            echo json_encode(['status' => 'error', 'message' => '未找到匹配的RCP柜']);
            exit;
        }
        
        $results = [];
        
        // 处理每个RCP柜的数据
        foreach ($rcps as $rcp) {
            // 查询RCP柜下的接线箱
            $stmt = $pdo->prepare("
                SELECT * FROM junction_box
                WHERE rcp_id = ?
                ORDER BY name ASC
            ");
            $stmt->execute([$rcp['id']]);
            $junctionBoxes = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // 查询RCP柜下的位号（不属于任何接线箱的位号）
            $stmt = $pdo->prepare("
                SELECT * FROM terminal
                WHERE rcp_id = ? AND (junction_id IS NULL OR junction_id = 0)
                ORDER BY code ASC
            ");
            $stmt->execute([$rcp['id']]);
            $terminals = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // 组装结果
            $results[] = [
                'id' => $rcp['id'],
                'name' => $rcp['name'],
                'power_name' => $rcp['power_name'],
                'location' => $rcp['location'],
                'remark' => $rcp['remark'],
                'junctionBoxes' => $junctionBoxes,
                'terminals' => $terminals
            ];
        }
        
        echo json_encode(['status' => 'success', 'data' => $results]);
    } catch (PDOException $e) {
        echo json_encode(['status' => 'error', 'message' => '数据库错误: ' . $e->getMessage()]);
    }
}

// 查询接线箱信息
function queryJunction($input) {
    global $pdo;
    
    if (!isset($input['name']) || empty($input['name'])) {
        echo json_encode(['status' => 'error', 'message' => '缺少接线箱名称']);
        exit;
    }
    
    $name = trim($input['name']);
    
    try {
        // 查询所有匹配的接线箱
        $stmt = $pdo->prepare("
            SELECT j.*, r.name AS rcp_name, p.name AS power_name
            FROM junction_box j
            JOIN rcp_cabinet r ON j.rcp_id = r.id
            JOIN power_cabinet p ON r.power_id = p.id
            WHERE j.name LIKE ?
            ORDER BY j.name ASC
        ");
        $stmt->execute(["%$name%"]);
        $junctions = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (empty($junctions)) {
            echo json_encode(['status' => 'error', 'message' => '未找到匹配的接线箱']);
            exit;
        }
        
        $results = [];
        
        // 处理每个接线箱的数据
        foreach ($junctions as $junction) {
            // 查询接线箱下的位号
            $stmt = $pdo->prepare("
                SELECT * FROM terminal
                WHERE junction_id = ?
                ORDER BY code ASC
            ");
            $stmt->execute([$junction['id']]);
            $terminals = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // 组装结果
            $results[] = [
                'id' => $junction['id'],
                'name' => $junction['name'],
                'rcp_id' => $junction['rcp_id'],
                'rcp_name' => $junction['rcp_name'],
                'power_name' => $junction['power_name'],
                'remark' => $junction['remark'],
                'terminals' => $terminals
            ];
        }
        
        echo json_encode(['status' => 'success', 'data' => $results]);
    } catch (PDOException $e) {
        echo json_encode(['status' => 'error', 'message' => '数据库错误: ' . $e->getMessage()]);
    }
}

// 查询位号信息
function queryTag($input) {
    global $pdo;
    
    if (!isset($input['info']) || empty($input['info'])) {
        echo json_encode(['status' => 'error', 'message' => '缺少位号信息']);
        exit;
    }
    
    $info = trim($input['info']);
    
    try {
        // 支持多个关键词搜索（按空格分隔）
        $keywords = explode(' ', $info);
        $conditions = [];
        $params = [];

        foreach ($keywords as $keyword) {
            $keyword = trim($keyword);
            if (!empty($keyword)) {
                $conditions[] = "(t.code LIKE ? OR t.cable_name LIKE ?)";
                $params[] = "%$keyword%";
                $params[] = "%$keyword%";
            }
        }

        $whereClause = count($conditions) > 0 ? implode(' AND ', $conditions) : "1=1";
        
        // 修复查询，确保只使用表中存在的列
        $query = "
            SELECT t.id, t.code, t.cable_name, t.rcp_id, t.junction_id, t.remark,
                   r.name AS rcp_name, j.name AS junction_name
            FROM terminal t
            JOIN rcp_cabinet r ON t.rcp_id = r.id
            LEFT JOIN junction_box j ON t.junction_id = j.id
            WHERE $whereClause
            ORDER BY t.code ASC
            LIMIT 100
        ";
        
        $stmt = $pdo->prepare($query);
        $stmt->execute($params);
        $tags = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (empty($tags)) {
            echo json_encode(['status' => 'error', 'message' => '未找到匹配的位号']);
            exit;
        }
        
        echo json_encode(['status' => 'success', 'data' => $tags]);
    } catch (PDOException $e) {
        echo json_encode(['status' => 'error', 'message' => '数据库错误: ' . $e->getMessage()]);
    }
}

// 用户登录
function userLogin($input) {
    global $pdo;
    
    if (!isset($input['username']) || !isset($input['password'])) {
        echo json_encode(['status' => 'error', 'message' => '用户名和密码不能为空']);
        exit;
    }
    
    $username = trim($input['username']);
    $password = trim($input['password']);
    
    try {
        // 检查是否被锁定
        $lockStatus = checkLoginAttempts($username);
        if ($lockStatus['locked']) {
            echo json_encode(['status' => 'error', 'message' => $lockStatus['message']]);
            recordLoginAttempt($username, false); // 记录失败尝试
            exit;
        }
        
        // 验证系统用户表中的登录信息
        $stmt = $pdo->prepare("SELECT * FROM users WHERE username = ?");
        $stmt->execute([$username]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($user && (password_verify($password, $user['password']) || $password === $user['password'])) {
            // 确保获取用户角色信息
            $role = isset($user['role']) ? $user['role'] : 'user';
            
            // 登录成功，重置失败尝试记录
            $resetSuccess = resetLoginAttempts($username);
            $recordSuccess = recordLoginAttempt($username, true); // 记录成功尝试

            // 记录操作日志
            if (function_exists('logAction')) {
                // 临时设置SESSION以便logAction能获取到用户ID
                $_SESSION['user'] = ['id' => $user['id'], 'username' => $user['username']];
                logAction('用户登录', 'login', ['username' => $username]);
                // 清除临时SESSION（API接口不需要保持SESSION）
                unset($_SESSION['user']);
            }

            if (!$resetSuccess || !$recordSuccess) {
                error_log("登录成功但记录尝试失败，用户: $username");
            }
            
            // 登录成功，返回用户信息，包括角色信息
            echo json_encode([
                'status' => 'success',
                'data' => [
                    'token' => md5($username . time()), // 生成一个临时token
                    'username' => $user['username'],
                    'role' => $role,
                    'expire_time' => date('Y-m-d H:i:s', strtotime('+7 days'))
                ]
            ]);
        } else {
            // 记录失败尝试
            $recordSuccess = recordLoginAttempt($username, false);
            
            if (!$recordSuccess) {
                // 如果无法记录尝试，只返回基本错误信息
                echo json_encode(['status' => 'error', 'message' => "用户名或密码错误"]);
                exit;
            }
            
            try {
                // 使用数据库时间函数计算失败尝试次数
                $failedAttempts = $pdo->prepare("SELECT COUNT(*) FROM login_attempts 
                                               WHERE username = ? 
                                               AND is_success = 0 
                                               AND attempt_time > DATE_SUB(NOW(), INTERVAL 1 HOUR)");
                $failedAttempts->execute([$username]);
                $attemptCount = $failedAttempts->fetchColumn();
                $remainingAttempts = 5 - $attemptCount;
                
                if ($remainingAttempts <= 0) {
                    echo json_encode(['status' => 'error', 'message' => '登录失败次数过多，账号已锁定1小时']);
                } else {
                    echo json_encode(['status' => 'error', 'message' => "用户名或密码错误，还剩{$remainingAttempts}次尝试机会"]);
                }
            } catch (PDOException $e) {
                // 如果获取尝试次数失败，返回一般性错误
                error_log("获取登录尝试次数失败: " . $e->getMessage());
                echo json_encode(['status' => 'error', 'message' => '用户名或密码错误']);
            }
        }
    } catch (PDOException $e) {
        error_log("登录数据库错误: " . $e->getMessage());
        echo json_encode(['status' => 'error', 'message' => '登录服务暂时不可用，请稍后再试']);
    }
}

// 检查登录状态
function checkLoginStatus($input) {
    // 简化逻辑，只要有token就认为是登录状态
    if (!isset($input['token'])) {
        echo json_encode(['status' => 'error', 'message' => '未登录']);
        exit;
    }
    
    // 返回成功状态，不验证token
    echo json_encode([
        'status' => 'success',
        'data' => [
            'username' => isset($input['username']) ? $input['username'] : '用户',
            'expire_time' => date('Y-m-d H:i:s', strtotime('+7 days'))
        ]
    ]);
}

// 修改密码
function changePassword($input) {
    global $pdo;
    
    // 检查参数
    if (!isset($input['username']) || !isset($input['old_password']) || !isset($input['new_password'])) {
        echo json_encode(['status' => 'error', 'message' => '缺少必要参数']);
        exit;
    }
    
    $username = trim($input['username']);
    $oldPassword = trim($input['old_password']);
    $newPassword = trim($input['new_password']);
    
    // 验证新密码长度
    if (strlen($newPassword) < 6) {
        echo json_encode(['status' => 'error', 'message' => '新密码长度不能少于6位']);
        exit;
    }
    
    try {
        // 先验证旧密码是否正确
        $stmt = $pdo->prepare("SELECT * FROM users WHERE username = ?");
        $stmt->execute([$username]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$user) {
            echo json_encode(['status' => 'error', 'message' => '用户不存在']);
            exit;
        }
        
        if (!(password_verify($oldPassword, $user['password']) || $oldPassword === $user['password'])) {
            echo json_encode(['status' => 'error', 'message' => '原密码错误']);
            exit;
        }
        
        // 更新密码（统一使用哈希存储）
        $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("UPDATE users SET password = ? WHERE username = ?");
        $stmt->execute([$hashedPassword, $username]);

        // 记录操作日志
        if (function_exists('logAction')) {
            // 临时设置SESSION以便logAction能获取到用户ID
            $_SESSION['user'] = ['id' => $user['id'], 'username' => $user['username']];
            logAction('修改密码', 'users', ['username' => $username]);
            unset($_SESSION['user']);
        }

        echo json_encode(['status' => 'success', 'message' => '密码修改成功']);
    } catch (PDOException $e) {
        echo json_encode(['status' => 'error', 'message' => '数据库错误: ' . $e->getMessage()]);
    }
}

// 获取用户列表
function getUsers($input) {
    global $pdo;
    
    // 简单的安全检查，确保有token
    if (!isset($input['token'])) {
        echo json_encode(['status' => 'error', 'message' => '未登录']);
        exit;
    }
    
    $keyword = isset($input['keyword']) ? trim($input['keyword']) : '';
    
    try {
        // 构建查询
        $sql = "SELECT id, username, role FROM users";
        $params = [];
        
        if (!empty($keyword)) {
            $sql .= " WHERE username LIKE ?";
            $params[] = "%$keyword%";
        }
        
        $sql .= " ORDER BY id DESC";
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo json_encode(['status' => 'success', 'data' => $users]);
    } catch (PDOException $e) {
        echo json_encode(['status' => 'error', 'message' => '数据库错误: ' . $e->getMessage()]);
    }
}

// 添加用户
function addUser($input) {
    global $pdo;
    
    // 参数检查
    if (!isset($input['token']) || !isset($input['username']) || !isset($input['password'])) {
        echo json_encode(['status' => 'error', 'message' => '参数不完整']);
        exit;
    }
    
    $username = trim($input['username']);
    $password = trim($input['password']);
    $role = isset($input['role']) ? trim($input['role']) : 'user';
    
    // 验证
    if (strlen($username) < 2) {
        echo json_encode(['status' => 'error', 'message' => '用户名长度不能少于2位']);
        exit;
    }
    
    if (strlen($password) < 6) {
        echo json_encode(['status' => 'error', 'message' => '密码长度不能少于6位']);
        exit;
    }
    
    // 允许三种角色: admin(管理员), manager(普通管理员), user(普通用户)
    if (!in_array($role, ['admin', 'manager', 'user'])) {
        $role = 'user'; // 默认为普通用户
    }
    
    try {
        // 检查用户名是否已存在
        $stmt = $pdo->prepare("SELECT id FROM users WHERE username = ?");
        $stmt->execute([$username]);
        if ($stmt->fetch()) {
            echo json_encode(['status' => 'error', 'message' => '用户名已存在']);
            exit;
        }
        
        // 创建用户（使用哈希密码存储）
        $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("INSERT INTO users (username, password, role) VALUES (?, ?, ?)");
        $stmt->execute([
            $username,
            $hashedPassword,
            $role
        ]);

        // 获取新创建的用户ID
        $newUserId = $pdo->lastInsertId();

        // 记录操作日志
        if (function_exists('logAction')) {
            // 临时设置SESSION以便logAction能获取到用户ID
            $_SESSION['user'] = ['id' => 1, 'username' => 'system']; // 使用系统用户记录
            logAction('创建新用户', 'users', ['username' => $username, 'role' => $role, 'new_user_id' => $newUserId]);
            unset($_SESSION['user']);
        }

        echo json_encode(['status' => 'success', 'message' => '用户添加成功']);
    } catch (PDOException $e) {
        echo json_encode(['status' => 'error', 'message' => '数据库错误: ' . $e->getMessage()]);
    }
}

// 重置密码
function resetPassword($input) {
    global $pdo;
    
    // 参数检查
    if (!isset($input['token']) || !isset($input['user_id'])) {
        echo json_encode(['status' => 'error', 'message' => '参数不完整']);
        exit;
    }
    
    $userId = intval($input['user_id']);
    $defaultPassword = '000000';
    
    try {
        // 检查用户是否存在
        $stmt = $pdo->prepare("SELECT id FROM users WHERE id = ?");
        $stmt->execute([$userId]);
        if (!$stmt->fetch()) {
            echo json_encode(['status' => 'error', 'message' => '用户不存在']);
            exit;
        }
        
        // 重置密码（使用哈希存储）
        $hashedPassword = password_hash($defaultPassword, PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("UPDATE users SET password = ? WHERE id = ?");
        $stmt->execute([$hashedPassword, $userId]);

        // 记录操作日志
        if (function_exists('logAction')) {
            // 临时设置SESSION以便logAction能获取到用户ID
            $_SESSION['user'] = ['id' => 1, 'username' => 'system']; // 使用系统用户记录
            logAction('重置用户密码', 'users', ['user_id' => $userId]);
            unset($_SESSION['user']);
        }

        echo json_encode(['status' => 'success', 'message' => '密码重置成功']);
    } catch (PDOException $e) {
        echo json_encode(['status' => 'error', 'message' => '数据库错误: ' . $e->getMessage()]);
    }
}

// 修改角色
function changeRole($input) {
    global $pdo;
    
    // 参数检查
    if (!isset($input['token']) || !isset($input['user_id']) || !isset($input['role'])) {
        echo json_encode(['status' => 'error', 'message' => '参数不完整']);
        exit;
    }
    
    $userId = intval($input['user_id']);
    $role = trim($input['role']);
    
    // 允许三种角色: admin(管理员), manager(普通管理员), user(普通用户)
    if (!in_array($role, ['admin', 'manager', 'user'])) {
        echo json_encode(['status' => 'error', 'message' => '无效的角色']);
        exit;
    }
    
    try {
        // 检查用户是否存在
        $stmt = $pdo->prepare("SELECT id FROM users WHERE id = ?");
        $stmt->execute([$userId]);
        if (!$stmt->fetch()) {
            echo json_encode(['status' => 'error', 'message' => '用户不存在']);
            exit;
        }
        
        // 修改角色
        $stmt = $pdo->prepare("UPDATE users SET role = ? WHERE id = ?");
        $stmt->execute([$role, $userId]);

        // 记录操作日志
        if (function_exists('logAction')) {
            // 临时设置SESSION以便logAction能获取到用户ID
            $_SESSION['user'] = ['id' => 1, 'username' => 'system']; // 使用系统用户记录
            logAction('修改用户角色', 'users', ['user_id' => $userId, 'new_role' => $role]);
            unset($_SESSION['user']);
        }

        echo json_encode(['status' => 'success', 'message' => '角色修改成功']);
    } catch (PDOException $e) {
        echo json_encode(['status' => 'error', 'message' => '数据库错误: ' . $e->getMessage()]);
    }
}

// 删除用户
function deleteUser($input) {
    global $pdo;
    
    // 参数检查
    if (!isset($input['token']) || !isset($input['user_id'])) {
        echo json_encode(['status' => 'error', 'message' => '参数不完整']);
        exit;
    }
    
    $userId = intval($input['user_id']);
    
    try {
        // 检查用户是否存在
        $stmt = $pdo->prepare("SELECT id FROM users WHERE id = ?");
        $stmt->execute([$userId]);
        if (!$stmt->fetch()) {
            echo json_encode(['status' => 'error', 'message' => '用户不存在']);
            exit;
        }
        
        // 删除用户
        $stmt = $pdo->prepare("DELETE FROM users WHERE id = ?");
        $stmt->execute([$userId]);

        // 记录操作日志
        if (function_exists('logAction')) {
            // 临时设置SESSION以便logAction能获取到用户ID
            $_SESSION['user'] = ['id' => 1, 'username' => 'system']; // 使用系统用户记录
            logAction('删除用户', 'users', ['user_id' => $userId]);
            unset($_SESSION['user']);
        }

        echo json_encode(['status' => 'success', 'message' => '用户删除成功']);
    } catch (PDOException $e) {
        echo json_encode(['status' => 'error', 'message' => '数据库错误: ' . $e->getMessage()]);
    }
}

/**
 * 查询阀门信息
 * 
 * @param array $input 传入参数
 */
function queryValve($input) {
    global $pdo;
    
    // 检查参数是否完整
    if (!isset($input['valve_info']) || empty($input['valve_info'])) {
        echo json_encode([
            'status' => 'error',
            'message' => '缺少阀门信息'
        ]);
        exit;
    }
    
    // 检查是否有token和用户名
    if (!isset($input['token']) || empty($input['token']) || !isset($input['username']) || empty($input['username'])) {
        echo json_encode([
            'status' => 'error',
            'message' => '缺少token或用户名'
        ]);
        exit;
    }
    
    // 获取token和用户名
    $token = $input['token'];
    $username = $input['username'];
    
    // 验证令牌（这里使用简单验证）
    // 注意：如果实际checkLoginToken函数不存在，需要替换为其他验证方式
    if (function_exists('checkLoginToken')) {
        $validToken = checkLoginToken($token, $username);
        if (!$validToken) {
            echo json_encode([
                'status' => 'error',
                'message' => '登录验证失败'
            ]);
            exit;
        }
    }
    
    // 获取阀门信息
    $valve_info = trim($input['valve_info']);
    
    // 解析阀门信息为关键词
    $keywords = explode(' ', $valve_info);
    $search_terms = [];
    
    foreach ($keywords as $keyword) {
        if (trim($keyword) !== '') {
            $search_terms[] = trim($keyword);
        }
    }
    
    if (empty($search_terms)) {
        echo json_encode([
            'status' => 'error',
            'message' => '没有有效的阀门信息关键词'
        ]);
        exit;
    }
    
    try {
        // 构建查询条件
        $conditions = [];
        $params = [];
        
        foreach ($search_terms as $term) {
            // 使用问号占位符而不是命名参数
            $conditions[] = "(serial_number LIKE ? OR valve_name LIKE ?)";
            // 每个条件需要两个参数值
            $params[] = "%$term%";  // 用于 serial_number LIKE ?
            $params[] = "%$term%";  // 用于 valve_name LIKE ?
        }
        
        // 构建完整的SQL查询
        $sql = "SELECT * FROM valve WHERE " . implode(" AND ", $conditions);
        
        // 执行查询，直接传递参数数组
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        
        // 获取查询结果
        $valves = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // 记录用户操作（如果函数存在）
        if (function_exists('logUserAction')) {
            logUserAction($username, 'query_valve', $valve_info, count($valves) > 0 ? 'success' : 'no_result');
        }
        
        if (count($valves) > 0) {
            // 格式化结果为数组
            $formatted_valves = [];
            foreach ($valves as $valve) {
                // 移除不需要展示的字段
                if (isset($valve['create_time'])) unset($valve['create_time']);
                if (isset($valve['update_time'])) unset($valve['update_time']);
                
                $formatted_valve = [
                    'valve_no' => $valve['serial_number'],
                    'name' => $valve['valve_name'],
                    'platform_name' => isset($valve['platform_name']) ? $valve['platform_name'] : '',
                    'type' => isset($valve['valve_type']) ? $valve['valve_type'] : '',
                    'location' => isset($valve['location']) ? $valve['location'] : '',
                    'flow_type' => isset($valve['flow_type']) ? $valve['flow_type'] : '',
                    'spec' => isset($valve['valve_size']) ? $valve['valve_size'] : '',
                    'flange_type' => isset($valve['flange_type']) ? $valve['flange_type'] : '',
                    'flange_holes' => isset($valve['flange_holes']) ? $valve['flange_holes'] : '',
                    'seal_type' => isset($valve['seal_type']) ? $valve['seal_type'] : '',
                    'gasket_type' => isset($valve['gasket_type']) ? $valve['gasket_type'] : '',
                    'operation_date' => isset($valve['operation_date']) ? $valve['operation_date'] : '',
                    'remark' => isset($valve['remark']) ? $valve['remark'] : ''
                ];
                $formatted_valves[] = $formatted_valve;
            }
            
            echo json_encode([
                'status' => 'success',
                'data' => $formatted_valves
            ]);
        } else {
            echo json_encode([
                'status' => 'success',
                'data' => [],
                'message' => '没有匹配的阀门信息'
            ]);
        }
    } catch (PDOException $e) {
        // 记录错误（如果函数存在）
        if (function_exists('logError')) {
            logError('query_valve', $e->getMessage());
        }
        echo json_encode([
            'status' => 'error',
            'message' => '数据库查询错误: ' . $e->getMessage()
        ]);
    }
}

/**
 * 处理退出登录
 */
function handleLogout($input) {
    try {
        // 验证基本参数
        if (!isset($input['username'])) {
            echo json_encode(['status' => 'error', 'message' => '缺少用户名参数']);
            exit;
        }

        $username = $input['username'];

        // 查询用户信息以获取用户ID
        global $pdo;
        $stmt = $pdo->prepare("SELECT id, username FROM users WHERE username = ?");
        $stmt->execute([$username]);
        $user = $stmt->fetch();

        if ($user) {
            // 记录操作日志
            if (function_exists('logAction')) {
                // 临时设置SESSION以便logAction能获取到用户ID
                $_SESSION['user'] = ['id' => $user['id'], 'username' => $user['username']];
                logAction('用户退出', 'logout', ['username' => $username]);
                // 清除临时SESSION
                unset($_SESSION['user']);
            }
        }

        echo json_encode(['status' => 'success', 'message' => '退出成功']);

    } catch (Exception $e) {
        echo json_encode(['status' => 'error', 'message' => '退出处理失败']);
    }
}