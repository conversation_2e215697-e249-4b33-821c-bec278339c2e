<?php
/**
 * 腾讯云配置管理API
 */

require_once '../includes/config.php';

// 检查用户权限
if (!isset($_SESSION['user']) || $_SESSION['user']['role'] !== 'admin') {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => '无权限访问']);
    exit;
}

// 引入腾讯云配置管理器
require_once '../includes/TencentConfigManager.php';
$configManager = new TencentConfigManager($pdo);

// 设置响应头
header('Content-Type: application/json; charset=utf-8');

$action = $_GET['action'] ?? $_POST['action'] ?? '';

try {
    switch ($action) {
        case 'get_config':
            // 获取当前配置
            $configs = $configManager->getAllConfigs();
            $result = [];
            foreach ($configs as $config) {
                $result[$config['config_key']] = [
                    'value' => $config['config_value'],
                    'description' => $config['config_desc'],
                    'is_encrypted' => $config['is_encrypted'],
                    'updated_at' => $config['updated_at']
                ];
            }
            echo json_encode(['success' => true, 'data' => $result]);
            break;
            
        case 'update_config':
            // 更新配置
            $secretId = trim($_POST['secret_id'] ?? '');
            $secretKey = trim($_POST['secret_key'] ?? '');
            $region = trim($_POST['region'] ?? '');
            
            // 验证输入
            if (empty($secretId) || empty($secretKey) || empty($region)) {
                echo json_encode(['success' => false, 'message' => '所有配置项都不能为空']);
                break;
            }
            
            // 验证地域格式
            $validRegions = ['ap-beijing', 'ap-shanghai', 'ap-guangzhou', 'ap-chengdu', 'ap-hongkong', 'ap-singapore'];
            if (!in_array($region, $validRegions)) {
                echo json_encode(['success' => false, 'message' => '无效的服务地域']);
                break;
            }
            
            // 更新配置
            $success = true;
            $success &= $configManager->setConfig('secret_id', $secretId, '腾讯云API密钥ID', true);
            $success &= $configManager->setConfig('secret_key', $secretKey, '腾讯云API密钥Key', true);
            $success &= $configManager->setConfig('region', $region, '腾讯云服务地域', false);
            
            if ($success) {
                // 记录操作日志
                logAction('更新配置', '腾讯云配置', [
                    'region' => $region,
                    'secret_id_length' => strlen($secretId)
                ]);
                
                echo json_encode(['success' => true, 'message' => '配置更新成功']);
            } else {
                echo json_encode(['success' => false, 'message' => '配置更新失败']);
            }
            break;
            
        case 'test_config':
            // 测试配置
            $testResult = $configManager->testConfig();
            echo json_encode($testResult);
            break;
            
        case 'reset_config':
            // 重置为默认配置
            $success = true;
            $success &= $configManager->setConfig('secret_id', '', '腾讯云API密钥ID', true);
            $success &= $configManager->setConfig('secret_key', '', '腾讯云API密钥Key', true);
            $success &= $configManager->setConfig('region', 'ap-beijing', '腾讯云服务地域', false);
            
            if ($success) {
                logAction('重置配置', '腾讯云配置', []);
                echo json_encode(['success' => true, 'message' => '配置已重置为默认值']);
            } else {
                echo json_encode(['success' => false, 'message' => '配置重置失败']);
            }
            break;
            
        case 'get_regions':
            // 获取可用地域列表
            $regions = [
                'ap-beijing' => '北京',
                'ap-shanghai' => '上海', 
                'ap-guangzhou' => '广州',
                'ap-chengdu' => '成都',
                'ap-hongkong' => '香港',
                'ap-singapore' => '新加坡'
            ];
            echo json_encode(['success' => true, 'data' => $regions]);
            break;
            
        default:
            echo json_encode(['success' => false, 'message' => '无效的操作']);
            break;
    }
    
} catch (Exception $e) {
    error_log("腾讯云配置API错误: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => '服务器内部错误']);
}
?>
