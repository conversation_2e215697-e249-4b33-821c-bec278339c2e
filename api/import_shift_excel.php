<?php
// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 设置正确的路径
$rootPath = dirname(__DIR__); // 获取上级目录 (zdh)
require_once $rootPath . '/includes/config.php';

// 加载vendor autoload
$vendorPath = dirname($rootPath) . '/vendor/autoload.php';

if (!file_exists($vendorPath)) {
    http_response_code(500);
    exit(json_encode(['status' => 'error', 'message' => 'PhpSpreadsheet库未安装，请联系管理员']));
}

try {
    require_once $vendorPath;
} catch (Exception $e) {
    http_response_code(500);
    exit(json_encode(['status' => 'error', 'message' => '加载PhpSpreadsheet库失败：' . $e->getMessage()]));
}

use PhpOffice\PhpSpreadsheet\IOFactory;

// 验证PhpSpreadsheet是否正确加载
if (!class_exists('PhpOffice\PhpSpreadsheet\IOFactory')) {
    http_response_code(500);
    exit(json_encode(['status' => 'error', 'message' => 'PhpSpreadsheet库加载失败']));
}

// 检查用户权限
if (!isset($_SESSION['user'])) {
    http_response_code(403);
    exit(json_encode(['status' => 'error', 'message' => '未授权访问']));
}

$user = $_SESSION['user'];
$hasPermission = $user['role'] === 'admin';

if (!$hasPermission) {
    http_response_code(403);
    exit(json_encode(['status' => 'error', 'message' => '权限不足，只有管理员可以导入数据']));
}

header('Content-Type: application/json; charset=utf-8');

// 日期转换函数
function convertExcelDate($dateValue) {
    // 如果已经是DateTime对象
    if ($dateValue instanceof DateTime) {
        return $dateValue->format('Y-m-d');
    }

    // 如果是空值
    if (empty($dateValue) || $dateValue === null) {
        return null;
    }

    // 如果是数字（Excel日期序列号）
    if (is_numeric($dateValue)) {
        try {
            // 使用PhpSpreadsheet的日期转换
            $dateObj = \PhpOffice\PhpSpreadsheet\Shared\Date::excelToDateTimeObject($dateValue);
            return $dateObj->format('Y-m-d');
        } catch (Exception $e) {
            // 如果转换失败，返回原值
            return $dateValue;
        }
    }

    // 如果是字符串，尝试多种格式
    $dateString = trim($dateValue);

    // 支持的日期格式
    $formats = [
        'Y-m-d',     // 2025-01-01
        'Y/m/d',     // 2025/01/01
        'Y-n-j',     // 2025-1-1
        'Y/n/j',     // 2025/1/1
        'd/m/Y',     // 01/01/2025
        'd-m-Y',     // 01-01-2025
        'm/d/Y',     // 01/01/2025
        'm-d-Y'      // 01-01-2025
    ];

    foreach ($formats as $format) {
        $dateObj = DateTime::createFromFormat($format, $dateString);
        if ($dateObj !== false) {
            return $dateObj->format('Y-m-d');
        }
    }

    // 最后尝试strtotime
    $timestamp = strtotime($dateString);
    if ($timestamp !== false) {
        return date('Y-m-d', $timestamp);
    }

    // 如果都失败了，返回原值
    return $dateValue;
}

try {
    // 检查是否有文件上传
    if (!isset($_FILES['excel_file'])) {
        throw new Exception('没有接收到上传文件');
    }

    $uploadedFile = $_FILES['excel_file'];

    // 详细检查上传错误
    switch ($uploadedFile['error']) {
        case UPLOAD_ERR_OK:
            break;
        case UPLOAD_ERR_NO_FILE:
            throw new Exception('请选择要导入的Excel文件');
        case UPLOAD_ERR_INI_SIZE:
        case UPLOAD_ERR_FORM_SIZE:
            throw new Exception('文件大小超过限制');
        case UPLOAD_ERR_PARTIAL:
            throw new Exception('文件上传不完整');
        default:
            throw new Exception('文件上传失败，错误代码：' . $uploadedFile['error']);
    }
    
    // 验证文件扩展名（更可靠的方式）
    $fileName = $uploadedFile['name'];
    $fileExtension = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));
    $allowedExtensions = ['xlsx', 'xls'];

    if (!in_array($fileExtension, $allowedExtensions)) {
        throw new Exception('只支持Excel文件格式（.xlsx 或 .xls），当前文件：' . $fileName);
    }

    // 验证文件是否真的存在
    if (!file_exists($uploadedFile['tmp_name'])) {
        throw new Exception('上传的文件不存在');
    }
    
    // 验证文件大小（限制为5MB）
    if ($uploadedFile['size'] > 5 * 1024 * 1024) {
        throw new Exception('文件大小不能超过5MB');
    }
    
    // 读取Excel文件
    try {
        $spreadsheet = IOFactory::load($uploadedFile['tmp_name']);
        $worksheet = $spreadsheet->getActiveSheet();
        $highestRow = $worksheet->getHighestRow();
        $highestColumn = $worksheet->getHighestColumn();
    } catch (Exception $e) {
        throw new Exception('无法读取Excel文件：' . $e->getMessage());
    }
    
    // 验证列数
    if ($highestColumn < 'C') {
        throw new Exception('Excel文件格式不正确，至少需要3列数据（班次、开始日期、结束日期）');
    }
    
    // 查找数据开始行（跳过标题和说明）
    $dataStartRow = 0;
    for ($row = 1; $row <= $highestRow; $row++) {
        $cellValue = $worksheet->getCell('A' . $row)->getValue();
        if ($cellValue === '班次' || $cellValue === 'station') {
            $dataStartRow = $row + 1;
            break;
        }
    }

    if ($dataStartRow === 0) {
        throw new Exception('未找到数据表头，请确保Excel文件包含"班次"列标题');
    }
    
    $importData = [];
    $errors = [];
    $successCount = 0;
    $skipCount = 0;
    $foundInstructionSection = false; // 标记是否已经遇到说明行

    // 开始事务
    $pdo->beginTransaction();
    
    // 读取数据行
    for ($row = $dataStartRow; $row <= $highestRow; $row++) {
        // 安全地获取单元格值
        $station = trim($worksheet->getCell('A' . $row)->getValue() ?? '');
        $startDate = $worksheet->getCell('B' . $row)->getValue();
        $endDate = $worksheet->getCell('C' . $row)->getValue();

        // 如果已经遇到说明行，后面的所有行都跳过
        if ($foundInstructionSection) {
            $skipCount++;
            continue;
        }



        // 处理Excel日期格式
        $startDate = convertExcelDate($startDate);
        $endDate = convertExcelDate($endDate);

        // 从开始日期自动提取年份和月份
        $year = null;
        $month = null;
        if (!empty($startDate)) {
            try {
                $startDateObj = new DateTime($startDate);
                $year = (int)$startDateObj->format('Y');
                $month = (int)$startDateObj->format('n'); // n = 1-12 without leading zeros
            } catch (Exception $e) {
                // 日期解析失败，年份和月份保持为null
            }
        }

        // 更智能的空行检测 - 只有当所有关键字段都真正为空时才认为是空行
        $stationEmpty = ($station === null || $station === '' || trim($station) === '');
        $startDateEmpty = ($startDate === null || $startDate === '' || $startDate === 0);
        $endDateEmpty = ($endDate === null || $endDate === '' || $endDate === 0);

        $isEmpty = $stationEmpty && $startDateEmpty && $endDateEmpty;

        // 检测说明行（包含中文说明文字的行）
        $isInstructionRow = false;
        if (!$isEmpty) {
            $allText = $station . $startDate . $endDate;
            // 更精确的说明行检测 - 只检测明确的说明关键词
            $instructionKeywords = [
                '填写说明', '年份：', '月份：', '班次：', '开始日期：', '结束日期：',
                '系统会', '请删除', '保存文件', '格式为YYYY', '自动计算', '无需手动',
                '实际排班', '选择', '进行上传', '4位数年份', '1-12的数字', '注意：'
            ];

            foreach ($instructionKeywords as $keyword) {
                if (strpos($allText, $keyword) !== false) {
                    $isInstructionRow = true;
                    $foundInstructionSection = true; // 标记已经遇到说明行
                    break;
                }
            }
        }

        // 跳过空行和说明行
        if ($isEmpty || $isInstructionRow) {
            $skipCount++;
            continue;
        }



        // 数据验证
        $rowErrors = [];

        // 验证从开始日期提取的年份和月份
        if (empty($year) || !is_numeric($year) || $year < 2019 || $year > 2030) {
            $rowErrors[] = '无法从开始日期提取有效年份（应为2019-2030之间）';
        }

        if (empty($month) || !is_numeric($month) || $month < 1 || $month > 12) {
            $rowErrors[] = '无法从开始日期提取有效月份（应为1-12之间）';
        }
        
        // 验证班次
        if (empty($station) || !in_array($station, ['一站', '二站'])) {
            $rowErrors[] = '班次无效（只能是"一站"或"二站"）';
        }
        
        // 验证日期格式
        if (empty($startDate) || $startDate === null) {
            $rowErrors[] = '开始日期不能为空';
        } elseif (empty($endDate) || $endDate === null) {
            $rowErrors[] = '结束日期不能为空';
        } else {
            try {
                // 创建DateTime对象进行验证
                $startDateObj = new DateTime($startDate);
                $endDateObj = new DateTime($endDate);

                if ($startDateObj > $endDateObj) {
                    $rowErrors[] = '开始日期不能晚于结束日期';
                }

                $startDateStr = $startDateObj->format('Y-m-d');
                $endDateStr = $endDateObj->format('Y-m-d');

            } catch (Exception $e) {
                $rowErrors[] = '日期格式无效（支持格式：YYYY-MM-DD 或 YYYY/MM/DD）：' . $e->getMessage();
            }
        }
        
        if (!empty($rowErrors)) {
            $errors[] = "第{$row}行：" . implode('；', $rowErrors);
            continue;
        }
        
        // 计算天数
        $days = $startDateObj->diff($endDateObj)->days + 1;
        
        // 检查是否已存在相同的排班记录
        $checkStmt = $pdo->prepare("
            SELECT id FROM shift_schedules 
            WHERE year = ? AND month = ? AND station = ? 
            AND start_date = ? AND end_date = ?
        ");
        $checkStmt->execute([$year, $month, $station, $startDateStr, $endDateStr]);
        
        if ($checkStmt->fetch()) {
            $errors[] = "第{$row}行：相同的排班记录已存在";
            continue;
        }
        
        // 插入数据
        try {
            $insertStmt = $pdo->prepare("
                INSERT INTO shift_schedules (year, month, station, start_date, end_date, days)
                VALUES (?, ?, ?, ?, ?, ?)
            ");
            $insertStmt->execute([$year, $month, $station, $startDateStr, $endDateStr, $days]);
            $successCount++;
            
            $importData[] = [
                'year' => $year,
                'month' => $month,
                'station' => $station,
                'start_date' => $startDateStr,
                'end_date' => $endDateStr,
                'days' => $days
            ];
            
        } catch (PDOException $e) {
            $errors[] = "第{$row}行：数据库插入失败 - " . $e->getMessage();
        }
    }
    
    // 提交事务
    $pdo->commit();
    
    // 记录操作日志
    if (function_exists('logAction')) {
        logAction('导入倒班Excel', 'shift_schedules', [
            'filename' => $uploadedFile['name'],
            'success_count' => $successCount,
            'error_count' => count($errors),
            'skip_count' => $skipCount
        ]);
    }
    
    // 返回结果
    $result = [
        'status' => 'success',
        'message' => "导入完成！成功导入 {$successCount} 条记录",
        'data' => [
            'success_count' => $successCount,
            'error_count' => count($errors),
            'skip_count' => $skipCount,
            'errors' => $errors,
            'imported_data' => $importData,
            'found_instruction_section' => $foundInstructionSection
        ]
    ];

    if (count($errors) > 0) {
        $result['message'] .= "，{$skipCount} 条记录被跳过，" . count($errors) . " 条记录导入失败";
    } elseif ($skipCount > 0) {
        $result['message'] .= "，{$skipCount} 条记录被跳过";
        if ($foundInstructionSection) {
            $result['message'] .= "（包含说明行及其后续内容）";
        }
    }
    
    echo json_encode($result, JSON_UNESCAPED_UNICODE);

} catch (Exception $e) {
    // 回滚事务
    if (isset($pdo) && $pdo->inTransaction()) {
        $pdo->rollback();
    }

    echo json_encode([
        'status' => 'error',
        'message' => $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
?>
