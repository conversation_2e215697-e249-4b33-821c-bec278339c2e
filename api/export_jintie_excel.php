<?php
// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 设置正确的路径
$rootPath = dirname(__DIR__); // 获取上级目录 (zdh)
require_once $rootPath . '/includes/config.php';

// 加载vendor autoload
$vendorPath = dirname($rootPath) . '/vendor/autoload.php';

if (!file_exists($vendorPath)) {
    http_response_code(500);
    exit('PhpSpreadsheet库未安装，请联系管理员');
}
require_once $vendorPath;

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;

// 验证PhpSpreadsheet是否正确加载
if (!class_exists('PhpOffice\PhpSpreadsheet\Spreadsheet')) {
    http_response_code(500);
    exit('PhpSpreadsheet库加载失败');
}

// 检查用户权限
if (!isset($_SESSION['user'])) {
    http_response_code(403);
    exit('未授权访问');
}

$user = $_SESSION['user'];
$hasPermission = $user['role'] === 'admin' || $user['role'] === 'user';

if (!$hasPermission) {
    http_response_code(403);
    exit('权限不足');
}

try {
    // 获取POST数据
    $input = json_decode(file_get_contents('php://input'), true);

    if (!$input || !isset($input['employees']) || !isset($input['record_date'])) {
        http_response_code(400);
        exit('缺少必要参数');
    }

    $employees = $input['employees'];
    $recordDate = $input['record_date'];
    $recordMonth = $input['record_month'] ?? '';

// 创建新的Spreadsheet对象
$spreadsheet = new Spreadsheet();
$sheet = $spreadsheet->getActiveSheet();

// 设置工作表名称
$monthText = '';
if ($recordMonth) {
    $monthDate = new DateTime($recordMonth . '-01');
    $monthText = $monthDate->format('Y年n月');
}
$dateText = $recordDate ? (new DateTime($recordDate))->format('n月j日') : '';

$sheet->setTitle($monthText . $dateText . '津贴计算');

// 设置标题行
$currentRow = 1;
$sheet->setCellValue('A' . $currentRow, '工艺业务流艰苦津贴计算表');
$sheet->mergeCells('A' . $currentRow . ':M' . $currentRow);
$sheet->getStyle('A' . $currentRow)->getFont()->setBold(true)->setSize(16);
$sheet->getStyle('A' . $currentRow)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);

$currentRow++;
$sheet->setCellValue('A' . $currentRow, '月度：' . $monthText . '  日期：' . $recordDate);
$sheet->mergeCells('A' . $currentRow . ':M' . $currentRow);
$sheet->getStyle('A' . $currentRow)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);

$currentRow++;
$sheet->setCellValue('A' . $currentRow, '导出时间：' . date('Y-m-d H:i:s'));
$sheet->mergeCells('A' . $currentRow . ':M' . $currentRow);
$sheet->getStyle('A' . $currentRow)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);

$currentRow += 2; // 空行

// 设置表头
$headers = [
    '序号', '姓名', '岗位', '固定部分', '浮动部分', '应得固定', 
    '应得浮动', '请假天数', '扣罚', '个人奖励', '总金额奖励', '总金额', '备注'
];

$col = 'A';
foreach ($headers as $header) {
    $sheet->setCellValue($col . $currentRow, $header);
    $col++;
}

// 表头样式
$sheet->getStyle('A' . $currentRow . ':M' . $currentRow)->getFont()->setBold(true);
$sheet->getStyle('A' . $currentRow . ':M' . $currentRow)->getFill()
    ->setFillType(Fill::FILL_SOLID)
    ->getStartColor()->setRGB('D3D3D3');
$sheet->getStyle('A' . $currentRow . ':M' . $currentRow)->getAlignment()
    ->setHorizontal(Alignment::HORIZONTAL_CENTER)
    ->setVertical(Alignment::VERTICAL_CENTER);

// 数据行
foreach ($employees as $index => $employee) {
    $currentRow++;
    $sheet->setCellValue('A' . $currentRow, $index + 1);
    $sheet->setCellValue('B' . $currentRow, $employee['name'] ?? '');
    $sheet->setCellValue('C' . $currentRow, $employee['dept'] ?? '');
    $sheet->setCellValue('D' . $currentRow, $employee['fixed'] ?? 0);
    $sheet->setCellValue('E' . $currentRow, $employee['floating'] ?? 0);
    $sheet->setCellValue('F' . $currentRow, $employee['fixedActual'] ?? 0);
    $sheet->setCellValue('G' . $currentRow, $employee['floatingActual'] ?? 0);
    $sheet->setCellValue('H' . $currentRow, $employee['days'] ?? 0);
    $sheet->setCellValue('I' . $currentRow, $employee['deduction'] ?? 0);
    $sheet->setCellValue('J' . $currentRow, $employee['personalBonus'] ?? 0);
    $sheet->setCellValue('K' . $currentRow, $employee['generalBonus'] ?? 0);
    $sheet->setCellValue('L' . $currentRow, $employee['total'] ?? 0);
    $sheet->setCellValue('M' . $currentRow, $employee['remark'] ?? '');

    // 数据行样式
    $sheet->getStyle('A' . $currentRow . ':M' . $currentRow)->getAlignment()
        ->setHorizontal(Alignment::HORIZONTAL_CENTER)
        ->setVertical(Alignment::VERTICAL_CENTER);

    // 数字格式
    $sheet->getStyle('D' . $currentRow . ':L' . $currentRow)->getNumberFormat()->setFormatCode('0');

    // 根据岗位设置不同的背景颜色
    $dept = $employee['dept'] ?? '';
    $backgroundColor = '';

    switch ($dept) {
        case '生产':
            $backgroundColor = 'E8F5E8'; // 浅绿色
            break;
        case '监控':
            $backgroundColor = 'E8F4FD'; // 浅蓝色
            break;
        case 'CB26':
            $backgroundColor = 'FFF2E8'; // 浅橙色
            break;
        default:
            $backgroundColor = 'F8F9FA'; // 浅灰色
            break;
    }

    // 应用背景颜色
    if ($backgroundColor) {
        $sheet->getStyle('A' . $currentRow . ':M' . $currentRow)->getFill()
            ->setFillType(Fill::FILL_SOLID)
            ->getStartColor()->setRGB($backgroundColor);
    }
}

// 设置列宽
$columnWidths = [
    'A' => 8,  'B' => 12, 'C' => 10, 'D' => 12, 'E' => 12, 'F' => 12,
    'G' => 12, 'H' => 10, 'I' => 10, 'J' => 12, 'K' => 12, 'L' => 10, 'M' => 15
];

foreach ($columnWidths as $col => $width) {
    $sheet->getColumnDimension($col)->setWidth($width);
}

// 设置边框
$styleArray = [
    'borders' => [
        'allBorders' => [
            'borderStyle' => Border::BORDER_THIN,
            'color' => ['rgb' => '000000'],
        ],
    ],
];

// 为整个数据区域添加边框
$dataStartRow = $currentRow - count($employees);
$dataEndRow = $currentRow;
$sheet->getStyle('A' . $dataStartRow . ':M' . $dataEndRow)->applyFromArray($styleArray);

// 设置文件名和输出
$filename = '工艺业务流艰苦津贴计算表_' . $monthText . $dateText . '_' . date('Y-m-d') . '.xlsx';

header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
header('Content-Disposition: attachment; filename="' . $filename . '"');
header('Cache-Control: max-age=0');

    $writer = new Xlsx($spreadsheet);
    $writer->save('php://output');
    exit;

} catch (Exception $e) {
    http_response_code(500);
    exit('导出失败：' . $e->getMessage());
}
?>
