<?php
// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 设置正确的路径
$rootPath = dirname(__DIR__); // 获取上级目录 (zdh)
require_once $rootPath . '/includes/config.php';

// 加载vendor autoload
$vendorPath = dirname($rootPath) . '/vendor/autoload.php';

if (!file_exists($vendorPath)) {
    http_response_code(500);
    exit('PhpSpreadsheet库未安装，请联系管理员');
}

try {
    require_once $vendorPath;
} catch (Exception $e) {
    http_response_code(500);
    exit('加载PhpSpreadsheet库失败：' . $e->getMessage());
}

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;

// 验证PhpSpreadsheet是否正确加载
if (!class_exists('PhpOffice\PhpSpreadsheet\Spreadsheet')) {
    http_response_code(500);
    exit('PhpSpreadsheet库加载失败');
}

// 检查用户权限
if (!isset($_SESSION['user'])) {
    http_response_code(403);
    exit('未授权访问');
}

$user = $_SESSION['user'];
$hasPermission = $user['role'] === 'admin';

if (!$hasPermission) {
    http_response_code(403);
    exit('权限不足，只有管理员可以导出数据');
}

// 获取POST参数
$analysisMode = $_POST['analysisMode'] ?? '';
$year = $_POST['year'] ?? '';
$compareYear = $_POST['compareYear'] ?? '';
$years = $_POST['years'] ?? '';

if (empty($analysisMode)) {
    http_response_code(400);
    exit('缺少分析模式参数');
}

try {
    // 创建新的Spreadsheet对象
    $spreadsheet = new Spreadsheet();
    $sheet = $spreadsheet->getActiveSheet();
    
    // 设置文档属性
    $spreadsheet->getProperties()
        ->setCreator('平台常用工具微信小程序后台管理')
        ->setTitle('倒班数据分析报告')
        ->setSubject('倒班数据分析报告')
        ->setDescription('倒班数据分析报告，包含统计数据和图表信息');

    // 设置标题
    $reportTitle = '';
    $reportDate = date('Y年m月d日 H:i:s');
    
    if ($analysisMode === 'single') {
        $reportTitle = $year . '年倒班数据分析报告';
    } elseif ($analysisMode === 'compare') {
        $reportTitle = $year . '年与' . $compareYear . '年倒班数据对比分析报告';
    } elseif ($analysisMode === 'multi') {
        $yearsList = explode(',', $years);
        $reportTitle = implode('、', $yearsList) . '年倒班数据多年对比分析报告';
    }
    
    // 设置表头样式
    $headerStyle = [
        'font' => [
            'bold' => true,
            'size' => 16,
            'color' => ['rgb' => '000000']
        ],
        'alignment' => [
            'horizontal' => Alignment::HORIZONTAL_CENTER,
            'vertical' => Alignment::VERTICAL_CENTER
        ],
        'fill' => [
            'fillType' => Fill::FILL_SOLID,
            'startColor' => ['rgb' => 'E3F2FD']
        ]
    ];
    
    $subHeaderStyle = [
        'font' => [
            'bold' => true,
            'size' => 12,
            'color' => ['rgb' => '000000']
        ],
        'alignment' => [
            'horizontal' => Alignment::HORIZONTAL_CENTER,
            'vertical' => Alignment::VERTICAL_CENTER
        ],
        'fill' => [
            'fillType' => Fill::FILL_SOLID,
            'startColor' => ['rgb' => 'F5F5F5']
        ]
    ];
    
    $dataStyle = [
        'alignment' => [
            'horizontal' => Alignment::HORIZONTAL_CENTER,
            'vertical' => Alignment::VERTICAL_CENTER
        ],
        'borders' => [
            'allBorders' => [
                'borderStyle' => Border::BORDER_THIN,
                'color' => ['rgb' => 'CCCCCC']
            ]
        ]
    ];
    
    // 设置列宽
    $sheet->getColumnDimension('A')->setWidth(20);
    $sheet->getColumnDimension('B')->setWidth(15);
    $sheet->getColumnDimension('C')->setWidth(15);
    $sheet->getColumnDimension('D')->setWidth(20);
    $sheet->getColumnDimension('E')->setWidth(30);
    
    $currentRow = 1;
    
    // 添加报告标题
    $sheet->setCellValue('A' . $currentRow, $reportTitle);
    $sheet->mergeCells('A' . $currentRow . ':E' . $currentRow);
    $sheet->getStyle('A' . $currentRow)->applyFromArray($headerStyle);
    $sheet->getRowDimension($currentRow)->setRowHeight(30);
    $currentRow++;
    
    // 添加生成时间
    $sheet->setCellValue('A' . $currentRow, '报告生成时间：' . $reportDate);
    $sheet->mergeCells('A' . $currentRow . ':E' . $currentRow);
    $sheet->getStyle('A' . $currentRow)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
    $currentRow += 2;
    
    // 根据分析模式获取数据并生成报告
    if ($analysisMode === 'single') {
        generateSingleYearReport($sheet, $currentRow, $year, $subHeaderStyle, $dataStyle);
    } elseif ($analysisMode === 'compare') {
        generateCompareReport($sheet, $currentRow, $year, $compareYear, $subHeaderStyle, $dataStyle);
    } elseif ($analysisMode === 'multi') {
        generateMultiYearReport($sheet, $currentRow, $years, $subHeaderStyle, $dataStyle);
    }
    
    // 设置文件名
    $filename = $reportTitle . '_' . date('YmdHis') . '.xlsx';
    $filename = str_replace(['年', '月', '日', '与', '、'], ['', '', '', '_', '_'], $filename);
    
    // 设置HTTP头
    header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    header('Content-Disposition: attachment;filename="' . $filename . '"');
    header('Cache-Control: max-age=0');
    
    // 输出文件
    $writer = new Xlsx($spreadsheet);
    $writer->save('php://output');
    
} catch (Exception $e) {
    http_response_code(500);
    exit('导出失败：' . $e->getMessage());
}

// 单年分析报告生成函数
function generateSingleYearReport($sheet, &$currentRow, $year, $subHeaderStyle, $dataStyle) {
    global $pdo;
    
    // 获取年度统计数据
    $sql = "SELECT 
                COUNT(*) as total_schedules,
                SUM(DATEDIFF(end_date, start_date) + 1) as total_days,
                SUM(CASE WHEN station = '一站' THEN DATEDIFF(end_date, start_date) + 1 ELSE 0 END) as station1_days,
                SUM(CASE WHEN station = '二站' THEN DATEDIFF(end_date, start_date) + 1 ELSE 0 END) as station2_days,
                AVG(DATEDIFF(end_date, start_date) + 1) as avg_days,
                MAX(DATEDIFF(end_date, start_date) + 1) as max_days,
                MIN(DATEDIFF(end_date, start_date) + 1) as min_days
            FROM shift_schedules 
            WHERE YEAR(start_date) = ?";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$year]);
    $yearStats = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // 添加年度概览
    $sheet->setCellValue('A' . $currentRow, '年度概览');
    $sheet->mergeCells('A' . $currentRow . ':E' . $currentRow);
    $sheet->getStyle('A' . $currentRow)->applyFromArray($subHeaderStyle);
    $currentRow++;
    
    $overviewData = [
        ['统计项目', '数值', '单位', '说明', ''],
        ['总出海天数', $yearStats['total_days'] ?: 0, '天', '全年累计出海天数', ''],
        ['一站天数', $yearStats['station1_days'] ?: 0, '天', '一站班次累计天数', ''],
        ['二站天数', $yearStats['station2_days'] ?: 0, '天', '二站班次累计天数', ''],
        ['相差天数', abs(($yearStats['station1_days'] ?: 0) - ($yearStats['station2_days'] ?: 0)), '天', '一站二站相差天数', ''],
        ['总班次数', $yearStats['total_schedules'] ?: 0, '次', '全年班次总数', ''],
        ['平均班次', round($yearStats['avg_days'] ?: 0, 1), '天', '单次班次平均天数', ''],
        ['最长班次', $yearStats['max_days'] ?: 0, '天', '单次班次最长天数', ''],
        ['最短班次', $yearStats['min_days'] ?: 0, '天', '单次班次最短天数', '']
    ];
    
    foreach ($overviewData as $row) {
        $sheet->fromArray($row, null, 'A' . $currentRow);
        $sheet->getStyle('A' . $currentRow . ':E' . $currentRow)->applyFromArray($dataStyle);
        $currentRow++;
    }
    
    $currentRow += 2;

    // 添加月度统计
    addMonthlyStats($sheet, $currentRow, $year, $subHeaderStyle, $dataStyle);

    // 添加详细班次信息
    addDetailedSchedules($sheet, $currentRow, $year, $subHeaderStyle, $dataStyle);

    // 添加班次分布分析
    addStationDistribution($sheet, $currentRow, $year, $subHeaderStyle, $dataStyle);
}

// 添加月度统计数据
function addMonthlyStats($sheet, &$currentRow, $year, $subHeaderStyle, $dataStyle) {
    global $pdo;
    
    $sheet->setCellValue('A' . $currentRow, '月度统计');
    $sheet->mergeCells('A' . $currentRow . ':E' . $currentRow);
    $sheet->getStyle('A' . $currentRow)->applyFromArray($subHeaderStyle);
    $currentRow++;
    
    // 获取月度数据
    $sql = "SELECT 
                MONTH(start_date) as month,
                COUNT(*) as schedules,
                SUM(DATEDIFF(end_date, start_date) + 1) as total_days,
                SUM(CASE WHEN station = '一站' THEN DATEDIFF(end_date, start_date) + 1 ELSE 0 END) as station1_days,
                SUM(CASE WHEN station = '二站' THEN DATEDIFF(end_date, start_date) + 1 ELSE 0 END) as station2_days
            FROM shift_schedules 
            WHERE YEAR(start_date) = ?
            GROUP BY MONTH(start_date)
            ORDER BY MONTH(start_date)";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$year]);
    $monthlyData = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // 表头
    $monthlyHeaders = ['月份', '一站天数', '二站天数', '总天数', '班次数'];
    $sheet->fromArray($monthlyHeaders, null, 'A' . $currentRow);
    $sheet->getStyle('A' . $currentRow . ':E' . $currentRow)->applyFromArray($subHeaderStyle);
    $currentRow++;
    
    // 数据行
    foreach ($monthlyData as $data) {
        $monthRow = [
            $data['month'] . '月',
            $data['station1_days'] ?: 0,
            $data['station2_days'] ?: 0,
            $data['total_days'] ?: 0,
            $data['schedules'] ?: 0
        ];
        $sheet->fromArray($monthRow, null, 'A' . $currentRow);
        $sheet->getStyle('A' . $currentRow . ':E' . $currentRow)->applyFromArray($dataStyle);
        $currentRow++;
    }
}

// 双年对比报告生成函数
function generateCompareReport($sheet, &$currentRow, $year, $compareYear, $subHeaderStyle, $dataStyle) {
    global $pdo;

    // 获取两年的统计数据
    $sql = "SELECT
                YEAR(start_date) as year,
                COUNT(*) as total_schedules,
                SUM(DATEDIFF(end_date, start_date) + 1) as total_days,
                SUM(CASE WHEN station = '一站' THEN DATEDIFF(end_date, start_date) + 1 ELSE 0 END) as station1_days,
                SUM(CASE WHEN station = '二站' THEN DATEDIFF(end_date, start_date) + 1 ELSE 0 END) as station2_days
            FROM shift_schedules
            WHERE YEAR(start_date) IN (?, ?)
            GROUP BY YEAR(start_date)
            ORDER BY YEAR(start_date)";

    $stmt = $pdo->prepare($sql);
    $stmt->execute([$year, $compareYear]);
    $yearData = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // 添加对比概览
    $sheet->setCellValue('A' . $currentRow, '双年对比概览');
    $sheet->mergeCells('A' . $currentRow . ':E' . $currentRow);
    $sheet->getStyle('A' . $currentRow)->applyFromArray($subHeaderStyle);
    $currentRow++;

    // 表头
    $compareHeaders = ['统计项目', $year . '年', $compareYear . '年', '差值', '说明'];
    $sheet->fromArray($compareHeaders, null, 'A' . $currentRow);
    $sheet->getStyle('A' . $currentRow . ':E' . $currentRow)->applyFromArray($subHeaderStyle);
    $currentRow++;

    // 处理数据
    $data1 = null;
    $data2 = null;
    foreach ($yearData as $data) {
        if ($data['year'] == $year) {
            $data1 = $data;
        } elseif ($data['year'] == $compareYear) {
            $data2 = $data;
        }
    }

    // 如果某年没有数据，设置默认值
    if (!$data1) $data1 = ['total_days' => 0, 'station1_days' => 0, 'station2_days' => 0, 'total_schedules' => 0];
    if (!$data2) $data2 = ['total_days' => 0, 'station1_days' => 0, 'station2_days' => 0, 'total_schedules' => 0];

    $compareData = [
        ['总出海天数', $data1['total_days'], $data2['total_days'], $data1['total_days'] - $data2['total_days'], '全年累计出海天数对比'],
        ['一站天数', $data1['station1_days'], $data2['station1_days'], $data1['station1_days'] - $data2['station1_days'], '一站班次累计天数对比'],
        ['二站天数', $data1['station2_days'], $data2['station2_days'], $data1['station2_days'] - $data2['station2_days'], '二站班次累计天数对比'],
        ['总班次数', $data1['total_schedules'], $data2['total_schedules'], $data1['total_schedules'] - $data2['total_schedules'], '全年班次总数对比']
    ];

    foreach ($compareData as $row) {
        $sheet->fromArray($row, null, 'A' . $currentRow);
        $sheet->getStyle('A' . $currentRow . ':E' . $currentRow)->applyFromArray($dataStyle);
        $currentRow++;
    }

    $currentRow += 2;

    // 添加月度对比
    addMonthlyCompare($sheet, $currentRow, $year, $compareYear, $subHeaderStyle, $dataStyle);

    // 添加趋势分析
    addTrendAnalysis($sheet, $currentRow, $year, $compareYear, $subHeaderStyle, $dataStyle);

    // 添加班次对比分析
    addScheduleComparison($sheet, $currentRow, $year, $compareYear, $subHeaderStyle, $dataStyle);
}

// 月度对比函数
function addMonthlyCompare($sheet, &$currentRow, $year, $compareYear, $subHeaderStyle, $dataStyle) {
    global $pdo;

    $sheet->setCellValue('A' . $currentRow, '月度对比统计');
    $sheet->mergeCells('A' . $currentRow . ':E' . $currentRow);
    $sheet->getStyle('A' . $currentRow)->applyFromArray($subHeaderStyle);
    $currentRow++;

    // 获取两年的月度数据
    $sql = "SELECT
                YEAR(start_date) as year,
                MONTH(start_date) as month,
                SUM(CASE WHEN station = '一站' THEN DATEDIFF(end_date, start_date) + 1 ELSE 0 END) as station1_days,
                SUM(CASE WHEN station = '二站' THEN DATEDIFF(end_date, start_date) + 1 ELSE 0 END) as station2_days
            FROM shift_schedules
            WHERE YEAR(start_date) IN (?, ?)
            GROUP BY YEAR(start_date), MONTH(start_date)
            ORDER BY MONTH(start_date), YEAR(start_date)";

    $stmt = $pdo->prepare($sql);
    $stmt->execute([$year, $compareYear]);
    $monthlyData = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // 表头
    $monthlyHeaders = ['月份', $year . '年一站', $year . '年二站', $compareYear . '年一站', $compareYear . '年二站'];
    $sheet->fromArray($monthlyHeaders, null, 'A' . $currentRow);
    $sheet->getStyle('A' . $currentRow . ':E' . $currentRow)->applyFromArray($subHeaderStyle);
    $currentRow++;

    // 整理数据
    $monthlyStats = [];
    foreach ($monthlyData as $data) {
        $month = $data['month'];
        if (!isset($monthlyStats[$month])) {
            $monthlyStats[$month] = [
                'month' => $month,
                'year1_station1' => 0,
                'year1_station2' => 0,
                'year2_station1' => 0,
                'year2_station2' => 0
            ];
        }

        if ($data['year'] == $year) {
            $monthlyStats[$month]['year1_station1'] = $data['station1_days'];
            $monthlyStats[$month]['year1_station2'] = $data['station2_days'];
        } else {
            $monthlyStats[$month]['year2_station1'] = $data['station1_days'];
            $monthlyStats[$month]['year2_station2'] = $data['station2_days'];
        }
    }

    // 输出数据
    for ($month = 1; $month <= 12; $month++) {
        $stats = $monthlyStats[$month] ?? [
            'year1_station1' => 0, 'year1_station2' => 0,
            'year2_station1' => 0, 'year2_station2' => 0
        ];

        $monthRow = [
            $month . '月',
            $stats['year1_station1'],
            $stats['year1_station2'],
            $stats['year2_station1'],
            $stats['year2_station2']
        ];
        $sheet->fromArray($monthRow, null, 'A' . $currentRow);
        $sheet->getStyle('A' . $currentRow . ':E' . $currentRow)->applyFromArray($dataStyle);
        $currentRow++;
    }
}

// 多年对比报告生成函数
function generateMultiYearReport($sheet, &$currentRow, $years, $subHeaderStyle, $dataStyle) {
    global $pdo;

    $yearsList = explode(',', $years);
    $placeholders = str_repeat('?,', count($yearsList) - 1) . '?';

    // 获取多年统计数据
    $sql = "SELECT
                YEAR(start_date) as year,
                COUNT(*) as total_schedules,
                SUM(DATEDIFF(end_date, start_date) + 1) as total_days,
                SUM(CASE WHEN station = '一站' THEN DATEDIFF(end_date, start_date) + 1 ELSE 0 END) as station1_days,
                SUM(CASE WHEN station = '二站' THEN DATEDIFF(end_date, start_date) + 1 ELSE 0 END) as station2_days
            FROM shift_schedules
            WHERE YEAR(start_date) IN ($placeholders)
            GROUP BY YEAR(start_date)
            ORDER BY YEAR(start_date)";

    $stmt = $pdo->prepare($sql);
    $stmt->execute($yearsList);
    $multiYearData = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // 添加多年概览
    $sheet->setCellValue('A' . $currentRow, '多年对比概览');
    $sheet->mergeCells('A' . $currentRow . ':E' . $currentRow);
    $sheet->getStyle('A' . $currentRow)->applyFromArray($subHeaderStyle);
    $currentRow++;

    // 表头
    $multiHeaders = ['年份', '一站天数', '二站天数', '总天数', '班次数'];
    $sheet->fromArray($multiHeaders, null, 'A' . $currentRow);
    $sheet->getStyle('A' . $currentRow . ':E' . $currentRow)->applyFromArray($subHeaderStyle);
    $currentRow++;

    // 数据行
    $totalStation1 = 0;
    $totalStation2 = 0;
    $totalDays = 0;
    $totalSchedules = 0;

    foreach ($multiYearData as $data) {
        $yearRow = [
            $data['year'] . '年',
            $data['station1_days'] ?: 0,
            $data['station2_days'] ?: 0,
            $data['total_days'] ?: 0,
            $data['total_schedules'] ?: 0
        ];
        $sheet->fromArray($yearRow, null, 'A' . $currentRow);
        $sheet->getStyle('A' . $currentRow . ':E' . $currentRow)->applyFromArray($dataStyle);
        $currentRow++;

        $totalStation1 += $data['station1_days'] ?: 0;
        $totalStation2 += $data['station2_days'] ?: 0;
        $totalDays += $data['total_days'] ?: 0;
        $totalSchedules += $data['total_schedules'] ?: 0;
    }

    // 总计行
    $totalRow = ['总计', $totalStation1, $totalStation2, $totalDays, $totalSchedules];
    $sheet->fromArray($totalRow, null, 'A' . $currentRow);
    $sheet->getStyle('A' . $currentRow . ':E' . $currentRow)->applyFromArray(array_merge($dataStyle, [
        'font' => ['bold' => true],
        'fill' => [
            'fillType' => Fill::FILL_SOLID,
            'startColor' => ['rgb' => 'E3F2FD']
        ]
    ]));
    $currentRow++;

    $currentRow += 2;

    // 添加多年趋势分析
    addMultiYearTrend($sheet, $currentRow, $yearsList, $multiYearData, $subHeaderStyle, $dataStyle);

    // 添加年度排名分析
    addYearRanking($sheet, $currentRow, $multiYearData, $subHeaderStyle, $dataStyle);
}

// 添加详细班次信息
function addDetailedSchedules($sheet, &$currentRow, $year, $subHeaderStyle, $dataStyle) {
    global $pdo;

    $currentRow += 2;
    $sheet->setCellValue('A' . $currentRow, '详细班次信息');
    $sheet->mergeCells('A' . $currentRow . ':E' . $currentRow);
    $sheet->getStyle('A' . $currentRow)->applyFromArray($subHeaderStyle);
    $currentRow++;

    // 获取详细班次数据
    $sql = "SELECT
                DATE_FORMAT(start_date, '%Y-%m-%d') as start_date,
                DATE_FORMAT(end_date, '%Y-%m-%d') as end_date,
                station,
                DATEDIFF(end_date, start_date) + 1 as days,
                MONTH(start_date) as month
            FROM shift_schedules
            WHERE YEAR(start_date) = ?
            ORDER BY start_date";

    $stmt = $pdo->prepare($sql);
    $stmt->execute([$year]);
    $schedules = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // 表头
    $detailHeaders = ['开始日期', '结束日期', '班次', '天数', '月份'];
    $sheet->fromArray($detailHeaders, null, 'A' . $currentRow);
    $sheet->getStyle('A' . $currentRow . ':E' . $currentRow)->applyFromArray($subHeaderStyle);
    $currentRow++;

    // 数据行
    foreach ($schedules as $schedule) {
        $scheduleRow = [
            $schedule['start_date'],
            $schedule['end_date'],
            $schedule['station'],
            $schedule['days'] . '天',
            $schedule['month'] . '月'
        ];
        $sheet->fromArray($scheduleRow, null, 'A' . $currentRow);
        $sheet->getStyle('A' . $currentRow . ':E' . $currentRow)->applyFromArray($dataStyle);
        $currentRow++;
    }
}

// 添加班次分布分析
function addStationDistribution($sheet, &$currentRow, $year, $subHeaderStyle, $dataStyle) {
    global $pdo;

    $currentRow += 2;
    $sheet->setCellValue('A' . $currentRow, '班次分布分析');
    $sheet->mergeCells('A' . $currentRow . ':E' . $currentRow);
    $sheet->getStyle('A' . $currentRow)->applyFromArray($subHeaderStyle);
    $currentRow++;

    // 获取班次分布数据
    $sql = "SELECT
                station,
                COUNT(*) as schedule_count,
                SUM(DATEDIFF(end_date, start_date) + 1) as total_days,
                AVG(DATEDIFF(end_date, start_date) + 1) as avg_days,
                MAX(DATEDIFF(end_date, start_date) + 1) as max_days,
                MIN(DATEDIFF(end_date, start_date) + 1) as min_days
            FROM shift_schedules
            WHERE YEAR(start_date) = ?
            GROUP BY station
            ORDER BY station";

    $stmt = $pdo->prepare($sql);
    $stmt->execute([$year]);
    $distribution = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // 表头
    $distHeaders = ['班次', '次数', '总天数', '平均天数', '最长天数', '最短天数'];
    $sheet->fromArray($distHeaders, null, 'A' . $currentRow);
    $sheet->getStyle('A' . $currentRow . ':F' . $currentRow)->applyFromArray($subHeaderStyle);
    $currentRow++;

    // 数据行
    foreach ($distribution as $dist) {
        $distRow = [
            $dist['station'],
            $dist['schedule_count'] . '次',
            $dist['total_days'] . '天',
            round($dist['avg_days'], 1) . '天',
            $dist['max_days'] . '天',
            $dist['min_days'] . '天'
        ];
        $sheet->fromArray($distRow, null, 'A' . $currentRow);
        $sheet->getStyle('A' . $currentRow . ':F' . $currentRow)->applyFromArray($dataStyle);
        $currentRow++;
    }

    // 添加占比分析
    $currentRow++;
    $sheet->setCellValue('A' . $currentRow, '班次占比分析');
    $sheet->mergeCells('A' . $currentRow . ':E' . $currentRow);
    $sheet->getStyle('A' . $currentRow)->applyFromArray($subHeaderStyle);
    $currentRow++;

    // 计算总天数
    $totalDays = 0;
    foreach ($distribution as $dist) {
        $totalDays += $dist['total_days'];
    }

    // 占比表头
    $ratioHeaders = ['班次', '天数', '占比', '说明', ''];
    $sheet->fromArray($ratioHeaders, null, 'A' . $currentRow);
    $sheet->getStyle('A' . $currentRow . ':E' . $currentRow)->applyFromArray($subHeaderStyle);
    $currentRow++;

    // 占比数据
    foreach ($distribution as $dist) {
        $ratio = $totalDays > 0 ? round(($dist['total_days'] / $totalDays) * 100, 1) : 0;
        $ratioRow = [
            $dist['station'],
            $dist['total_days'] . '天',
            $ratio . '%',
            $ratio > 50 ? '偏多' : ($ratio < 50 ? '偏少' : '平衡'),
            ''
        ];
        $sheet->fromArray($ratioRow, null, 'A' . $currentRow);
        $sheet->getStyle('A' . $currentRow . ':E' . $currentRow)->applyFromArray($dataStyle);
        $currentRow++;
    }
}

// 添加趋势分析
function addTrendAnalysis($sheet, &$currentRow, $year, $compareYear, $subHeaderStyle, $dataStyle) {
    global $pdo;

    $currentRow += 2;
    $sheet->setCellValue('A' . $currentRow, '趋势分析');
    $sheet->mergeCells('A' . $currentRow . ':E' . $currentRow);
    $sheet->getStyle('A' . $currentRow)->applyFromArray($subHeaderStyle);
    $currentRow++;

    // 获取两年的详细统计
    $sql = "SELECT
                YEAR(start_date) as year,
                COUNT(*) as total_schedules,
                SUM(DATEDIFF(end_date, start_date) + 1) as total_days,
                SUM(CASE WHEN station = '一站' THEN DATEDIFF(end_date, start_date) + 1 ELSE 0 END) as station1_days,
                SUM(CASE WHEN station = '二站' THEN DATEDIFF(end_date, start_date) + 1 ELSE 0 END) as station2_days,
                AVG(DATEDIFF(end_date, start_date) + 1) as avg_days
            FROM shift_schedules
            WHERE YEAR(start_date) IN (?, ?)
            GROUP BY YEAR(start_date)
            ORDER BY YEAR(start_date)";

    $stmt = $pdo->prepare($sql);
    $stmt->execute([$year, $compareYear]);
    $trendData = $stmt->fetchAll(PDO::FETCH_ASSOC);

    if (count($trendData) >= 2) {
        $data1 = $trendData[0];
        $data2 = $trendData[1];

        // 计算变化趋势
        $totalDaysChange = $data2['total_days'] - $data1['total_days'];
        $station1Change = $data2['station1_days'] - $data1['station1_days'];
        $station2Change = $data2['station2_days'] - $data1['station2_days'];
        $schedulesChange = $data2['total_schedules'] - $data1['total_schedules'];

        $trendHeaders = ['指标', '变化值', '变化率', '趋势', '说明'];
        $sheet->fromArray($trendHeaders, null, 'A' . $currentRow);
        $sheet->getStyle('A' . $currentRow . ':E' . $currentRow)->applyFromArray($subHeaderStyle);
        $currentRow++;

        $trendAnalysis = [
            [
                '总出海天数',
                ($totalDaysChange >= 0 ? '+' : '') . $totalDaysChange . '天',
                ($data1['total_days'] > 0 ? round(($totalDaysChange / $data1['total_days']) * 100, 1) : 0) . '%',
                $totalDaysChange > 0 ? '上升' : ($totalDaysChange < 0 ? '下降' : '持平'),
                $totalDaysChange > 0 ? '出海天数增加' : ($totalDaysChange < 0 ? '出海天数减少' : '出海天数保持稳定')
            ],
            [
                '一站天数',
                ($station1Change >= 0 ? '+' : '') . $station1Change . '天',
                ($data1['station1_days'] > 0 ? round(($station1Change / $data1['station1_days']) * 100, 1) : 0) . '%',
                $station1Change > 0 ? '上升' : ($station1Change < 0 ? '下降' : '持平'),
                $station1Change > 0 ? '一站班次增加' : ($station1Change < 0 ? '一站班次减少' : '一站班次保持稳定')
            ],
            [
                '二站天数',
                ($station2Change >= 0 ? '+' : '') . $station2Change . '天',
                ($data1['station2_days'] > 0 ? round(($station2Change / $data1['station2_days']) * 100, 1) : 0) . '%',
                $station2Change > 0 ? '上升' : ($station2Change < 0 ? '下降' : '持平'),
                $station2Change > 0 ? '二站班次增加' : ($station2Change < 0 ? '二站班次减少' : '二站班次保持稳定')
            ],
            [
                '班次总数',
                ($schedulesChange >= 0 ? '+' : '') . $schedulesChange . '次',
                ($data1['total_schedules'] > 0 ? round(($schedulesChange / $data1['total_schedules']) * 100, 1) : 0) . '%',
                $schedulesChange > 0 ? '上升' : ($schedulesChange < 0 ? '下降' : '持平'),
                $schedulesChange > 0 ? '班次频率增加' : ($schedulesChange < 0 ? '班次频率减少' : '班次频率保持稳定')
            ]
        ];

        foreach ($trendAnalysis as $trend) {
            $sheet->fromArray($trend, null, 'A' . $currentRow);
            $sheet->getStyle('A' . $currentRow . ':E' . $currentRow)->applyFromArray($dataStyle);
            $currentRow++;
        }
    }
}

// 添加班次对比分析
function addScheduleComparison($sheet, &$currentRow, $year, $compareYear, $subHeaderStyle, $dataStyle) {
    global $pdo;

    $currentRow += 2;
    $sheet->setCellValue('A' . $currentRow, '班次详细对比');
    $sheet->mergeCells('A' . $currentRow . ':E' . $currentRow);
    $sheet->getStyle('A' . $currentRow)->applyFromArray($subHeaderStyle);
    $currentRow++;

    // 获取两年的班次统计
    $sql = "SELECT
                YEAR(start_date) as year,
                station,
                COUNT(*) as schedule_count,
                SUM(DATEDIFF(end_date, start_date) + 1) as total_days,
                AVG(DATEDIFF(end_date, start_date) + 1) as avg_days,
                MAX(DATEDIFF(end_date, start_date) + 1) as max_days,
                MIN(DATEDIFF(end_date, start_date) + 1) as min_days
            FROM shift_schedules
            WHERE YEAR(start_date) IN (?, ?)
            GROUP BY YEAR(start_date), station
            ORDER BY station, YEAR(start_date)";

    $stmt = $pdo->prepare($sql);
    $stmt->execute([$year, $compareYear]);
    $scheduleData = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // 表头
    $compHeaders = ['班次', '指标', $year . '年', $compareYear . '年', '差值'];
    $sheet->fromArray($compHeaders, null, 'A' . $currentRow);
    $sheet->getStyle('A' . $currentRow . ':E' . $currentRow)->applyFromArray($subHeaderStyle);
    $currentRow++;

    // 整理数据
    $stationData = [];
    foreach ($scheduleData as $data) {
        $station = $data['station'];
        $year_key = $data['year'];
        if (!isset($stationData[$station])) {
            $stationData[$station] = [];
        }
        $stationData[$station][$year_key] = $data;
    }

    // 输出对比数据
    foreach (['一站', '二站'] as $station) {
        if (isset($stationData[$station])) {
            $data1 = $stationData[$station][$year] ?? null;
            $data2 = $stationData[$station][$compareYear] ?? null;

            $metrics = [
                ['班次数', 'schedule_count', '次'],
                ['总天数', 'total_days', '天'],
                ['平均天数', 'avg_days', '天'],
                ['最长天数', 'max_days', '天'],
                ['最短天数', 'min_days', '天']
            ];

            foreach ($metrics as $i => $metric) {
                $value1 = $data1 ? ($metric[1] === 'avg_days' ? round($data1[$metric[1]], 1) : $data1[$metric[1]]) : 0;
                $value2 = $data2 ? ($metric[1] === 'avg_days' ? round($data2[$metric[1]], 1) : $data2[$metric[1]]) : 0;
                $diff = $value2 - $value1;

                $compRow = [
                    $i === 0 ? $station : '',
                    $metric[0],
                    $value1 . $metric[2],
                    $value2 . $metric[2],
                    ($diff >= 0 ? '+' : '') . $diff . $metric[2]
                ];
                $sheet->fromArray($compRow, null, 'A' . $currentRow);
                $sheet->getStyle('A' . $currentRow . ':E' . $currentRow)->applyFromArray($dataStyle);
                $currentRow++;
            }
        }
    }
}

// 添加多年趋势分析
function addMultiYearTrend($sheet, &$currentRow, $yearsList, $multiYearData, $subHeaderStyle, $dataStyle) {
    $sheet->setCellValue('A' . $currentRow, '多年趋势分析');
    $sheet->mergeCells('A' . $currentRow . ':E' . $currentRow);
    $sheet->getStyle('A' . $currentRow)->applyFromArray($subHeaderStyle);
    $currentRow++;

    // 表头
    $trendHeaders = ['年份', '总天数变化', '一站变化', '二站变化', '趋势说明'];
    $sheet->fromArray($trendHeaders, null, 'A' . $currentRow);
    $sheet->getStyle('A' . $currentRow . ':E' . $currentRow)->applyFromArray($subHeaderStyle);
    $currentRow++;

    // 计算年度变化
    for ($i = 1; $i < count($multiYearData); $i++) {
        $prevYear = $multiYearData[$i-1];
        $currYear = $multiYearData[$i];

        $totalChange = $currYear['total_days'] - $prevYear['total_days'];
        $station1Change = $currYear['station1_days'] - $prevYear['station1_days'];
        $station2Change = $currYear['station2_days'] - $prevYear['station2_days'];

        $trendDesc = '';
        if ($totalChange > 0) {
            $trendDesc = '出海天数增加';
        } elseif ($totalChange < 0) {
            $trendDesc = '出海天数减少';
        } else {
            $trendDesc = '出海天数稳定';
        }

        if (abs($station1Change - $station2Change) > 10) {
            $trendDesc .= '，班次分配不均';
        } else {
            $trendDesc .= '，班次分配均衡';
        }

        $trendRow = [
            $currYear['year'] . '年',
            ($totalChange >= 0 ? '+' : '') . $totalChange . '天',
            ($station1Change >= 0 ? '+' : '') . $station1Change . '天',
            ($station2Change >= 0 ? '+' : '') . $station2Change . '天',
            $trendDesc
        ];
        $sheet->fromArray($trendRow, null, 'A' . $currentRow);
        $sheet->getStyle('A' . $currentRow . ':E' . $currentRow)->applyFromArray($dataStyle);
        $currentRow++;
    }
}

// 添加年度排名分析
function addYearRanking($sheet, &$currentRow, $multiYearData, $subHeaderStyle, $dataStyle) {
    $currentRow += 2;
    $sheet->setCellValue('A' . $currentRow, '年度排名分析');
    $sheet->mergeCells('A' . $currentRow . ':E' . $currentRow);
    $sheet->getStyle('A' . $currentRow)->applyFromArray($subHeaderStyle);
    $currentRow++;

    // 按总天数排序
    $sortedByTotal = $multiYearData;
    usort($sortedByTotal, function($a, $b) {
        return $b['total_days'] - $a['total_days'];
    });

    // 总天数排名
    $sheet->setCellValue('A' . $currentRow, '总出海天数排名');
    $sheet->mergeCells('A' . $currentRow . ':E' . $currentRow);
    $sheet->getStyle('A' . $currentRow)->applyFromArray($subHeaderStyle);
    $currentRow++;

    $rankHeaders = ['排名', '年份', '总天数', '一站天数', '二站天数'];
    $sheet->fromArray($rankHeaders, null, 'A' . $currentRow);
    $sheet->getStyle('A' . $currentRow . ':E' . $currentRow)->applyFromArray($subHeaderStyle);
    $currentRow++;

    foreach ($sortedByTotal as $index => $data) {
        $rankRow = [
            '第' . ($index + 1) . '名',
            $data['year'] . '年',
            $data['total_days'] . '天',
            $data['station1_days'] . '天',
            $data['station2_days'] . '天'
        ];
        $sheet->fromArray($rankRow, null, 'A' . $currentRow);
        $sheet->getStyle('A' . $currentRow . ':E' . $currentRow)->applyFromArray($dataStyle);
        $currentRow++;
    }

    $currentRow += 2;

    // 班次平衡度分析
    $sheet->setCellValue('A' . $currentRow, '班次平衡度分析');
    $sheet->mergeCells('A' . $currentRow . ':E' . $currentRow);
    $sheet->getStyle('A' . $currentRow)->applyFromArray($subHeaderStyle);
    $currentRow++;

    $balanceHeaders = ['年份', '一站天数', '二站天数', '差值', '平衡度'];
    $sheet->fromArray($balanceHeaders, null, 'A' . $currentRow);
    $sheet->getStyle('A' . $currentRow . ':E' . $currentRow)->applyFromArray($subHeaderStyle);
    $currentRow++;

    foreach ($multiYearData as $data) {
        $diff = abs($data['station1_days'] - $data['station2_days']);
        $balance = '';
        if ($diff <= 5) {
            $balance = '非常均衡';
        } elseif ($diff <= 15) {
            $balance = '基本均衡';
        } elseif ($diff <= 30) {
            $balance = '略有偏差';
        } else {
            $balance = '明显偏差';
        }

        $balanceRow = [
            $data['year'] . '年',
            $data['station1_days'] . '天',
            $data['station2_days'] . '天',
            $diff . '天',
            $balance
        ];
        $sheet->fromArray($balanceRow, null, 'A' . $currentRow);
        $sheet->getStyle('A' . $currentRow . ':E' . $currentRow)->applyFromArray($dataStyle);
        $currentRow++;
    }
}
?>
