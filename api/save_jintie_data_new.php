<?php
// 开启错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 设置响应头
header('Content-Type: application/json; charset=utf-8');

// 使用config.php的数据库配置
require_once '../includes/config.php';

try {
    
    // 检查请求方法
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        echo json_encode(['success' => false, 'message' => '方法不允许']);
        exit;
    }
    
    // 获取POST数据
    $rawInput = file_get_contents('php://input');
    $input = json_decode($rawInput, true);
    
    if (!$input) {
        echo json_encode([
            'success' => false, 
            'message' => '无效的JSON数据',
            'raw_input' => substr($rawInput, 0, 200)
        ]);
        exit;
    }
    
    // 获取基础参数
    $recordDate = $input['record_date'] ?? date('Y-m-d');
    $monthYear = date('Y-m', strtotime($recordDate));
    $workersCount = $input['workers_count'] ?? 15;
    $jintieTotal = $input['jintie_total'] ?? 10000;
    $distribution = $input['distribution'] ?? 0;
    $employees = $input['employees'] ?? [];
    
    $pdo->beginTransaction();
    
    $savedCount = 0;

    // 删除该日期的旧记录
    $deleteSql = "DELETE FROM jintie_records WHERE record_date = ?";
    $deleteStmt = $pdo->prepare($deleteSql);
    $deleteStmt->execute([$recordDate]);

    // 记录删除的行数
    $deletedCount = $deleteStmt->rowCount();
    
    // 插入员工数据
    foreach ($employees as $index => $employee) {
        // 跳过空姓名的记录
        $name = trim($employee['name'] ?? '');
        if (empty($name)) {
            continue; // 跳过没有姓名的记录
        }

        $insertSql = "INSERT INTO jintie_records (
            record_date, month_year, name, dept, fixed_amount, floating_amount,
            fixed_actual, floating_actual, days, deduction, personal_bonus,
            general_bonus, total_amount, is_fixed_bonus, floating_ratio, remark
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

        $insertStmt = $pdo->prepare($insertSql);
        $insertStmt->execute([
            $recordDate,
            $monthYear,
            $name,
            $employee['dept'] ?? '',
            floatval($employee['fixed'] ?? 0),
            floatval($employee['floating'] ?? 0),
            floatval($employee['fixed_actual'] ?? 0),
            floatval($employee['floating_actual'] ?? 0),
            intval($employee['days'] ?? 0),
            floatval($employee['deduction'] ?? 0),
            floatval($employee['personal_bonus'] ?? 0),
            floatval($employee['general_bonus'] ?? 0),
            floatval($employee['total'] ?? 0),
            intval($employee['is_fixed_bonus'] ?? 0),
            floatval($employee['floating_ratio'] ?? 1.0),
            $employee['remark'] ?? ''
        ]);
        $savedCount++;
    }
    
    // 保存参数
    $paramSql = "INSERT INTO jintie_params (record_date, workers_count, jintie_total, distribution) 
                 VALUES (?, ?, ?, ?) 
                 ON DUPLICATE KEY UPDATE 
                 workers_count = VALUES(workers_count),
                 jintie_total = VALUES(jintie_total),
                 distribution = VALUES(distribution)";
    
    $paramStmt = $pdo->prepare($paramSql);
    $paramStmt->execute([$recordDate, $workersCount, $jintieTotal, $distribution]);
    
    $pdo->commit();
    
    echo json_encode([
        'success' => true,
        'message' => "成功保存 {$savedCount} 条员工记录" . (isset($deletedCount) && $deletedCount > 0 ? "（覆盖了 {$deletedCount} 条旧记录）" : ""),
        'record_date' => $recordDate,
        'saved_count' => $savedCount,
        'deleted_count' => $deletedCount ?? 0
    ]);
    
} catch (Exception $e) {
    if (isset($pdo)) {
        $pdo->rollBack();
    }
    echo json_encode([
        'success' => false, 
        'message' => '保存失败: ' . $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine()
    ]);
}
?>
