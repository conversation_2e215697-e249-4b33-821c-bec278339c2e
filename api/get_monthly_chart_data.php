<?php
/**
 * API端点：获取指定年份的月度图表数据
 */

// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 处理OPTIONS请求
if (isset($_SERVER['REQUEST_METHOD']) && $_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

try {
    // 引入必要的文件
    require_once __DIR__ . '/../includes/config.php';
    require_once __DIR__ . '/api_stats_logger.php';
    
    // 获取年份参数
    $year = isset($_GET['year']) ? (int)$_GET['year'] : date('Y');
    
    // 验证年份范围（2019-2030）
    if ($year < 2019 || $year > 2030) {
        throw new Exception('年份参数无效，请选择2019-2030之间的年份');
    }
    
    // 获取月度统计数据
    $monthlyStats = getCurrentYearMonthlyStats($year);
    
    // 返回成功响应
    echo json_encode([
        'code' => 0,
        'message' => '获取数据成功',
        'data' => [
            'year' => $year,
            'monthly_stats' => $monthlyStats
        ]
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    // 记录错误日志
    error_log("获取月度图表数据失败: " . $e->getMessage());
    
    // 返回错误响应
    http_response_code(500);
    echo json_encode([
        'code' => -1,
        'message' => '获取数据失败: ' . $e->getMessage(),
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
}
?>
