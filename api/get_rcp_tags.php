<?php
require '../includes/config.php';

// 设置响应头为JSON
header('Content-Type: application/json');

// 检查是否提供了RCP柜ID
if (!isset($_GET['rcp_id']) || empty($_GET['rcp_id'])) {
    echo json_encode([
        'status' => 'error',
        'message' => '缺少RCP柜ID参数'
    ]);
    exit;
}

$rcpId = intval($_GET['rcp_id']);

try {
    // 查询RCP柜下的位号，只包括junction_id为空的记录
    $stmt = $pdo->prepare("
        SELECT id, code as tag_number, cable_name as tag_name, remark
        FROM terminal 
        WHERE rcp_id = ? AND (junction_id IS NULL OR junction_id = 0)
        ORDER BY code ASC
    ");
    $stmt->execute([$rcpId]);
    $tags = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // 返回成功响应
    echo json_encode([
        'status' => 'success',
        'data' => $tags
    ]);
} catch (PDOException $e) {
    // 记录错误
    error_log("获取RCP柜位号数据失败: " . $e->getMessage());
    
    // 返回错误响应
    echo json_encode([
        'status' => 'error',
        'message' => '获取位号数据失败: ' . $e->getMessage()
    ]);
}