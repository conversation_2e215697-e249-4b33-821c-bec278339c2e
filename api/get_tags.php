<?php
require '../includes/config.php';

// 设置响应头为JSON
header('Content-Type: application/json');

// 检查是否提供了分线箱ID
if (!isset($_GET['box_id']) || empty($_GET['box_id'])) {
    echo json_encode([
        'status' => 'error',
        'message' => '缺少分线箱ID参数'
    ]);
    exit;
}

$boxId = intval($_GET['box_id']);

try {
    // 查询该分线箱下的所有位号
    $stmt = $pdo->prepare("
        SELECT id, code as tag_number, cable_name as tag_name, remark
        FROM terminal 
        WHERE junction_id = ?
        ORDER BY code ASC
    ");
    $stmt->execute([$boxId]);
    $tags = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // 返回成功响应
    echo json_encode([
        'status' => 'success',
        'data' => $tags
    ]);
} catch (PDOException $e) {
    // 记录错误
    error_log("获取分线箱位号数据失败: " . $e->getMessage());
    
    // 返回错误响应
    echo json_encode([
        'status' => 'error',
        'message' => '获取位号数据失败: ' . $e->getMessage()
    ]);
}