<?php
// 检查session是否已经启动
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}
require_once '../includes/config.php';

// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Cache-Control: no-cache, must-revalidate');

// 暂时注释掉用户登录检查，专注于解决JSON问题
/*
if(!isset($_SESSION['user_id']) && !isset($_SESSION['user']) && !isset($_SESSION['username'])) {
    echo json_encode([
        'success' => false,
        'message' => '用户未登录'
    ], JSON_UNESCAPED_UNICODE);
    exit;
}
*/

try {
    // 处理获取岗位金额设置的请求
    if(isset($_POST['action']) && $_POST['action'] === 'get_dept_settings') {
        // 从数据库获取岗位金额设置
        $stmt = $pdo->query("SELECT * FROM jintie_dept_settings ORDER BY id DESC LIMIT 1");
        $settings = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$settings) {
            // 如果没有设置，返回默认值
            $settings = [
                'jintie_total' => 10000,
                'monitoring_base_amount' => 2500,
                'cb26_base_amount' => 400
            ];
        }
        
        // 返回成功响应
        echo json_encode([
            'success' => true,
            'settings' => $settings
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 处理保存岗位金额设置的请求
    if(isset($_POST['action']) && $_POST['action'] === 'save_dept_settings') {
        $jintieTotal = floatval($_POST['jintie_total'] ?? 10000);
        $monitoringBaseAmount = floatval($_POST['monitoring_base_amount'] ?? 2500);
        $cb26BaseAmount = floatval($_POST['cb26_base_amount'] ?? 400);
        
        // 删除所有现有记录，然后插入新记录（确保只有一条记录）
        $pdo->exec("DELETE FROM jintie_dept_settings");
        $stmt = $pdo->prepare("INSERT INTO jintie_dept_settings (jintie_total, monitoring_base_amount, cb26_base_amount) VALUES (?, ?, ?)");
        $result = $stmt->execute([$jintieTotal, $monitoringBaseAmount, $cb26BaseAmount]);
        
        if ($result) {
            // 同时更新SESSION中的津贴总额
            $_SESSION['jintie_params']['jintie_total'] = $jintieTotal;
            
            // 返回成功响应
            echo json_encode([
                'success' => true,
                'message' => '岗位金额设置已保存'
            ], JSON_UNESCAPED_UNICODE);
        } else {
            echo json_encode([
                'success' => false,
                'message' => '保存失败'
            ], JSON_UNESCAPED_UNICODE);
        }
        exit;
    }
    
    // 如果没有匹配的操作
    echo json_encode([
        'success' => false,
        'message' => '无效的操作'
    ], JSON_UNESCAPED_UNICODE);
    
} catch (PDOException $e) {
    error_log("岗位金额设置操作失败: " . $e->getMessage());
    
    // 返回错误响应
    echo json_encode([
        'success' => false,
        'message' => '数据库操作失败: ' . $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
?>
