<?php
// 启动会话
session_start();

// 设置响应头
header('Content-Type: application/json; charset=utf-8');

// 使用config.php的数据库配置
require_once '../includes/config.php';

// 检查是否登录
if(!isset($_SESSION['user'])) {
    echo json_encode(['success' => false, 'message' => '未登录']);
    exit;
}

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => '方法不允许']);
    exit;
}

try {
    $recordDate = $_POST['record_date'] ?? '';
    
    // 验证日期格式
    if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $recordDate)) {
        echo json_encode(['success' => false, 'message' => '无效的日期格式']);
        exit;
    }
    
    // 检查表是否存在
    $tableCheck = $pdo->query("SHOW TABLES LIKE 'jintie_records'");
    if ($tableCheck->rowCount() == 0) {
        echo json_encode(['success' => false, 'message' => '历史记录表不存在']);
        exit;
    }
    
    // 开始事务
    $pdo->beginTransaction();
    
    // 先检查是否存在该日期的记录
    $checkSql = "SELECT COUNT(*) as count FROM jintie_records WHERE record_date = ?";
    $checkStmt = $pdo->prepare($checkSql);
    $checkStmt->execute([$recordDate]);
    $count = $checkStmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    if ($count == 0) {
        $pdo->rollBack();
        echo json_encode(['success' => false, 'message' => '该日期的记录不存在']);
        exit;
    }
    
    // 删除津贴记录
    $deleteSql = "DELETE FROM jintie_records WHERE record_date = ?";
    $deleteStmt = $pdo->prepare($deleteSql);
    $result = $deleteStmt->execute([$recordDate]);
    
    if (!$result) {
        $pdo->rollBack();
        echo json_encode(['success' => false, 'message' => '删除记录失败']);
        exit;
    }
    
    $deletedCount = $deleteStmt->rowCount();
    
    // 同时删除相关的参数记录（如果存在jintie_params表）
    $paramsTableCheck = $pdo->query("SHOW TABLES LIKE 'jintie_params'");
    if ($paramsTableCheck->rowCount() > 0) {
        $deleteParamsSql = "DELETE FROM jintie_params WHERE record_date = ?";
        $deleteParamsStmt = $pdo->prepare($deleteParamsSql);
        $deleteParamsStmt->execute([$recordDate]);
    }
    
    // 提交事务
    $pdo->commit();
    
    echo json_encode([
        'success' => true,
        'message' => "成功删除 {$recordDate} 的 {$deletedCount} 条记录",
        'deleted_count' => $deletedCount,
        'record_date' => $recordDate
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    // 回滚事务
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }
    
    error_log("删除历史记录失败: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false, 
        'message' => '删除历史记录失败: ' . $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine()
    ], JSON_UNESCAPED_UNICODE);
}
?>
