<?php
// 设置响应头
header('Content-Type: application/json; charset=utf-8');

// 使用config.php的数据库配置
require_once '../includes/config.php';

// 检查是否登录
if(!isset($_SESSION['user'])) {
    echo json_encode(['success' => false, 'message' => '未登录']);
    exit;
}

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => '方法不允许']);
    exit;
}

try {

    // 获取POST数据
    $rawInput = file_get_contents('php://input');
    $input = json_decode($rawInput, true);

    if (!$input) {
        echo json_encode([
            'success' => false,
            'message' => '无效的JSON数据',
            'raw_input' => substr($rawInput, 0, 200)
        ]);
        exit;
    }

try {
    // 检查数据库表是否存在
    $checkTables = $pdo->query("SHOW TABLES LIKE 'jintie_records'");
    if ($checkTables->rowCount() == 0) {
        echo json_encode(['success' => false, 'message' => '数据库表 jintie_records 不存在，请先运行 init_database.php']);
        exit;
    }

    $checkParams = $pdo->query("SHOW TABLES LIKE 'jintie_params'");
    if ($checkParams->rowCount() == 0) {
        echo json_encode(['success' => false, 'message' => '数据库表 jintie_params 不存在，请先运行 init_database.php']);
        exit;
    }

    $pdo->beginTransaction();

    // 获取基础参数
    $recordDate = $input['record_date'] ?? date('Y-m-d');
    $monthYear = date('Y-m', strtotime($recordDate));
    $workersCount = $input['workers_count'] ?? 15;
    $jintieTotal = $input['jintie_total'] ?? 10000;
    $distribution = $input['distribution'] ?? 0;
    
    // 保存或更新参数
    $paramSql = "INSERT INTO jintie_params (record_date, workers_count, jintie_total, distribution) 
                 VALUES (?, ?, ?, ?) 
                 ON DUPLICATE KEY UPDATE 
                 workers_count = VALUES(workers_count),
                 jintie_total = VALUES(jintie_total),
                 distribution = VALUES(distribution),
                 updated_at = CURRENT_TIMESTAMP";
    
    $paramStmt = $pdo->prepare($paramSql);
    $paramStmt->execute([$recordDate, $workersCount, $jintieTotal, $distribution]);
    
    // 删除该日期的旧记录
    $deleteSql = "DELETE FROM jintie_records WHERE record_date = ?";
    $deleteStmt = $pdo->prepare($deleteSql);
    $deleteStmt->execute([$recordDate]);
    
    // 准备插入员工数据的SQL
    $insertSql = "INSERT INTO jintie_records (
        record_date, month_year, name, dept, fixed_amount, floating_amount,
        fixed_actual, floating_actual, days, deduction, personal_bonus,
        general_bonus, total_amount, is_fixed_bonus, floating_ratio, remark
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

    $insertStmt = $pdo->prepare($insertSql);

    // 插入员工数据
    $employees = $input['employees'] ?? [];
    $savedCount = 0;

    foreach ($employees as $employee) {
        $insertStmt->execute([
            $recordDate,
            $monthYear,
            $employee['name'] ?? '',
            $employee['dept'] ?? '',
            $employee['fixed'] ?? 0,
            $employee['floating'] ?? 0,
            $employee['fixed_actual'] ?? 0,
            $employee['floating_actual'] ?? 0,
            $employee['days'] ?? 0,
            $employee['deduction'] ?? 0,
            $employee['personal_bonus'] ?? 0,
            $employee['general_bonus'] ?? 0,
            $employee['total'] ?? 0,
            $employee['is_fixed_bonus'] ?? 0,
            $employee['floating_ratio'] ?? 1.0,
            $employee['remark'] ?? ''
        ]);
        $savedCount++;
    }
    
    $pdo->commit();
    
    echo json_encode([
        'success' => true, 
        'message' => "成功保存 {$savedCount} 条员工记录",
        'record_date' => $recordDate,
        'saved_count' => $savedCount
    ]);
    
} catch (Exception $e) {
    $pdo->rollBack();
    error_log("保存津贴数据失败: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false, 
        'message' => '保存失败: ' . $e->getMessage()
    ]);
}
?>
