# 微信小程序登录功能使用指南

## 功能概述

本系统为您的微信小程序提供了完整的微信登录功能，支持：

- 微信账号一键登录
- 与现有用户系统绑定
- 安全的Token管理
- 完整的权限控制
- 后台绑定关系管理

## 安装配置

### 1. 数据库初始化

运行数据库初始化脚本：

```bash
php database/init_wechat_login.php
```

或者手动执行SQL文件：

```sql
-- 导入 database/create_wechat_tables.sql
```

### 2. 配置微信小程序信息

在 `includes/config.php` 中设置您的微信小程序信息：

```php
// 微信小程序配置
define('WECHAT_MINI_APPID', 'your_actual_appid_here');     // 您的微信小程序AppID
define('WECHAT_MINI_SECRET', 'your_actual_secret_here');   // 您的微信小程序Secret
```

### 3. 微信小程序端集成

将 `miniprogram_examples/` 目录下的示例代码集成到您的微信小程序中：

1. 复制 `login.js` 到您的 `utils/` 目录
2. 参考 `login_page.wxml` 和 `login_page.js` 创建登录页面
3. 根据需要调整样式和交互逻辑

## API接口说明

### 基础URL
```
https://your-domain.com/api/wechat_login_api.php
```

### 1. 微信登录
```javascript
POST /api/wechat_login_api.php
{
  "action": "wechat_login",
  "code": "微信授权码",
  "userInfo": {
    "nickName": "用户昵称",
    "avatarUrl": "头像URL",
    // ... 其他用户信息
  }
}
```

**响应：**
- 已绑定账号：返回登录成功和token
- 未绑定账号：返回需要绑定的状态

### 2. 绑定系统账号
```javascript
POST /api/wechat_login_api.php
{
  "action": "bind_account",
  "openid": "微信openid",
  "wechat_user_id": "微信用户ID",
  "username": "系统用户名",
  "password": "系统密码"
}
```

### 3. 验证Token
```javascript
POST /api/wechat_login_api.php
{
  "action": "verify_token",
  "token": "用户token"
}
```

### 4. 解绑账号
```javascript
POST /api/wechat_login_api.php
{
  "action": "unbind_account",
  "token": "用户token"
}
```

## 后台管理

### 微信绑定管理

微信绑定管理功能已集成到用户管理页面中，访问 `pages/users.php` 可以：

- 查看所有用户的微信绑定状态
- 解绑用户的微信账号
- 查看绑定时间和微信昵称
- 在删除用户时自动解除微信绑定

### 权限要求

- 只有管理员（admin角色）可以访问用户管理页面
- 用户可以解绑自己的微信账号
- 普通用户可以在小程序中自行绑定和解绑

## 安全机制

### 1. Token管理
- 使用安全的随机Token
- Token有效期7天，可自动续期
- 支持强制过期和清理

### 2. 登录日志
- 记录所有登录、绑定、解绑操作
- 包含IP地址、用户代理等信息
- 便于安全审计

### 3. 权限验证
- 基于现有用户角色系统
- 支持一站人员身份验证
- API接口统一权限控制

## 数据表结构

### wechat_users
存储微信用户基本信息

### user_wechat_bindings
存储用户与微信账号的绑定关系

### wechat_tokens
存储登录Token信息

### wechat_login_logs
存储登录操作日志

## 使用流程

### 用户首次使用
1. 在微信小程序中点击"微信登录"
2. 授权获取微信信息
3. 系统提示需要绑定账号
4. 输入现有系统账号密码进行绑定
5. 绑定成功，获得登录状态

### 已绑定用户
1. 在微信小程序中点击"微信登录"
2. 自动识别已绑定账号
3. 直接登录成功

### 管理员操作
1. 在用户管理页面查看所有用户的微信绑定状态
2. 可以强制解绑用户的微信账号
3. 删除用户时自动解除微信绑定
4. 查看登录统计和日志

## 常见问题

### Q: 如何获取微信小程序的AppID和Secret？
A: 登录微信公众平台，在小程序管理后台的"开发"-"开发设置"中可以找到。

### Q: 用户可以绑定多个微信账号吗？
A: 不可以，一个系统账号只能绑定一个微信账号。

### Q: 如何处理用户换手机的情况？
A: 微信的openid是唯一的，换手机不影响登录。如果需要换绑其他微信，需要先解绑再重新绑定。

### Q: Token过期了怎么办？
A: 系统会自动检测Token有效性，过期后会提示重新登录。

## 技术支持

如有问题，请检查：
1. 数据库表是否正确创建
2. 微信小程序配置是否正确
3. API接口是否可以正常访问
4. 服务器PHP版本是否支持（需要PHP 7.4+）

## 更新日志

### v1.0.0
- 初始版本发布
- 支持微信登录和账号绑定
- 完整的后台管理功能
- 安全的Token管理机制
