# 意见反馈系统对接文档（简化版）

## 系统概述

意见反馈系统是一个简洁高效的用户反馈收集和管理平台，支持微信小程序用户提交反馈，管理员在后台进行查看和处理。

### 主要功能
- 用户提交各类反馈（建议、问题反馈、功能需求、其他）
- 管理员查看和管理反馈
- 简化的状态管理（未读、已读、已处理）
- 自动标记已读功能
- 一键处理功能
- 统计分析功能

### 功能特点
- **简化流程**：去除复杂的优先级和回复功能
- **自动化**：查看详情时自动标记为已读
- **高效处理**：一键标记为已处理
- **清晰统计**：直观的数据统计面板

## 1. 数据库设计

### 1.1 主表结构（简化版）

```sql
-- 意见反馈主表（简化版）
CREATE TABLE `feedback` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '反馈ID',
  `user_id` int(11) DEFAULT NULL COMMENT '用户ID（如果已登录）',
  `wechat_openid` varchar(100) DEFAULT NULL COMMENT '微信OpenID',
  `username` varchar(50) DEFAULT NULL COMMENT '用户名（冗余字段，便于查询）',
  `real_name` varchar(50) DEFAULT NULL COMMENT '真实姓名（冗余字段）',
  `feedback_type` varchar(20) NOT NULL COMMENT '反馈类型：建议、问题反馈、功能需求、其他',
  `content` text NOT NULL COMMENT '反馈内容',
  `contact_info` varchar(200) DEFAULT NULL COMMENT '联系方式（可选）',
  `status` enum('unread','read','processed') DEFAULT 'unread' COMMENT '处理状态：未读、已读、已处理',
  `read_at` timestamp NULL DEFAULT NULL COMMENT '已读时间',
  `processed_at` timestamp NULL DEFAULT NULL COMMENT '处理时间',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_feedback_type` (`feedback_type`),
  KEY `idx_wechat_openid` (`wechat_openid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='意见反馈表（简化版）';
```

### 1.2 状态说明

| 状态 | 英文值 | 说明 | 触发条件 |
|------|--------|------|----------|
| 未读 | unread | 用户刚提交的反馈 | 用户提交反馈时的初始状态 |
| 已读 | read | 管理员已查看但未处理 | 管理员点击查看详情时自动触发 |
| 已处理 | processed | 管理员已处理完成 | 管理员手动标记为已处理 |

### 1.3 重要技术说明

⚠️ **MySQL保留字处理**：由于 `read` 是MySQL的保留字，在SQL查询中需要特别处理：

```sql
-- 正确的查询方式（使用反引号）
SELECT
    SUM(CASE WHEN status = 'read' THEN 1 ELSE 0 END) as `read`
FROM feedback;

-- 错误的查询方式（会导致语法错误）
SELECT
    SUM(CASE WHEN status = 'read' THEN 1 ELSE 0 END) as read
FROM feedback;
```

**开发注意事项**：
- 在编写SQL查询时，涉及 `read` 字段别名需要用反引号包围
- 建议在所有字段别名中统一使用反引号以避免保留字冲突
- 如果使用ORM框架，通常会自动处理此类问题

## 2. API接口文档

### 2.1 基础信息

- **接口地址**：`/api/feedback_api.php`
- **请求方式**：GET/POST
- **返回格式**：JSON
- **字符编码**：UTF-8

### 2.2 提交反馈接口

**接口说明**：用户提交意见反馈

**请求方式**：POST

**请求参数**：
```json
{
  "action": "submit_feedback",
  "feedback_type": "建议",
  "content": "反馈内容",
  "contact_info": "联系方式（可选）",
  "wechat_auth": false,
  "user_info": null
}
```

**参数说明**：

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| action | string | 是 | 固定值：submit_feedback |
| feedback_type | string | 是 | 反馈类型：建议、问题反馈、功能需求、其他 |
| content | string | 是 | 反馈内容，最大500字 |
| contact_info | string | 否 | 联系方式 |
| wechat_auth | boolean | 否 | 是否微信授权登录 |
| user_info | object | 否 | 用户信息（微信授权时提供） |

**返回示例**：
```json
{
  "success": true,
  "message": "反馈提交成功",
  "data": {
    "feedback_id": 123,
    "created_at": "2024-01-01 12:00:00"
  }
}
```

### 2.3 获取反馈列表接口（管理员）

**接口说明**：管理员获取反馈列表

**请求方式**：GET

**请求参数**：

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| action | string | 是 | 固定值：get_feedback_list |
| page | int | 否 | 页码，默认1 |
| limit | int | 否 | 每页数量，默认20，最大100 |
| status | string | 否 | 状态筛选：unread、read、processed |
| type | string | 否 | 类型筛选：建议、问题反馈、功能需求、其他 |
| search | string | 否 | 搜索关键词 |

**返回示例**：
```json
{
  "success": true,
  "data": {
    "list": [
      {
        "id": 1,
        "feedback_type": "建议",
        "content": "反馈内容",
        "contact_info": "联系方式",
        "status": "unread",
        "status_text": "未读",
        "user_info": {
          "id": 1,
          "username": "用户名",
          "real_name": "真实姓名"
        },
        "created_at": "2024-01-01 12:00:00",
        "updated_at": "2024-01-01 12:00:00"
      }
    ],
    "total": 100,
    "page": 1,
    "limit": 20,
    "total_pages": 5
  }
}
```

### 2.4 获取反馈详情接口（管理员）

**接口说明**：管理员获取反馈详情，自动标记为已读

**请求方式**：GET

**请求参数**：

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| action | string | 是 | 固定值：get_feedback_detail |
| feedback_id | int | 是 | 反馈ID |

**返回示例**：
```json
{
  "success": true,
  "data": {
    "id": 1,
    "feedback_type": "建议",
    "content": "反馈内容",
    "contact_info": "联系方式",
    "status": "read",
    "status_text": "已读",
    "read_at": "2024-01-01 12:05:00",
    "processed_at": null,
    "user_info": {
      "id": 1,
      "username": "用户名",
      "real_name": "真实姓名"
    },
    "created_at": "2024-01-01 12:00:00",
    "updated_at": "2024-01-01 12:05:00"
  }
}
```

### 2.5 标记为已处理接口（管理员）

**接口说明**：管理员标记反馈为已处理

**请求方式**：POST

**请求参数**：
```json
{
  "action": "update_feedback",
  "feedback_id": 1,
  "action_type": "mark_processed"
}
```

**参数说明**：

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| action | string | 是 | 固定值：update_feedback |
| feedback_id | int | 是 | 反馈ID |
| action_type | string | 是 | 固定值：mark_processed |

**返回示例**：
```json
{
  "success": true,
  "message": "反馈已标记为已处理",
  "data": {
    "feedback_id": 1,
    "updated_at": "2024-01-01 12:10:00"
  }
}
```

### 2.6 获取统计数据接口（管理员）

**接口说明**：管理员获取反馈统计数据

**请求方式**：GET

**请求参数**：

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| action | string | 是 | 固定值：get_feedback_stats |

**返回示例**：
```json
{
  "success": true,
  "data": {
    "total": 100,
    "unread": 20,
    "read": 30,
    "processed": 50,
    "by_type": {
      "建议": 40,
      "问题反馈": 30,
      "功能需求": 20,
      "其他": 10
    },
    "by_status": {
      "unread": 20,
      "read": 30,
      "processed": 50
    },
    "recent_7_days": 15,
    "recent_30_days": 45
  }
}
```

## 3. 微信小程序集成

### 3.1 提交反馈示例代码

```javascript
// 提交反馈
submitFeedback() {
  const data = {
    action: 'submit_feedback',
    feedback_type: this.data.selectedType,
    content: this.data.content,
    contact_info: this.data.contact,
    wechat_auth: false,
    user_info: null
  };

  wx.request({
    url: 'https://your-domain.com/api/feedback_api.php',
    method: 'POST',
    header: {
      'content-type': 'application/json'
    },
    data: data,
    success: (res) => {
      if (res.data.success) {
        wx.showToast({
          title: '提交成功',
          icon: 'success'
        });
      } else {
        wx.showToast({
          title: res.data.message,
          icon: 'none'
        });
      }
    },
    fail: () => {
      wx.showToast({
        title: '网络错误',
        icon: 'none'
      });
    }
  });
}
```

### 3.2 反馈类型选择

```javascript
// 反馈类型数组
const feedbackTypes = [
  { value: '建议', label: '建议' },
  { value: '问题反馈', label: '问题反馈' },
  { value: '功能需求', label: '功能需求' },
  { value: '其他', label: '其他' }
];
```

## 4. 后台管理功能

### 4.1 访问地址

- **管理页面**：`/pages/feedback_management.php`
- **权限要求**：仅管理员可访问

### 4.2 主要功能

1. **统计面板**
   - 总反馈数
   - 未读数量
   - 已读数量
   - 已处理数量
   - 最近7天数量

2. **反馈列表**
   - 卡片式展示
   - 支持分页
   - 状态筛选
   - 类型筛选
   - 关键词搜索

3. **反馈详情**
   - 查看完整反馈内容
   - 查看用户信息
   - 查看时间信息
   - 自动标记为已读

4. **处理操作**
   - 一键标记为已处理
   - 简化的操作流程

### 4.3 操作流程

1. **查看反馈**：点击反馈卡片查看详情，自动标记为已读
2. **处理反馈**：在详情页面点击"标记为已处理"
3. **筛选查看**：使用状态和类型筛选器快速定位反馈

## 5. 权限控制

### 5.1 管理员权限

- 所有API接口都需要管理员权限
- 基于session的权限验证
- 自动检查用户角色为'admin'

### 5.2 安全措施

- 输入数据验证
- SQL注入防护
- XSS攻击防护
- 会话安全管理

## 6. 部署说明

### 6.1 环境要求

- PHP 7.4+
- MySQL 5.7+
- 支持session的Web服务器

### 6.2 部署步骤

1. 上传文件到服务器
2. 执行数据库建表SQL
3. 配置数据库连接
4. 设置文件权限
5. 测试功能

### 6.3 测试验证

1. **数据库连接测试**
   ```php
   // 测试数据库连接
   try {
       $pdo = new PDO($dsn, $username, $password);
       echo "数据库连接成功";
   } catch (PDOException $e) {
       echo "连接失败: " . $e->getMessage();
   }
   ```

2. **SQL语法验证**
   ```sql
   -- 测试状态统计查询
   SELECT
       COUNT(*) as total,
       SUM(CASE WHEN status = 'unread' THEN 1 ELSE 0 END) as unread,
       SUM(CASE WHEN status = 'read' THEN 1 ELSE 0 END) as `read`,
       SUM(CASE WHEN status = 'processed' THEN 1 ELSE 0 END) as processed
   FROM feedback;
   ```

3. **功能测试清单**
   - ✅ 测试反馈提交功能
   - ✅ 测试管理员查看功能
   - ✅ 测试状态自动更新
   - ✅ 测试权限控制
   - ✅ 测试SQL查询语法
   - ✅ 测试API错误处理

## 7. 常见问题

### 7.1 SQL语法错误（SQLSTATE[42000]: 1064）

**问题描述**：API返回SQL语法错误，特别是涉及 `read` 字段的查询

**原因分析**：`read` 是MySQL保留字，需要用反引号包围

**解决方案**：
```sql
-- 错误写法
SELECT status, COUNT(*) as read FROM feedback WHERE status = 'read';

-- 正确写法
SELECT status, COUNT(*) as `read` FROM feedback WHERE status = 'read';
```

**预防措施**：
- 所有字段别名使用反引号包围
- 避免使用MySQL保留字作为字段名
- 使用IDE的SQL语法检查功能

### 7.2 API调用失败

- 检查请求参数格式
- 确认管理员权限
- 查看服务器错误日志
- 验证Content-Type设置

### 7.3 状态未自动更新

- 确认API调用成功
- 检查数据库连接
- 验证session状态
- 检查数据库事务提交

### 7.4 权限验证失败

- 确认用户已登录
- 检查用户角色为admin
- 验证session有效性
- 检查session配置

### 7.5 前端显示异常

- 检查JavaScript控制台错误
- 验证API返回数据格式
- 确认jQuery版本兼容性
- 检查CSS样式冲突

---

**文档版本**：v2.0（简化版）  
**更新时间**：2024-01-01  
**维护人员**：系统管理员
