# 用户管理API接口文档

## 概述

用户管理API提供了完整的用户管理功能，专门用于微信小程序的管理员用户管理页面。包括用户的增删改查、角色管理、密码修改、**微信绑定管理**等功能。

## 基础信息

### API端点
```
/api/user_management_api.php
```

### 认证方式
所有接口都需要管理员权限，通过Token进行身份验证。

### 请求格式
- **Content-Type**: `application/json`
- **认证**: 在请求体中包含 `token` 字段

### 响应格式
所有接口返回JSON格式数据：
```json
{
    "success": true|false,
    "message": "操作结果描述",
    "data": {} // 具体数据（成功时）
}
```

## API接口

### 1. 获取用户列表

**请求方式**: GET

**URL**: `/api/user_management_api.php?action=list`

**参数**:
- `token` (必需): 管理员Token
- `page` (可选): 页码，默认为1
- `search` (可选): 搜索关键词，支持用户名和真实姓名
- `role` (可选): 角色筛选，可选值：admin, manager, user

**示例请求**:
```javascript
const response = await fetch('/api/user_management_api.php?action=list&token=your_token&page=1&search=张三');
```

**成功响应**:
```json
{
    "success": true,
    "data": {
        "users": [
            {
                "id": 1,
                "username": "admin",
                "real_name": "管理员",
                "role": "admin",
                "role_text": "管理员",
                "is_station_staff": 1,
                "station_text": "一站人员",
                "wechat_bindings": 1,
                "has_wechat": true,
                "wechat_info": {
                    "nickname": "微信昵称",
                    "bind_time": "2024-01-15 10:30:00",
                    "bind_time_formatted": "01-15 10:30"
                }
            }
        ],
        "pagination": {
            "current_page": 1,
            "total_items": 10,
            "items_per_page": 20,
            "total_pages": 1
        }
    }
}
```

### 2. 添加用户

**请求方式**: POST

**URL**: `/api/user_management_api.php`

**请求体**:
```json
{
    "action": "add",
    "token": "your_admin_token",
    "username": "newuser",
    "password": "password123",
    "real_name": "新用户",
    "role": "user",
    "is_station_staff": false
}
```

**成功响应**:
```json
{
    "success": true,
    "message": "用户添加成功",
    "data": {
        "user_id": 123
    }
}
```

### 3. 删除用户（自动解绑微信）

**请求方式**: POST

**URL**: `/api/user_management_api.php`

**请求体**:
```json
{
    "action": "delete",
    "token": "your_admin_token",
    "user_id": 123
}
```

**成功响应**:
```json
{
    "success": true,
    "message": "用户删除成功（已自动解除微信绑定）"
}
```

**说明**: 删除用户时会自动解除该用户的微信绑定，并删除相关的登录Token。

### 4. 解绑用户微信 🆕

**请求方式**: POST

**URL**: `/api/user_management_api.php`

**请求体**:
```json
{
    "action": "unbind_wechat",
    "token": "your_admin_token",
    "user_id": 123
}
```

**成功响应**:
```json
{
    "success": true,
    "message": "微信绑定解除成功"
}
```

**错误响应示例**:
```json
{
    "success": false,
    "message": "该用户没有微信绑定"
}
```

**说明**: 
- 解绑微信会将绑定状态设置为非活跃，并删除相关的登录Token
- 用户需要重新绑定才能使用微信登录
- 会记录操作日志

### 5. 修改用户密码

**请求方式**: POST

**URL**: `/api/user_management_api.php`

**请求体**:
```json
{
    "action": "update_password",
    "token": "your_admin_token",
    "user_id": 123,
    "new_password": "newpassword123"
}
```

**成功响应**:
```json
{
    "success": true,
    "message": "密码修改成功"
}
```

### 6. 修改用户真实姓名

**请求方式**: POST

**URL**: `/api/user_management_api.php`

**请求体**:
```json
{
    "action": "update_real_name",
    "token": "your_admin_token",
    "user_id": 123,
    "real_name": "新的真实姓名"
}
```

**成功响应**:
```json
{
    "success": true,
    "message": "真实姓名修改成功"
}
```

### 7. 修改用户角色

**请求方式**: POST

**URL**: `/api/user_management_api.php`

**请求体**:
```json
{
    "action": "update_role",
    "token": "your_admin_token",
    "user_id": 123,
    "role": "manager",
    "is_station_staff": true
}
```

**成功响应**:
```json
{
    "success": true,
    "message": "用户角色修改成功"
}
```

### 8. 获取用户详情

**请求方式**: GET

**URL**: `/api/user_management_api.php?action=detail`

**参数**:
- `token` (必需): 管理员Token
- `user_id` (必需): 用户ID

**示例请求**:
```javascript
const response = await fetch('/api/user_management_api.php?action=detail&token=your_token&user_id=123');
```

**成功响应**:
```json
{
    "success": true,
    "data": {
        "id": 123,
        "username": "testuser",
        "real_name": "测试用户",
        "role": "user",
        "role_text": "普通用户",
        "is_station_staff": 0,
        "station_text": "非一站人员",
        "wechat_bindings": 1,
        "has_wechat": true,
        "wechat_info": {
            "nickname": "微信昵称",
            "bind_time": "2024-01-15 10:30:00",
            "bind_time_formatted": "01-15 10:30"
        }
    }
}
```

## 微信绑定管理功能

### 功能特点

1. **自动解绑**: 删除用户时自动解除微信绑定
2. **手动解绑**: 提供独立的解绑接口
3. **绑定信息**: 显示微信昵称和绑定时间
4. **状态管理**: 通过 `is_active` 字段管理绑定状态
5. **日志记录**: 记录所有绑定相关操作

### 微信绑定状态说明

- `has_wechat`: 布尔值，表示是否有微信绑定
- `wechat_bindings`: 数字，活跃绑定的数量
- `wechat_info`: 对象，包含微信绑定的详细信息
  - `nickname`: 微信昵称
  - `bind_time`: 完整绑定时间
  - `bind_time_formatted`: 格式化的绑定时间（月-日 时:分）

## 错误处理

### 常见错误码
- `403`: 权限不足，Token无效或非管理员
- `404`: 未找到请求的操作
- `500`: 服务器内部错误

### 错误响应示例
```json
{
    "success": false,
    "message": "权限不足，只有管理员可以访问"
}
```

## 使用示例

### JavaScript示例

```javascript
class UserManagementAPI {
    constructor(baseUrl, token) {
        this.baseUrl = baseUrl;
        this.token = token;
    }

    async getUserList(page = 1, search = '', role = '') {
        const params = new URLSearchParams({
            action: 'list',
            token: this.token,
            page: page
        });

        if (search) params.append('search', search);
        if (role) params.append('role', role);

        const response = await fetch(`${this.baseUrl}?${params}`);
        return await response.json();
    }

    async addUser(userData) {
        const response = await fetch(this.baseUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                action: 'add',
                token: this.token,
                ...userData
            })
        });
        return await response.json();
    }

    async deleteUser(userId) {
        const response = await fetch(this.baseUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                action: 'delete',
                token: this.token,
                user_id: userId
            })
        });
        return await response.json();
    }

    // 🆕 解绑用户微信
    async unbindWechat(userId) {
        const response = await fetch(this.baseUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                action: 'unbind_wechat',
                token: this.token,
                user_id: userId
            })
        });
        return await response.json();
    }

    async updatePassword(userId, newPassword) {
        const response = await fetch(this.baseUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                action: 'update_password',
                token: this.token,
                user_id: userId,
                new_password: newPassword
            })
        });
        return await response.json();
    }

    async updateRole(userId, role, isStationStaff) {
        const response = await fetch(this.baseUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                action: 'update_role',
                token: this.token,
                user_id: userId,
                role: role,
                is_station_staff: isStationStaff
            })
        });
        return await response.json();
    }
}

// 使用示例
const userAPI = new UserManagementAPI('/api/user_management_api.php', 'your_admin_token');

// 获取用户列表
const userList = await userAPI.getUserList(1, '张三');

// 添加用户
const result = await userAPI.addUser({
    username: 'newuser',
    password: 'password123',
    real_name: '新用户',
    role: 'user',
    is_station_staff: false
});

// 解绑用户微信
const unbindResult = await userAPI.unbindWechat(123);

// 删除用户（自动解绑微信）
const deleteResult = await userAPI.deleteUser(123);
```

### 微信小程序示例

```javascript
// 在微信小程序中使用
const app = getApp();

Page({
    data: {
        users: [],
        loading: false
    },

    async loadUsers() {
        this.setData({ loading: true });

        try {
            const token = app.globalData.adminToken;
            const response = await wx.request({
                url: `${app.globalData.apiBase}/user_management_api.php?action=list&token=${token}`,
                method: 'GET'
            });

            if (response.data.success) {
                this.setData({
                    users: response.data.data.users
                });
            } else {
                wx.showToast({
                    title: response.data.message,
                    icon: 'error'
                });
            }
        } catch (error) {
            wx.showToast({
                title: '加载失败',
                icon: 'error'
            });
        } finally {
            this.setData({ loading: false });
        }
    },

    async unbindWechat(userId, username) {
        const result = await wx.showModal({
            title: '确认解绑',
            content: `确定要解除用户 ${username} 的微信绑定吗？`
        });

        if (!result.confirm) return;

        try {
            const token = app.globalData.adminToken;
            const response = await wx.request({
                url: `${app.globalData.apiBase}/user_management_api.php`,
                method: 'POST',
                header: {
                    'content-type': 'application/json'
                },
                data: {
                    action: 'unbind_wechat',
                    token: token,
                    user_id: userId
                }
            });

            if (response.data.success) {
                wx.showToast({
                    title: '解绑成功',
                    icon: 'success'
                });
                this.loadUsers(); // 刷新列表
            } else {
                wx.showToast({
                    title: response.data.message,
                    icon: 'error'
                });
            }
        } catch (error) {
            wx.showToast({
                title: '操作失败',
                icon: 'error'
            });
        }
    },

    async deleteUser(userId, username) {
        const result = await wx.showModal({
            title: '确认删除',
            content: `确定要删除用户 ${username} 吗？此操作不可恢复！`
        });

        if (!result.confirm) return;

        try {
            const token = app.globalData.adminToken;
            const response = await wx.request({
                url: `${app.globalData.apiBase}/user_management_api.php`,
                method: 'POST',
                header: {
                    'content-type': 'application/json'
                },
                data: {
                    action: 'delete',
                    token: token,
                    user_id: userId
                }
            });

            if (response.data.success) {
                wx.showToast({
                    title: '删除成功',
                    icon: 'success'
                });
                this.loadUsers(); // 刷新列表
            } else {
                wx.showToast({
                    title: response.data.message,
                    icon: 'error'
                });
            }
        } catch (error) {
            wx.showToast({
                title: '删除失败',
                icon: 'error'
            });
        }
    }
});
```

## 注意事项

1. **权限验证**: 所有接口都需要管理员权限，请确保Token有效
2. **参数验证**: 请确保传入的参数符合要求，特别是用户名长度、密码强度等
3. **错误处理**: 请妥善处理API返回的错误信息
4. **安全性**: 不要在前端暴露管理员Token，建议通过安全的方式获取和存储
5. **微信绑定**: 解绑微信后，用户需要重新绑定才能使用微信登录功能
6. **操作确认**: 删除用户和解绑微信等重要操作建议添加确认提示

## 更新日志

### v1.1.0 (2024-01-15)
- 🆕 新增解绑用户微信接口 (`unbind_wechat`)
- 🔧 优化删除用户接口，自动解绑微信并记录日志
- 🔧 用户列表和详情接口增加微信绑定详细信息
- 📝 完善接口文档和使用示例
