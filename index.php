<?php
require 'includes/config.php'; // 确保session_start在config.php中

// 检查用户是否已登录
if(!isset($_SESSION['user'])) {
    header("Location: pages/login.php");
    exit;
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>平台常用工具微信小程序后台管理系统</title>

    <!-- 预加载字体文件 -->
    <link rel="preload" href="./assets/webfonts/fa-solid-900.woff2" as="font" type="font/woff2" crossorigin="anonymous">

    <link rel="stylesheet" href="./assets/css/bootstrap.min.css">
    <link href="./assets/css/all.min.css" rel="stylesheet">
    <script src="./assets/js/jquery.min.js"></script>
    <script src="./assets/js/bootstrap.bundle.min.js"></script>
    <script src="./assets/js/fix_frontend_errors.js"></script>
    <style>
        .sidebar {
            position: fixed;
            left: 0;
            top: 0;
            bottom: 0;
            width: 280px;
            background: #ffffff;
            padding: 24px 20px;
            border-right: 1px solid rgba(0, 0, 0, 0.04);
            z-index: 1000;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            overflow-y: auto;
        }
        .main-content {
            margin-left: 280px;
            padding: 0;
            height: 100vh;
            background: #f2f2f7;
        }
        iframe {
            width: 100%;
            height: calc(100vh - 40px); /* 调整高度，不需要为标题留空间 */
            border: none;
        }
        .pagination {
            margin-top: 20px;
        }
        .toggle-children {
            cursor: pointer;
        }
        .child-row {
            display: none;
            background: #f8f9fa;
        }
        .user-info {
            background: #ffffff;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 24px;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.04);
            border: 1px solid rgba(0, 0, 0, 0.04);
        }

        .user-profile {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
        }

        .user-avatar {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            background: linear-gradient(135deg, #007aff 0%, #5856d6 100%);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 18px;
            margin-right: 12px;
            box-shadow: 0 4px 12px rgba(0, 122, 255, 0.3);
        }

        .user-details h5 {
            font-size: 17px;
            font-weight: 600;
            color: #1d1d1f;
            margin: 0 0 4px 0;
            letter-spacing: -0.022em;
        }

        .user-welcome {
            font-size: 13px;
            color: #86868b;
            margin: 0;
            line-height: 1.3;
        }
        .dashboard-btn {
            background: #007aff;
            color: white;
            border: none;
            border-radius: 12px;
            padding: 12px 20px;
            margin: 0 auto 24px auto;
            font-size: 15px;
            font-weight: 500;
            transition: all 0.2s ease-in-out;
            box-shadow: 0 4px 12px rgba(0, 122, 255, 0.3);
            letter-spacing: -0.022em;
            display: block;
            width: fit-content;
            min-width: 160px;
            text-align: center;
        }

        .dashboard-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0, 122, 255, 0.4);
            background: #0051d5;
            color: white;
        }

        .dashboard-btn:active {
            transform: translateY(0);
            box-shadow: 0 4px 12px rgba(0, 122, 255, 0.3);
        }

        .dashboard-btn i {
            margin-right: 8px;
            font-size: 16px;
        }
        .list-group {
            border: none;
        }

        .list-group-item {
            background: transparent;
            border: none;
            border-radius: 8px;
            margin-bottom: 4px;
            padding: 12px 16px;
            color: #1d1d1f;
            font-size: 15px;
            font-weight: 400;
            transition: all 0.2s ease-in-out;
            letter-spacing: -0.022em;
        }

        .list-group-item:hover {
            background-color: #f2f2f7;
            color: #1d1d1f;
            text-decoration: none;
            transform: translateX(4px);
        }

        .list-group-item.active-menu-item {
            background-color: #007aff;
            color: white;
            font-weight: 500;
            box-shadow: 0 4px 12px rgba(0, 122, 255, 0.3);
        }

        .list-group-item.active-menu-item:hover {
            background-color: #0051d5;
            color: white;
            transform: translateX(4px);
        }

        .list-group-item i {
            width: 20px;
            margin-right: 12px;
            text-align: center;
            font-size: 16px;
        }
        .user-actions {
            margin-top: 12px;
        }

        .user-actions .btn {
            background: #f2f2f7;
            border: none;
            color: #007aff;
            font-size: 13px;
            font-weight: 500;
            padding: 8px 12px;
            border-radius: 8px;
            transition: all 0.2s ease;
            width: 100%;
        }

        .user-actions .btn:hover {
            background: #e5e5ea;
            color: #0051d5;
        }

        .user-actions .btn i {
            margin-right: 6px;
            font-size: 12px;
        }

        .user-role-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 6px;
            font-size: 11px;
            font-weight: 500;
            margin-left: 8px;
            letter-spacing: 0.06em;
        }

        .role-admin {
            background-color: rgba(255, 59, 48, 0.1);
            color: #ff3b30;
        }

        .role-manager {
            background-color: rgba(255, 149, 0, 0.1);
            color: #ff9500;
        }
        
        /* 添加管理页面所需的样式 */
        /* 表格文字居中 */
        .table th, .table td {
            text-align: center;
            vertical-align: middle;
        }
        
        /* RCP柜名称样式 */
        .rcp-name {
            cursor: pointer;
            color: #28a745;
            text-decoration: underline;
        }
        
        .rcp-name:hover {
            color: #218838;
        }
        
        /* 分线箱名称样式 */
        .box-name {
            cursor: pointer;
            color: #007bff;
            text-decoration: underline;
        }
        
        .box-name:hover {
            color: #0056b3;
        }
        
        /* 位号列表样式 */
        .tag-list {
            display: none;
            margin-top: 10px;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 4px;
            border: 1px solid #dee2e6;
        }
        
        .tag-list table {
            margin-bottom: 0;
        }
        
        /* 加载指示器样式 */
        .loading-indicator {
            text-align: center;
            padding: 20px;
        }

        /* 退出登录按钮样式 */
        .logout-btn {
            background: #ff3b30;
            border: none;
            border-radius: 12px;
            color: white;
            font-size: 15px;
            font-weight: 500;
            padding: 12px 20px;
            transition: all 0.2s ease-in-out;
            box-shadow: 0 4px 12px rgba(255, 59, 48, 0.3);
            letter-spacing: -0.022em;
            display: block;
            margin: 0 auto;
            width: fit-content;
            min-width: 160px;
            text-align: center;
        }

        .logout-btn:hover {
            background: #d70015;
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(255, 59, 48, 0.4);
        }

        .logout-btn:active {
            transform: translateY(0);
            box-shadow: 0 4px 12px rgba(255, 59, 48, 0.3);
        }

        .logout-btn i {
            margin-right: 8px;
            font-size: 16px;
        }

        /* 菜单分组 */
        .menu-section {
            margin-bottom: 24px;
        }

        .menu-section-title {
            font-size: 13px;
            font-weight: 600;
            color: #86868b;
            margin-bottom: 8px;
            padding: 0 16px;
            text-transform: uppercase;
            letter-spacing: 0.06em;
        }

        /* 图标颜色 */
        .list-group-item .fa-users { color: #5856d6; }
        .list-group-item .fa-battery-full { color: #34c759; }
        .list-group-item .fa-cube { color: #007aff; }
        .list-group-item .fa-box-open { color: #5856d6; }
        .list-group-item .fa-tag { color: #ff3b30; }
        .list-group-item .fa-tint { color: #007aff; }
        .list-group-item .fa-money-bill-wave { color: #ff9500; }
        .list-group-item .fa-clock { color: #af52de; }
        .list-group-item .fa-chart-bar { color: #ff2d92; }
        .list-group-item .fa-clipboard-list { color: #86868b; }

        /* 活动状态时图标保持白色 */
        .list-group-item.active-menu-item i {
            color: white !important;
        }
    </style>
</head>
<body>
<div class="sidebar">
    <div class="user-info">
        <div class="user-profile">
            <div class="user-avatar">
                <?= strtoupper(substr($_SESSION['user']['username'] ?? '?', 0, 1)) ?>
            </div>
            <div class="user-details">
                <h5>
                    <?= htmlspecialchars($_SESSION['user']['username'] ?? '未登录') ?>
                    <?php if($_SESSION['user']['role'] == 'admin'): ?>
                        <span class="user-role-badge role-admin">管理员</span>
                    <?php elseif($_SESSION['user']['role'] == 'manager'): ?>
                        <span class="user-role-badge role-manager">管理员</span>
                    <?php endif; ?>
                </h5>
                <p class="user-welcome">欢迎使用后台管理系统</p>
            </div>
        </div>

        <!-- 用户操作按钮 -->
        <?php if($_SESSION['user']['role'] == 'manager'): ?>
        <div class="user-actions">
            <button class="btn change-password-btn">
                <i class="fas fa-key"></i> 修改密码
            </button>
        </div>
        <?php endif; ?>
    </div>

    <!-- 控制面板按钮 -->
    <a href="pages/dashboard.php" class="btn dashboard-btn" target="contentFrame" data-page="pages/dashboard.php" data-title="控制面板">
        <i class="fas fa-tachometer-alt"></i> 控制面板
    </a>

    <!-- 管理功能菜单 -->
    <div class="menu-section">
        <div class="menu-section-title">管理功能</div>
        <div class="list-group">
        <?php if($_SESSION['user']['role'] == 'admin'): ?>
        <a href="pages/users.php" class="list-group-item list-group-item-action" target="contentFrame" data-page="pages/users.php" data-title="用户管理">
            <i class="fas fa-users"></i> 用户管理
        </a>
        <?php endif; ?>
        <a href="pages/power_cabinet.php" class="list-group-item list-group-item-action" target="contentFrame" data-page="pages/power_cabinet.php" data-title="电源柜管理">
            <i class="fas fa-battery-full"></i> 电源柜管理
        </a>
        <a href="pages/rcp_cabinet.php" class="list-group-item list-group-item-action" target="contentFrame" data-page="pages/rcp_cabinet.php" data-title="RCP柜管理">
            <i class="fas fa-cube"></i> RCP柜管理
        </a>
        <a href="pages/junction_box.php" class="list-group-item list-group-item-action" target="contentFrame" data-page="pages/junction_box.php" data-title="分线箱管理">
            <i class="fas fa-box-open"></i> 分线箱管理
        </a>
        <a href="pages/terminal.php" class="list-group-item list-group-item-action" target="contentFrame" data-page="pages/terminal.php" data-title="位号管理">
            <i class="fas fa-tag"></i> 位号管理
        </a>
        <a href="pages/valve.php" class="list-group-item list-group-item-action" target="contentFrame" data-page="pages/valve.php" data-title="阀门管理">
            <i class="fas fa-tint"></i> 阀门管理
        </a>
        <?php if($_SESSION['user']['role'] == 'admin'): ?>
        <a href="pages/jintie.php" class="list-group-item list-group-item-action" target="contentFrame" data-page="pages/jintie.php" data-title="艰苦津贴管理">
            <i class="fas fa-money-bill-wave"></i> 艰苦津贴管理
        </a>
        <?php endif; ?>
        <?php if($_SESSION['user']['role'] === 'admin'): ?>
        <a href="pages/shift_schedule.php" class="list-group-item list-group-item-action" target="contentFrame" data-page="pages/shift_schedule.php" data-title="倒班时间管理">
            <i class="fas fa-clock"></i> 倒班时间管理
        </a>
        <?php endif; ?>
        <?php if($_SESSION['user']['role'] === 'admin'): ?>
        <a href="pages/feedback_management.php" class="list-group-item list-group-item-action" target="contentFrame" data-page="pages/feedback_management.php" data-title="意见反馈管理">
            <i class="fas fa-comments"></i> 意见反馈管理
        </a>
        <a href="pages/api_stats.php" class="list-group-item list-group-item-action" target="contentFrame" data-page="pages/api_stats.php" data-title="API调用统计">
            <i class="fas fa-chart-bar"></i> API调用统计
        </a>
        <a href="pages/tencent_config.php" class="list-group-item list-group-item-action" target="contentFrame" data-page="pages/tencent_config.php" data-title="腾讯云配置">
            <i class="fas fa-cloud"></i> 腾讯云配置
        </a>
        <?php endif; ?>
        <a href="pages/operation_logs.php" class="list-group-item list-group-item-action" target="contentFrame" data-page="pages/operation_logs.php" data-title="操作日志">
            <i class="fas fa-clipboard-list"></i> 操作日志
        </a>
        </div>
    </div>

    <!-- 退出登录按钮 -->
    <div class="mt-4">
        <a href="includes/logout.php" class="btn logout-btn">
            <i class="fas fa-sign-out-alt"></i> 退出登录
        </a>
    </div>
</div>

<div class="main-content">
    <iframe name="contentFrame" id="contentFrame" src="pages/dashboard.php" style="height: 100%;"></iframe>
</div>

<!-- 修改密码模态框 -->
<div class="modal fade" id="passwordModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header" style="background: linear-gradient(135deg, #4b6cb7 0%, #182848 100%); color: white;">
                <h5 class="modal-title"><i class="fas fa-key mr-2"></i>修改我的密码</h5>
                <button type="button" class="close" data-dismiss="modal" style="color: white;">&times;</button>
            </div>
            <form id="passwordForm">
                <div class="modal-body">
                    <input type="hidden" id="userId" name="user_id" value="<?= $_SESSION['user']['id'] ?>">
                    <div class="form-group">
                        <label for="password">新密码</label>
                        <div class="input-group">
                            <input type="password" id="password" name="password" class="form-control" required>
                            <div class="input-group-append">
                                <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </div>
                        <small class="form-text text-muted">密码长度至少6位，建议使用字母、数字和特殊字符的组合</small>
                    </div>
                    <div class="form-group">
                        <label for="confirmPassword">确认新密码</label>
                        <div class="input-group">
                            <input type="password" id="confirmPassword" name="confirmPassword" class="form-control" required>
                            <div class="input-group-append">
                                <button class="btn btn-outline-secondary" type="button" id="toggleConfirmPassword">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </div>
                        <small class="form-text text-muted">请再次输入新密码以确认</small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary" style="background: linear-gradient(135deg, #4b6cb7 0%, #182848 100%); border: none;">
                        <i class="fas fa-save mr-1"></i> 保存修改
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 提示框 -->
<div class="alert alert-success" id="alertBox" style="position: fixed; top: 20px; right: 20px; z-index: 9999; display: none; min-width: 300px; box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);"></div>

<script>
$(document).ready(function(){
    // 菜单点击事件
    $('a[target="contentFrame"]').click(function(){
        const pageUrl = $(this).attr('href');

        // 更新菜单高亮状态
        $('a[target="contentFrame"]').removeClass('active-menu-item');
        $(this).addClass('active-menu-item');

        // 保存当前页面URL到localStorage
        localStorage.setItem('currentPage', pageUrl);

        // 设置iframe的src
        try {
            document.getElementById('contentFrame').src = pageUrl;
        } catch (e) {
            console.error('设置iframe src失败:', e);
        }
    });
    
    // 页面加载时恢复上次访问的页面
    const savedPage = localStorage.getItem('currentPage');
    
    if(savedPage) {
        try {
            // 恢复iframe的src
            document.getElementById('contentFrame').src = savedPage;
            
            // 高亮当前选中的菜单项
            const basePage = savedPage.split('?')[0];
            $('a[data-page="' + basePage + '"]').addClass('active-menu-item');
        } catch (e) {
            console.error('恢复页面失败:', e);
            // 如果恢复失败，默认加载控制面板
            document.getElementById('contentFrame').src = 'pages/dashboard.php';
            $('a[data-page="pages/dashboard.php"]').addClass('active-menu-item');
        }
    } else {
        // 如果没有保存的页面，默认高亮控制面板
        $('a[data-page="pages/dashboard.php"]').addClass('active-menu-item');
    }
    
    // 监听iframe加载完成事件
    $('#contentFrame').on('load', function() {
        try {
            const currentSrc = $(this).attr('src');
            
            // 获取基本页面路径（不含查询参数）
            const basePage = currentSrc.split('?')[0];
            
            // 更新localStorage中的当前页面
            localStorage.setItem('currentPage', currentSrc);
            
            // 查找对应的菜单项
            const menuItem = $('a[data-page="' + basePage + '"]');
            
            if (menuItem.length > 0) {
                // 更新菜单高亮
                $('a[target="contentFrame"]').removeClass('active-menu-item');
                menuItem.addClass('active-menu-item');
            }
        } catch (e) {
            console.error('iframe加载事件处理失败:', e);
        }
    });
    
    // 修改密码按钮点击事件
    $('.change-password-btn').click(function() {
        $('#passwordModal').modal('show');
    });
    
    // 密码显示/隐藏切换
    $('#togglePassword').click(function() {
        const passwordField = $('#password');
        const icon = $(this).find('i');
        
        if (passwordField.attr('type') === 'password') {
            passwordField.attr('type', 'text');
            icon.removeClass('fa-eye').addClass('fa-eye-slash');
        } else {
            passwordField.attr('type', 'password');
            icon.removeClass('fa-eye-slash').addClass('fa-eye');
        }
    });
    
    // 确认密码显示/隐藏切换
    $('#toggleConfirmPassword').click(function() {
        const passwordField = $('#confirmPassword');
        const icon = $(this).find('i');
        
        if (passwordField.attr('type') === 'password') {
            passwordField.attr('type', 'text');
            icon.removeClass('fa-eye').addClass('fa-eye-slash');
        } else {
            passwordField.attr('type', 'password');
            icon.removeClass('fa-eye-slash').addClass('fa-eye');
        }
    });
    
    // 显示提示框
    function showAlert(message, type = 'success') {
        const alertBox = $('#alertBox');
        alertBox.removeClass('alert-success alert-danger alert-warning')
                .addClass('alert-' + type)
                .html(message)
                .fadeIn();
        
        setTimeout(function() {
            alertBox.fadeOut();
        }, 3000);
    }
    
    // 修改密码表单提交
    $('#passwordForm').submit(function(e) {
        e.preventDefault();
        
        const userId = $('#userId').val();
        const password = $('#password').val();
        const confirmPassword = $('#confirmPassword').val();
        
        if (password.length < 6) {
            showAlert('<i class="fas fa-exclamation-triangle mr-1"></i> 密码长度必须至少为6位', 'warning');
            return;
        }
        
        if (password !== confirmPassword) {
            showAlert('<i class="fas fa-exclamation-triangle mr-1"></i> 两次输入的密码不一致', 'warning');
            return;
        }
        
        $.ajax({
            url: 'pages/change_password.php',
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({
                change_password: true,
                user_id: userId,
                password: password
            }),
            success: function(response) {
                try {
                    const result = typeof response === 'string' ? JSON.parse(response) : response;
                    
                    if (result.status === 'success') {
                        $('#passwordModal').modal('hide');
                        showAlert('<i class="fas fa-check-circle mr-1"></i> ' + (result.message || '密码修改成功'), 'success');
                        
                        // 清空表单
                        $('#password').val('');
                        $('#confirmPassword').val('');
                    } else {
                        showAlert('<i class="fas fa-exclamation-circle mr-1"></i> ' + (result.message || '操作失败'), 'danger');
                    }
                } catch (e) {
                    showAlert('<i class="fas fa-exclamation-circle mr-1"></i> 服务器响应格式错误', 'danger');
                    console.error('Response parsing error:', e, response);
                }
            },
            error: function(xhr) {
                showAlert('<i class="fas fa-exclamation-circle mr-1"></i> 服务器错误，请稍后重试', 'danger');
                console.error('AJAX error:', xhr);
            }
        });
    });
    
    // 全局错误处理
    window.addEventListener('error', function(event) {
        if (event.message && event.message.includes('Receiving end does not exist')) {
            event.preventDefault();
            console.log('已忽略Chrome扩展错误');
            return true;
        }
    }, true);
});
</script>
</body>
</html>