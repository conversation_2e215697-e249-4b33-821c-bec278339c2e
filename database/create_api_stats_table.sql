-- API调用统计表
-- 用于监控腾讯云API的使用情况
-- 创建时间: 2025-01-08

CREATE TABLE IF NOT EXISTS `api_usage_stats` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `api_name` varchar(50) NOT NULL COMMENT 'API名称(ocr_recognition/image_compress/portrait_segmentation)',
  `api_action` varchar(100) DEFAULT NULL COMMENT '具体的API动作',
  `call_time` datetime NOT NULL COMMENT '调用时间',
  `ip_address` varchar(45) DEFAULT NULL COMMENT '调用IP地址',
  `user_agent` text COMMENT '用户代理',
  `request_size` int(11) DEFAULT NULL COMMENT '请求大小(字节)',
  `response_size` int(11) DEFAULT NULL COMMENT '响应大小(字节)',
  `processing_time` decimal(8,3) DEFAULT NULL COMMENT '处理时间(秒)',
  `is_success` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否成功(1:成功,0:失败)',
  `error_message` text COMMENT '错误信息',
  `tencent_request_id` varchar(100) DEFAULT NULL COMMENT '腾讯云请求ID',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_api_name` (`api_name`),
  KEY `idx_call_time` (`call_time`),
  KEY `idx_ip_address` (`ip_address`),
  KEY `idx_is_success` (`is_success`),
  KEY `idx_api_time` (`api_name`, `call_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='API调用统计表';

-- 创建API调用统计汇总视图
CREATE OR REPLACE VIEW `api_usage_summary` AS
SELECT 
    `api_name`,
    DATE(`call_time`) as `call_date`,
    COUNT(*) as `total_calls`,
    SUM(CASE WHEN `is_success` = 1 THEN 1 ELSE 0 END) as `success_calls`,
    SUM(CASE WHEN `is_success` = 0 THEN 1 ELSE 0 END) as `failed_calls`,
    ROUND(AVG(`processing_time`), 3) as `avg_processing_time`,
    SUM(`request_size`) as `total_request_size`,
    SUM(`response_size`) as `total_response_size`
FROM `api_usage_stats`
GROUP BY `api_name`, DATE(`call_time`)
ORDER BY `call_date` DESC, `api_name`;
