<?php
require_once __DIR__ . '/../includes/config.php';

echo "=== 数据库状态检查 ===\n\n";

try {
    // 检查微信用户表
    echo "1. 微信用户表 (wechat_users):\n";
    $stmt = $pdo->query('SELECT * FROM wechat_users');
    $wechatUsers = $stmt->fetchAll();
    
    if (empty($wechatUsers)) {
        echo "   没有微信用户数据\n";
    } else {
        foreach($wechatUsers as $user) {
            echo "   ID: {$user['id']}, OpenID: {$user['openid']}, 昵称: {$user['nickname']}\n";
        }
    }
    
    // 检查绑定关系表
    echo "\n2. 绑定关系表 (user_wechat_bindings):\n";
    $stmt = $pdo->query('SELECT * FROM user_wechat_bindings');
    $bindings = $stmt->fetchAll();
    
    if (empty($bindings)) {
        echo "   没有绑定关系数据\n";
    } else {
        foreach($bindings as $binding) {
            echo "   绑定ID: {$binding['id']}, 用户ID: {$binding['user_id']}, 微信用户ID: {$binding['wechat_user_id']}, 状态: " . ($binding['is_active'] ? '激活' : '禁用') . "\n";
        }
    }
    
    // 检查系统用户表
    echo "\n3. 系统用户表 (users) - 前5个用户:\n";
    $stmt = $pdo->query('SELECT id, username, role FROM users LIMIT 5');
    $users = $stmt->fetchAll();
    
    foreach($users as $user) {
        echo "   用户ID: {$user['id']}, 用户名: {$user['username']}, 角色: {$user['role']}\n";
    }
    
    // 检查是否有重复的绑定尝试
    echo "\n4. 检查可能的重复绑定:\n";
    $stmt = $pdo->query('SELECT user_id, wechat_user_id, COUNT(*) as count FROM user_wechat_bindings GROUP BY user_id, wechat_user_id HAVING count > 1');
    $duplicates = $stmt->fetchAll();
    
    if (empty($duplicates)) {
        echo "   没有发现重复绑定\n";
    } else {
        foreach($duplicates as $dup) {
            echo "   重复绑定: 用户ID {$dup['user_id']} - 微信用户ID {$dup['wechat_user_id']} (重复 {$dup['count']} 次)\n";
        }
    }
    
} catch (Exception $e) {
    echo "检查失败: " . $e->getMessage() . "\n";
}
?>
