-- 意见反馈系统数据库表

-- 1. 意见反馈主表
CREATE TABLE IF NOT EXISTS `feedback` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '反馈ID',
  `user_id` int(11) DEFAULT NULL COMMENT '用户ID（如果已登录）',
  `wechat_openid` varchar(100) DEFAULT NULL COMMENT '微信OpenID',
  `username` varchar(50) DEFAULT NULL COMMENT '用户名（冗余字段，便于查询）',
  `real_name` varchar(50) DEFAULT NULL COMMENT '真实姓名（冗余字段）',
  `feedback_type` varchar(20) NOT NULL COMMENT '反馈类型：建议、问题反馈、功能需求、其他',
  `content` text NOT NULL COMMENT '反馈内容',
  `contact_info` varchar(200) DEFAULT NULL COMMENT '联系方式（可选）',
  `status` enum('pending','processing','resolved','closed') DEFAULT 'pending' COMMENT '处理状态：待处理、处理中、已解决、已关闭',
  `priority` enum('low','medium','high','urgent') DEFAULT 'medium' COMMENT '优先级',
  `admin_reply` text DEFAULT NULL COMMENT '管理员回复',
  `admin_id` int(11) DEFAULT NULL COMMENT '处理的管理员ID',
  `admin_name` varchar(50) DEFAULT NULL COMMENT '处理管理员姓名',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `resolved_at` timestamp NULL DEFAULT NULL COMMENT '解决时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_feedback_type` (`feedback_type`),
  KEY `idx_wechat_openid` (`wechat_openid`),
  KEY `idx_priority` (`priority`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='意见反馈表';

-- 2. 反馈附件表（预留扩展）
CREATE TABLE IF NOT EXISTS `feedback_attachments` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `feedback_id` int(11) NOT NULL COMMENT '反馈ID',
  `file_name` varchar(255) NOT NULL COMMENT '文件名',
  `file_path` varchar(500) NOT NULL COMMENT '文件路径',
  `file_size` int(11) DEFAULT NULL COMMENT '文件大小（字节）',
  `file_type` varchar(50) DEFAULT NULL COMMENT '文件类型',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_feedback_id` (`feedback_id`),
  FOREIGN KEY (`feedback_id`) REFERENCES `feedback` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='反馈附件表';
