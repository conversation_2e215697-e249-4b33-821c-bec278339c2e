<?php
// 快速创建意见反馈系统数据库表
require_once __DIR__ . '/../includes/config.php';

echo "🚀 开始创建意见反馈系统数据库表...\n";

try {
    // 读取SQL文件
    $sql = file_get_contents(__DIR__ . '/create_feedback_tables.sql');
    
    if ($sql === false) {
        throw new Exception('无法读取SQL文件');
    }
    
    // 分割SQL语句
    $statements = array_filter(array_map('trim', explode(';', $sql)));

    $successCount = 0;
    $errors = [];

    foreach ($statements as $index => $statement) {
        if (empty($statement) || strpos($statement, '--') === 0) {
            continue;
        }

        // 清理语句
        $cleanStatement = trim($statement);
        if (empty($cleanStatement)) {
            continue;
        }

        echo "执行语句 " . ($index + 1) . ": ";

        try {
            $pdo->exec($cleanStatement);
            $successCount++;
            
            // 提取表名
            if (preg_match('/CREATE TABLE.*`(\w+)`/i', $cleanStatement, $matches)) {
                echo "✅ 创建表: {$matches[1]}\n";
            }
        } catch (PDOException $e) {
            $errors[] = $e->getMessage();
            echo "❌ 执行失败: " . $e->getMessage() . "\n";
        }
    }
    
    if (empty($errors)) {
        echo "\n🎉 意见反馈系统数据库表创建成功！\n";
        echo "📊 创建的表：\n";
        echo "   - feedback (意见反馈主表)\n";
        echo "   - feedback_attachments (反馈附件表)\n";
        echo "\n📝 功能说明：\n";
        echo "   - 支持用户提交各类反馈意见\n";
        echo "   - 管理员可以查看、回复和管理反馈\n";
        echo "   - 支持状态跟踪和优先级管理\n";
        echo "   - 预留附件上传功能扩展\n";
    } else {
        echo "\n⚠️  部分表创建失败\n";
        foreach ($errors as $error) {
            echo "   - " . $error . "\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ 创建失败: " . $e->getMessage() . "\n";
}
?>
