<?php
/**
 * 直接创建微信登录相关数据表
 */

require_once __DIR__ . '/../includes/config.php';

echo "开始创建微信登录相关数据表...\n";

try {
    // 1. 创建微信用户信息表
    $sql1 = "CREATE TABLE IF NOT EXISTS `wechat_users` (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `openid` varchar(100) NOT NULL COMMENT '微信用户唯一标识',
      `unionid` varchar(100) DEFAULT NULL COMMENT '微信开放平台唯一标识',
      `session_key` varchar(100) DEFAULT NULL COMMENT '微信会话密钥',
      `nickname` varchar(100) DEFAULT NULL COMMENT '微信昵称',
      `avatar_url` varchar(500) DEFAULT NULL COMMENT '微信头像URL',
      `gender` tinyint(1) DEFAULT 0 COMMENT '性别：0未知，1男，2女',
      `city` varchar(50) DEFAULT NULL COMMENT '城市',
      `province` varchar(50) DEFAULT NULL COMMENT '省份',
      `country` varchar(50) DEFAULT NULL COMMENT '国家',
      `language` varchar(20) DEFAULT 'zh_CN' COMMENT '语言',
      `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
      `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
      PRIMARY KEY (`id`),
      UNIQUE KEY `uk_openid` (`openid`),
      KEY `idx_unionid` (`unionid`),
      KEY `idx_created_at` (`created_at`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='微信用户信息表'";
    
    $pdo->exec($sql1);
    echo "✓ wechat_users 表创建成功\n";
    
    // 2. 创建用户绑定关系表
    $sql2 = "CREATE TABLE IF NOT EXISTS `user_wechat_bindings` (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `user_id` int(11) NOT NULL COMMENT '系统用户ID',
      `wechat_user_id` int(11) NOT NULL COMMENT '微信用户ID',
      `bind_type` enum('manual','auto') NOT NULL DEFAULT 'manual' COMMENT '绑定类型：manual手动绑定，auto自动绑定',
      `bind_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '绑定时间',
      `bind_ip` varchar(45) DEFAULT NULL COMMENT '绑定IP地址',
      `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否激活：1激活，0禁用',
      `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
      `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
      PRIMARY KEY (`id`),
      UNIQUE KEY `uk_user_wechat` (`user_id`, `wechat_user_id`),
      KEY `idx_user_id` (`user_id`),
      KEY `idx_wechat_user_id` (`wechat_user_id`),
      KEY `idx_bind_time` (`bind_time`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户微信绑定关系表'";
    
    $pdo->exec($sql2);
    echo "✓ user_wechat_bindings 表创建成功\n";
    
    // 3. 创建微信登录Token表
    $sql3 = "CREATE TABLE IF NOT EXISTS `wechat_tokens` (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `user_id` int(11) NOT NULL COMMENT '系统用户ID',
      `wechat_user_id` int(11) DEFAULT NULL COMMENT '微信用户ID（微信登录时有值）',
      `token` varchar(64) NOT NULL COMMENT 'Token值',
      `login_type` enum('password','wechat') NOT NULL DEFAULT 'password' COMMENT '登录类型',
      `expires_at` datetime NOT NULL COMMENT '过期时间',
      `last_used_at` timestamp NULL DEFAULT NULL COMMENT '最后使用时间',
      `ip_address` varchar(45) DEFAULT NULL COMMENT '登录IP地址',
      `user_agent` text COMMENT '用户代理信息',
      `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
      PRIMARY KEY (`id`),
      UNIQUE KEY `uk_token` (`token`),
      KEY `idx_user_id` (`user_id`),
      KEY `idx_wechat_user_id` (`wechat_user_id`),
      KEY `idx_expires_at` (`expires_at`),
      KEY `idx_login_type` (`login_type`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='微信登录Token表'";
    
    $pdo->exec($sql3);
    echo "✓ wechat_tokens 表创建成功\n";
    
    // 4. 创建微信登录日志表
    $sql4 = "CREATE TABLE IF NOT EXISTS `wechat_login_logs` (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `openid` varchar(100) NOT NULL COMMENT '微信用户openid',
      `user_id` int(11) DEFAULT NULL COMMENT '绑定的系统用户ID',
      `action` enum('login','bind','unbind','failed') NOT NULL COMMENT '操作类型',
      `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP地址',
      `user_agent` text COMMENT '用户代理',
      `result` enum('success','failed') NOT NULL COMMENT '操作结果',
      `error_message` varchar(500) DEFAULT NULL COMMENT '错误信息',
      `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
      PRIMARY KEY (`id`),
      KEY `idx_openid` (`openid`),
      KEY `idx_user_id` (`user_id`),
      KEY `idx_action` (`action`),
      KEY `idx_created_at` (`created_at`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='微信登录日志表'";
    
    $pdo->exec($sql4);
    echo "✓ wechat_login_logs 表创建成功\n";
    
    echo "\n所有数据表创建完成！\n";
    echo "现在可以访问微信绑定管理页面了。\n";
    
} catch (PDOException $e) {
    echo "✗ 创建表失败: " . $e->getMessage() . "\n";
    exit(1);
}
?>
