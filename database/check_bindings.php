<?php
require_once __DIR__ . '/../includes/config.php';

echo "检查现有微信绑定关系:\n";

try {
    // 查询所有绑定关系
    $stmt = $pdo->query("
        SELECT 
            uwb.id as binding_id,
            uwb.user_id,
            uwb.wechat_user_id,
            uwb.is_active,
            uwb.bind_time,
            u.username,
            wu.openid,
            wu.nickname
        FROM user_wechat_bindings uwb
        JOIN users u ON uwb.user_id = u.id
        JOIN wechat_users wu ON uwb.wechat_user_id = wu.id
        ORDER BY uwb.bind_time DESC
    ");
    
    $bindings = $stmt->fetchAll();
    
    if (empty($bindings)) {
        echo "没有找到任何绑定关系。\n";
    } else {
        echo "找到 " . count($bindings) . " 个绑定关系:\n\n";
        
        foreach ($bindings as $binding) {
            echo "绑定ID: {$binding['binding_id']}\n";
            echo "系统用户: {$binding['username']} (ID: {$binding['user_id']})\n";
            echo "微信用户: {$binding['nickname']} (OpenID: {$binding['openid']})\n";
            echo "状态: " . ($binding['is_active'] ? '激活' : '禁用') . "\n";
            echo "绑定时间: {$binding['bind_time']}\n";
            echo "---\n";
        }
    }
    
    // 查询微信用户信息
    echo "\n微信用户信息:\n";
    $stmt = $pdo->query("SELECT id, openid, nickname, created_at FROM wechat_users ORDER BY created_at DESC");
    $wechatUsers = $stmt->fetchAll();
    
    foreach ($wechatUsers as $user) {
        echo "微信用户ID: {$user['id']}, OpenID: {$user['openid']}, 昵称: {$user['nickname']}, 创建时间: {$user['created_at']}\n";
    }
    
} catch (Exception $e) {
    echo "查询失败: " . $e->getMessage() . "\n";
}
?>
