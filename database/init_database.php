<?php
// 数据库初始化脚本
require_once __DIR__ . '/../includes/config.php';

try {
    // 读取SQL文件
    $sql = file_get_contents(__DIR__ . '/create_jintie_tables.sql');
    
    if ($sql === false) {
        throw new Exception('无法读取SQL文件');
    }
    
    // 分割SQL语句（按分号分割）
    $statements = array_filter(array_map('trim', explode(';', $sql)));

    $successCount = 0;
    $errors = [];

    echo "找到 " . count($statements) . " 条SQL语句\n";

    foreach ($statements as $index => $statement) {
        // 跳过空语句
        if (empty($statement)) {
            echo "跳过语句 " . ($index + 1) . ": 空语句\n";
            continue;
        }

        // 跳过纯注释行（以--开头且不包含CREATE等关键字）
        if (strpos(trim($statement), '--') === 0 && !preg_match('/CREATE|INSERT|UPDATE|DELETE/i', $statement)) {
            echo "跳过语句 " . ($index + 1) . ": 注释\n";
            continue;
        }

        // 移除注释行，保留SQL语句
        $lines = explode("\n", $statement);
        $cleanLines = [];
        foreach ($lines as $line) {
            $line = trim($line);
            if (!empty($line) && strpos($line, '--') !== 0) {
                $cleanLines[] = $line;
            }
        }
        $cleanStatement = implode("\n", $cleanLines);

        if (empty(trim($cleanStatement))) {
            echo "跳过语句 " . ($index + 1) . ": 清理后为空\n";
            continue;
        }

        echo "执行语句 " . ($index + 1) . ": " . substr($cleanStatement, 0, 50) . "...\n";

        try {
            $pdo->exec($cleanStatement);
            $successCount++;
            echo "成功\n";
        } catch (PDOException $e) {
            $error = "执行SQL失败: " . $e->getMessage() . "\nSQL: " . substr($cleanStatement, 0, 100) . "...";
            $errors[] = $error;
            echo "失败: " . $e->getMessage() . "\n";
        }
    }
    
    if (empty($errors)) {
        echo "数据库初始化成功！执行了 {$successCount} 条SQL语句。\n";
        echo "创建的表：\n";
        echo "- jintie_records (津贴计算记录表)\n";
        echo "- jintie_params (津贴计算参数表)\n";
        echo "- dept_amount_settings (岗位金额设置表)\n";
    } else {
        echo "数据库初始化部分成功。执行了 {$successCount} 条SQL语句。\n";
        echo "错误信息：\n";
        foreach ($errors as $error) {
            echo "- " . $error . "\n";
        }
    }
    
} catch (Exception $e) {
    echo "数据库初始化失败: " . $e->getMessage() . "\n";
}
?>
