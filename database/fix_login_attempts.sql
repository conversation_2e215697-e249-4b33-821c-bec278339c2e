-- 修复login_attempts表结构的SQL脚本

-- 创建login_attempts表（如果不存在）
CREATE TABLE IF NOT EXISTS login_attempts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(255) NOT NULL,
    ip_address VARCHAR(45) NOT NULL,
    attempt_time DATETIME NOT NULL,
    is_success TINYINT(1) DEFAULT 0
);

-- 添加is_success列（如果不存在）
-- 注意：如果列已存在，这个语句会报错，但不会影响数据库
ALTER TABLE login_attempts ADD COLUMN is_success TINYINT(1) DEFAULT 0;

-- 添加索引（如果不存在）
-- 注意：如果索引已存在，这些语句会报错，但不会影响数据库
ALTER TABLE login_attempts ADD INDEX idx_username (username);
ALTER TABLE login_attempts ADD INDEX idx_ip_address (ip_address);
ALTER TABLE login_attempts ADD INDEX idx_attempt_time (attempt_time);
ALTER TABLE login_attempts ADD INDEX idx_is_success (is_success);

-- 更新现有记录的is_success字段（如果需要）
-- 假设现有记录都是失败的尝试
UPDATE login_attempts SET is_success = 0 WHERE is_success IS NULL;
