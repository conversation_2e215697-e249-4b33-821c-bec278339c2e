<?php
/**
 * 创建反馈违规记录表
 */
require_once __DIR__ . '/../includes/config.php';

echo "开始创建反馈违规记录表...\n";

try {
    // 读取SQL文件
    $sql = file_get_contents(__DIR__ . '/create_feedback_violations_table.sql');
    
    if ($sql === false) {
        throw new Exception('无法读取SQL文件');
    }
    
    // 执行SQL
    $pdo->exec($sql);
    echo "✅ 创建表: feedback_violations\n";
    
    echo "\n反馈违规记录表创建成功！\n";
    echo "创建的表：\n";
    echo "  - feedback_violations (反馈违规记录表)\n";
    
    // 验证表是否创建成功
    $stmt = $pdo->query("SHOW TABLES LIKE 'feedback_violations'");
    if ($stmt->rowCount() > 0) {
        echo "\n✅ 验证: feedback_violations 表创建成功\n";
        
        // 显示表结构
        echo "\nfeedback_violations 表结构：\n";
        $stmt = $pdo->query("DESCRIBE feedback_violations");
        $columns = $stmt->fetchAll();
        foreach ($columns as $column) {
            echo "  - {$column['Field']} ({$column['Type']})\n";
        }
    } else {
        echo "\n❌ 验证: feedback_violations 表创建失败\n";
    }
    
} catch (Exception $e) {
    echo "❌ 创建失败: " . $e->getMessage() . "\n";
}
?>
