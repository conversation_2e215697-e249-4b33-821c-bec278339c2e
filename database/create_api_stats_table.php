<?php
/**
 * 创建API统计表
 * 用于监控腾讯云API调用次数
 */

require_once __DIR__ . '/../includes/config.php';

echo "🚀 开始创建API统计数据库表...\n";

try {
    // 读取SQL文件
    $sql = file_get_contents(__DIR__ . '/create_api_stats_table.sql');
    
    if ($sql === false) {
        throw new Exception('无法读取SQL文件');
    }
    
    // 分割SQL语句
    $statements = array_filter(array_map('trim', explode(';', $sql)));

    $successCount = 0;
    $errors = [];

    foreach ($statements as $statement) {
        if (empty($statement) || strpos(trim($statement), '--') === 0) {
            continue;
        }

        // 移除注释行
        $lines = explode("\n", $statement);
        $cleanLines = [];
        foreach ($lines as $line) {
            $line = trim($line);
            if (!empty($line) && strpos($line, '--') !== 0) {
                $cleanLines[] = $line;
            }
        }
        $cleanStatement = implode("\n", $cleanLines);

        if (empty(trim($cleanStatement))) {
            continue;
        }

        try {
            $pdo->exec($cleanStatement);
            $successCount++;
            
            // 提取表名或视图名
            if (preg_match('/CREATE TABLE.*`(\w+)`/i', $cleanStatement, $matches)) {
                echo "✅ 创建表: {$matches[1]}\n";
            } elseif (preg_match('/CREATE.*VIEW.*`(\w+)`/i', $cleanStatement, $matches)) {
                echo "✅ 创建视图: {$matches[1]}\n";
            }
        } catch (PDOException $e) {
            $errors[] = $e->getMessage();
            echo "❌ 执行失败: " . $e->getMessage() . "\n";
        }
    }
    
    if (empty($errors)) {
        echo "\n🎉 API统计表创建成功！\n";
        echo "📊 创建的对象：\n";
        echo "   - api_usage_stats (API调用统计表)\n";
        echo "   - api_usage_summary (API调用汇总视图)\n";
        echo "\n📝 功能说明：\n";
        echo "   - 记录每次API调用的详细信息\n";
        echo "   - 支持成功率统计和性能分析\n";
        echo "   - 提供按日期汇总的统计视图\n";
    } else {
        echo "\n⚠️  部分对象创建失败\n";
        foreach ($errors as $error) {
            echo "   - " . $error . "\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ 创建失败: " . $e->getMessage() . "\n";
}
?>
