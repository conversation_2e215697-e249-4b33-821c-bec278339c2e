<?php
// 快速创建倒班管理表
require_once __DIR__ . '/../includes/config.php';

echo "🚀 开始创建倒班管理数据库表...\n";

try {
    // 读取SQL文件
    $sql = file_get_contents(__DIR__ . '/create_shift_tables.sql');
    
    if ($sql === false) {
        throw new Exception('无法读取SQL文件');
    }
    
    // 分割SQL语句
    $statements = array_filter(array_map('trim', explode(';', $sql)));

    $successCount = 0;
    $errors = [];

    foreach ($statements as $statement) {
        if (empty($statement) || strpos(trim($statement), '--') === 0) {
            continue;
        }

        // 移除注释行
        $lines = explode("\n", $statement);
        $cleanLines = [];
        foreach ($lines as $line) {
            $line = trim($line);
            if (!empty($line) && strpos($line, '--') !== 0) {
                $cleanLines[] = $line;
            }
        }
        $cleanStatement = implode("\n", $cleanLines);

        if (empty(trim($cleanStatement))) {
            continue;
        }

        try {
            $pdo->exec($cleanStatement);
            $successCount++;
            
            // 提取表名
            if (preg_match('/CREATE TABLE.*`(\w+)`/i', $cleanStatement, $matches)) {
                echo "✅ 创建表: {$matches[1]}\n";
            }
        } catch (PDOException $e) {
            $errors[] = $e->getMessage();
            echo "❌ 执行失败: " . $e->getMessage() . "\n";
        }
    }
    
    if (empty($errors)) {
        echo "\n🎉 所有表创建成功！\n";
        echo "📊 创建的表：\n";
        echo "   - shift_schedules (倒班排程表)\n";
        echo "   - shift_month_remarks (月度备注表)\n";
        echo "   - shift_tokens (微信小程序Token表)\n";
    } else {
        echo "\n⚠️  部分表创建失败\n";
        foreach ($errors as $error) {
            echo "   - " . $error . "\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ 创建失败: " . $e->getMessage() . "\n";
}
?>
