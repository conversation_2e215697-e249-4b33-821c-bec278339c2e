<?php
// 检查session是否已经启动
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}
ob_start();
header('Content-Type: text/html; charset=utf-8');
date_default_timezone_set('Asia/Shanghai');

// 数据库配置
define('DB_HOST', 'localhost');
define('DB_NAME', 'sunxiyue_zdh');
define('DB_USER', 'sunxiyue_zdh');
define('DB_PASS', 'a8578369@');
define('ITEMS_PER_PAGE', 20);
define('SITE_URL', 'http://zdh.sunxiyue.com');

// 初始化PDO连接
try {
    $pdo = new PDO(
        "mysql:host=".DB_HOST.";dbname=".DB_NAME.";charset=utf8mb4",
        DB_USER,
        DB_PASS,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false
        ]
    );
} catch(PDOException $e) {
    die("数据库连接失败: " . $e->getMessage());
}

// 权限检查函数
function requireRole($allowedRoles) {
    // 检查用户是否已登录
    if(!isset($_SESSION['user'])) {
        header("Location: login.php");
        exit;
    }
    
    // 如果允许的角色是数组，检查用户角色是否在允许的角色列表中
    if(is_array($allowedRoles)) {
        if(!in_array($_SESSION['user']['role'], $allowedRoles)) {
            header("HTTP/1.1 403 Forbidden");
            exit('无权访问');
        }
    } 
    // 如果允许的角色是字符串，检查用户角色是否匹配
    else if($_SESSION['user']['role'] != $allowedRoles) {
        header("HTTP/1.1 403 Forbidden");
        exit('无权访问');
    }
    
    return true;
}

// 操作日志记录
function logAction($action, $target, $details = []) {
    global $pdo;
    
    // 检查用户是否已登录
    if(!isset($_SESSION['user']) || !isset($_SESSION['user']['id'])) {
        // 未登录情况下使用0作为用户ID
        $userId = 0;
    } else {
        $userId = $_SESSION['user']['id'];
    }
    
    $stmt = $pdo->prepare("INSERT INTO operation_logs (user_id, action, target, details) VALUES (?,?,?,?)");
    $stmt->execute([
        $userId,
        $action,
        $target,
        json_encode($details, JSON_UNESCAPED_UNICODE)
    ]);
    
    // 检查日志总数，如果超过50条则删除最旧的记录
    $countStmt = $pdo->query("SELECT COUNT(*) FROM operation_logs");
    $totalLogs = $countStmt->fetchColumn();
    
    if ($totalLogs > 50) {
        // 计算需要删除的记录数
        $deleteCount = $totalLogs - 50;
        // 删除最旧的记录
        $deleteStmt = $pdo->prepare("DELETE FROM operation_logs ORDER BY created_at ASC LIMIT ?");
        $deleteStmt->execute([$deleteCount]);
    }
}

// 清理登录尝试记录，只保留最新的100条
function cleanupLoginAttempts() {
    global $pdo;
    
    try {
        // 检查login_attempts表是否存在
        $tableCheck = $pdo->query("SHOW TABLES LIKE 'login_attempts'");
        if ($tableCheck->rowCount() == 0) {
            return; // 表不存在，不需要清理
        }
        
        // 检查记录总数
        $countStmt = $pdo->query("SELECT COUNT(*) FROM login_attempts");
        $totalAttempts = $countStmt->fetchColumn();
        
        // 如果超过100条，删除多余的旧记录
        if ($totalAttempts > 100) {
            // 计算需要删除的记录数
            $deleteCount = $totalAttempts - 100;
            
            // 删除最旧的记录
            $deleteStmt = $pdo->prepare("DELETE FROM login_attempts ORDER BY attempt_time ASC LIMIT ?");
            $deleteStmt->execute([$deleteCount]);
        }
    } catch (Exception $e) {
        error_log("清理登录尝试记录失败: " . $e->getMessage());
    }
}

// CSRF保护
if (!isset($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}

// 加载腾讯云配置管理器
require_once __DIR__ . '/TencentConfigManager.php';

// 从数据库加载腾讯云配置
try {
    $tencentConfigs = TencentConfigManager::getTencentConstants($pdo);
    define('TENCENT_SECRET_ID', $tencentConfigs['TENCENT_SECRET_ID'] ?? '');
    define('TENCENT_SECRET_KEY', $tencentConfigs['TENCENT_SECRET_KEY'] ?? '');
    define('TENCENT_REGION', $tencentConfigs['TENCENT_REGION'] ?? 'ap-beijing');
} catch (Exception $e) {
    // 如果数据库读取失败，使用默认值
    define('TENCENT_SECRET_ID', '');
    define('TENCENT_SECRET_KEY', '');
    define('TENCENT_REGION', 'ap-beijing');
    error_log("加载腾讯云配置失败: " . $e->getMessage());
}

// 微信小程序配置
define('WECHAT_MINI_APPID', 'wx6aa87efc3d1e3605');     // 请替换为您的微信小程序AppID
define('WECHAT_MINI_SECRET', '1f2902b99bedad690f6d3c82e0ecf0d4');   // 请替换为您的微信小程序Secret

// 在config.php中添加
error_reporting(E_ALL);
ini_set('display_errors', 1);
?>