<?php
require 'config.php';

if($_SERVER['REQUEST_METHOD'] == 'POST') {
    $username = trim($_POST['username']);
    $password = $_POST['password'];
    $remember_username = isset($_POST['remember_username']) ? $_POST['remember_username'] : '0';
    $remember_password = isset($_POST['remember_password']) ? $_POST['remember_password'] : '0';
    $ip_address = $_SERVER['REMOTE_ADDR'];
    
    try {
        // 检查IP是否被锁定（只基于IP地址，不考虑用户名）
        $lockoutCheck = $pdo->prepare("
            SELECT COUNT(*) as attempts
            FROM login_attempts
            WHERE ip_address = ?
            AND attempt_time > DATE_SUB(NOW(), INTERVAL 1 HOUR)
            AND is_success = 0
        ");
        $lockoutCheck->execute([$ip_address]);
        $attemptCount = $lockoutCheck->fetch()['attempts'];
        
        // 如果IP尝试次数达到或超过5次，锁定该IP
        if ($attemptCount >= 5) {
            // 获取该IP最后一次失败尝试的时间
            $lastAttemptCheck = $pdo->prepare("
                SELECT attempt_time
                FROM login_attempts
                WHERE ip_address = ?
                AND is_success = 0
                ORDER BY attempt_time DESC
                LIMIT 1
            ");
            $lastAttemptCheck->execute([$ip_address]);
            $lastAttempt = $lastAttemptCheck->fetch();
            
            if ($lastAttempt) {
                $lastAttemptTime = new DateTime($lastAttempt['attempt_time']);
                $unlockTime = $lastAttemptTime->modify('+1 hour');
                $now = new DateTime();
                $timeRemaining = $now->diff($unlockTime);
                
                // 如果锁定时间未到，返回错误
                if ($unlockTime > $now) {
                    $minutesRemaining = ($timeRemaining->h * 60) + $timeRemaining->i;
                    header("Location: ../pages/login.php?error=2&minutes=" . $minutesRemaining);
                    exit();
                }
            }
        }

        // 验证用户
        $stmt = $pdo->prepare("SELECT id, username, role, password FROM users WHERE username = ? AND role IN ('admin','manager')");
        $stmt->execute([$username]);
        $user = $stmt->fetch();

        if($user && (password_verify($password, $user['password']) || $password === $user['password'])) {
            // 登录成功，记录成功登录并清除该IP的失败尝试记录
            $recordSuccess = $pdo->prepare("
                INSERT INTO login_attempts (username, ip_address, attempt_time, is_success, module)
                VALUES (?, ?, NOW(), 1, 'admin_backend')
            ");
            $recordSuccess->execute([$username, $ip_address]);

            // 清除该IP的失败尝试记录
            $clearAttempts = $pdo->prepare("DELETE FROM login_attempts WHERE ip_address = ? AND is_success = 0");
            $clearAttempts->execute([$ip_address]);
            
            $_SESSION['user'] = [
                'id' => $user['id'],
                'username' => $user['username'],
                'role' => $user['role']
            ];
            
            // 更新最后登录时间（需要last_login字段存在）
            // 注释掉，因为数据库中不存在last_login字段
            /*
            $pdo->prepare("UPDATE users SET last_login = NOW() WHERE id = ?")
                ->execute([$user['id']]);
            */
            
            // 记录成功登录
            logAction('用户登录', 'login', ['username' => $user['username']]);
            
            // 清理旧的登录尝试记录
            cleanupLoginAttempts();
            
            header("Location: ../index.php");
            exit();
        } else {
            // 登录失败，记录失败尝试（只记录IP，不锁定用户名）
            $recordAttempt = $pdo->prepare("
                INSERT INTO login_attempts (username, ip_address, attempt_time, is_success, module)
                VALUES (?, ?, NOW(), 0, 'admin_backend')
            ");
            $recordAttempt->execute([$username, $ip_address]);

            // 获取该IP当前失败尝试次数
            $currentAttempts = $pdo->prepare("
                SELECT COUNT(*) as count
                FROM login_attempts
                WHERE ip_address = ?
                AND attempt_time > DATE_SUB(NOW(), INTERVAL 1 HOUR)
                AND is_success = 0
            ");
            $currentAttempts->execute([$ip_address]);
            $attemptsLeft = 5 - $currentAttempts->fetch()['count'];
            
            // 清理旧的登录尝试记录
            cleanupLoginAttempts();
            
            if ($attemptsLeft <= 0) {
                header("Location: ../pages/login.php?error=2&minutes=60");
            } else {
                header("Location: ../pages/login.php?error=1&attempts=" . $attemptsLeft);
            }
            exit();
        }
    } catch(Exception $e) {
        error_log($e->getMessage());
        header("Location: ../pages/login.php?error=3");
        exit();
    }
}
// 如果不是POST请求，重定向到登录页面
header("Location: ../pages/login.php");
exit(); // 确保脚本在这里终止