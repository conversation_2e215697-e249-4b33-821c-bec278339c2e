<?php
/**
 * 腾讯云配置管理类
 */

class TencentConfigManager {
    private $pdo;
    
    public function __construct($pdo) {
        $this->pdo = $pdo;
    }
    
    /**
     * 获取配置值
     */
    public function getConfig($key) {
        try {
            $sql = "SELECT config_value, is_encrypted FROM tencent_config WHERE config_key = ?";
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute([$key]);
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($result) {
                $value = $result['config_value'];
                // 如果是加密存储的，需要解密
                if ($result['is_encrypted']) {
                    $value = base64_decode($value);
                }
                return $value;
            }
            
            return null;
        } catch (Exception $e) {
            error_log("获取腾讯云配置失败: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * 设置配置值
     */
    public function setConfig($key, $value, $description = null, $isEncrypted = false) {
        try {
            // 如果需要加密，先加密
            $storeValue = $isEncrypted ? base64_encode($value) : $value;
            
            // 检查配置是否已存在
            $checkSql = "SELECT id FROM tencent_config WHERE config_key = ?";
            $stmt = $this->pdo->prepare($checkSql);
            $stmt->execute([$key]);
            
            if ($stmt->fetch()) {
                // 更新现有配置
                $sql = "UPDATE tencent_config SET config_value = ?, config_desc = ?, is_encrypted = ? WHERE config_key = ?";
                $stmt = $this->pdo->prepare($sql);
                $stmt->execute([$storeValue, $description, $isEncrypted ? 1 : 0, $key]);
            } else {
                // 插入新配置
                $sql = "INSERT INTO tencent_config (config_key, config_value, config_desc, is_encrypted) VALUES (?, ?, ?, ?)";
                $stmt = $this->pdo->prepare($sql);
                $stmt->execute([$key, $storeValue, $description, $isEncrypted ? 1 : 0]);
            }
            
            return true;
        } catch (Exception $e) {
            error_log("设置腾讯云配置失败: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 获取所有配置
     */
    public function getAllConfigs() {
        try {
            $sql = "SELECT config_key, config_value, config_desc, is_encrypted, updated_at FROM tencent_config ORDER BY config_key";
            $stmt = $this->pdo->query($sql);
            $configs = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // 解密加密的配置值
            foreach ($configs as &$config) {
                if ($config['is_encrypted']) {
                    $config['config_value'] = base64_decode($config['config_value']);
                }
            }
            
            return $configs;
        } catch (Exception $e) {
            error_log("获取腾讯云配置列表失败: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * 测试腾讯云配置是否有效
     */
    public function testConfig() {
        try {
            $secretId = $this->getConfig('secret_id');
            $secretKey = $this->getConfig('secret_key');
            $region = $this->getConfig('region');
            
            if (!$secretId || !$secretKey || !$region) {
                return ['success' => false, 'message' => '配置信息不完整'];
            }
            
            // 这里可以添加实际的腾讯云API测试调用
            // 暂时只检查配置是否存在
            return ['success' => true, 'message' => '配置信息完整'];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => '测试失败: ' . $e->getMessage()];
        }
    }
    
    /**
     * 获取腾讯云配置常量（用于替代原来的define）
     */
    public static function getTencentConstants($pdo) {
        $manager = new self($pdo);
        return [
            'TENCENT_SECRET_ID' => $manager->getConfig('secret_id'),
            'TENCENT_SECRET_KEY' => $manager->getConfig('secret_key'),
            'TENCENT_REGION' => $manager->getConfig('region')
        ];
    }
}
?>
