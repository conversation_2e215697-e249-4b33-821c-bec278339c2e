<?php
require_once '../includes/config.php';
requireRole('admin');

// 引入腾讯云配置管理器
require_once '../includes/TencentConfigManager.php';
$configManager = new TencentConfigManager($pdo);

$message = '';
$messageType = '';

// 处理表单提交
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'update_config':
                $secretId = trim($_POST['secret_id']);
                $secretKey = trim($_POST['secret_key']);
                $region = trim($_POST['region']);

                // 验证输入 - 至少需要填写一项
                if (empty($secretId) && empty($secretKey) && empty($region)) {
                    $message = '请至少填写一项需要更新的配置';
                    $messageType = 'warning';
                } else {
                    // 只更新非空的配置项
                    $success = true;
                    $updatedItems = [];

                    if (!empty($secretId)) {
                        $success &= $configManager->setConfig('secret_id', $secretId, '腾讯云API密钥ID', true);
                        $updatedItems[] = 'Secret ID';
                    }

                    if (!empty($secretKey)) {
                        $success &= $configManager->setConfig('secret_key', $secretKey, '腾讯云API密钥Key', true);
                        $updatedItems[] = 'Secret Key';
                    }

                    if (!empty($region)) {
                        $success &= $configManager->setConfig('region', $region, '腾讯云服务地域', false);
                        $updatedItems[] = '服务地域';
                    }

                    if ($success) {
                        $message = '已成功更新：' . implode('、', $updatedItems);
                        $messageType = 'success';
                        logAction('更新配置', '腾讯云配置', [
                            'updated_items' => $updatedItems,
                            'region' => $region
                        ]);
                    } else {
                        $message = '配置更新失败，请检查数据库连接';
                        $messageType = 'danger';
                    }
                }
                break;
                
            case 'test_config':
                $testResult = $configManager->testConfig();
                $message = $testResult['message'];
                $messageType = $testResult['success'] ? 'success' : 'warning';
                break;
        }
    }
}

// 获取当前配置
$configs = $configManager->getAllConfigs();
$currentConfig = [];
foreach ($configs as $config) {
    $currentConfig[$config['config_key']] = $config;
}

// 页面标题
$pageTitle = '腾讯云配置管理';
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $pageTitle ?> - 平台常用工具微信小程序后台管理</title>

    <!-- 预加载字体文件 -->
    <link rel="preload" href="../assets/webfonts/fa-solid-900.woff2" as="font" type="font/woff2" crossorigin="anonymous">

    <link href="../assets/css/bootstrap.min.css" rel="stylesheet">
    <link href="../assets/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f2f2f7;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            color: #1d1d1f;
            line-height: 1.47059;
            font-weight: 400;
        }

        .container-fluid {
            padding: 24px;
        }
        
        .page-header {
            background: #ffffff;
            border-radius: 12px;
            padding: 24px 32px;
            margin-bottom: 24px;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.04);
            border: 1px solid rgba(0, 0, 0, 0.04);
        }

        .page-header h2 {
            font-size: 28px;
            font-weight: 600;
            color: #1d1d1f;
            margin-bottom: 4px;
            letter-spacing: -0.003em;
        }

        .page-header p {
            font-size: 17px;
            color: #86868b;
            margin: 0;
            font-weight: 400;
        }
        
        .config-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            border: 1px solid rgba(0,0,0,0.05);
        }
        
        .config-card h5 {
            color: #333;
            font-weight: 600;
            margin-bottom: 25px;
            display: flex;
            align-items: center;
        }
        
        .config-card h5 i {
            margin-right: 10px;
            color: #667eea;
        }
        
        .form-label {
            font-weight: 500;
            color: #495057;
            margin-bottom: 8px;
        }
        
        .form-control, .form-select {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
            transition: all 0.3s ease;
            height: 48px; /* 确保统一高度 */
        }

        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }

        .form-select {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23667eea' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m1 6 7 7 7-7'/%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 15px center;
            background-size: 16px 12px;
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
        }

        /* 修复input-group中的对齐问题 */
        .input-group .form-control {
            border-top-right-radius: 0;
            border-bottom-right-radius: 0;
        }

        .input-group .btn {
            height: 48px; /* 与form-control保持一致的高度 */
            border-top-left-radius: 0;
            border-bottom-left-radius: 0;
            border-left: none;
            padding: 0 15px;
        }

        .input-group .btn:hover {
            border-left: 2px solid #667eea;
        }

        /* 确保form-text说明文字的对齐 */
        .form-text {
            margin-top: 8px;
            font-size: 13px;
            color: #6c757d;
            line-height: 1.4;
        }
        
        .btn {
            border-radius: 10px;
            padding: 12px 25px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        
        .btn-outline-info {
            border-color: #17a2b8;
            color: #17a2b8;
        }
        
        .btn-outline-info:hover {
            background-color: #17a2b8;
            border-color: #17a2b8;
            transform: translateY(-2px);
        }
        
        .alert {
            border-radius: 10px;
            border: none;
            padding: 15px 20px;
        }
        
        .config-info {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 25px;
        }
        
        .config-info h6 {
            color: #495057;
            font-weight: 600;
            margin-bottom: 15px;
        }
        
        .config-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #e9ecef;
        }
        
        .config-item:last-child {
            border-bottom: none;
        }
        
        .config-key {
            font-weight: 500;
            color: #495057;
        }
        
        .config-value {
            font-family: 'Courier New', monospace;
            background: #e9ecef;
            padding: 4px 8px;
            border-radius: 5px;
            font-size: 12px;
        }
        
        .config-value.encrypted {
            color: #6c757d;
        }
        

    </style>
</head>
<body>
    <div class="container-fluid">
        <!-- 页面标题 -->
        <div class="page-header">
            <h2><i class="fas fa-cloud mr-3"></i><?= $pageTitle ?></h2>
            <p class="mb-0">管理腾讯云API配置信息</p>
        </div>
        <!-- 消息提示 -->
        <?php if ($message): ?>
        <div class="alert alert-<?= $messageType ?> alert-dismissible fade show" role="alert">
            <i class="fas fa-<?= $messageType === 'success' ? 'check-circle' : ($messageType === 'danger' ? 'exclamation-circle' : 'info-circle') ?> mr-2"></i>
            <?= htmlspecialchars($message) ?>
            <button type="button" class="btn-close" onclick="closeAlert(this)"></button>
        </div>
        <?php endif; ?>

        <!-- 当前配置信息 -->
        <div class="config-card">
            <h5><i class="fas fa-info-circle"></i>当前配置信息</h5>
            <div class="config-info">
                <h6>配置状态</h6>
                <?php foreach ($currentConfig as $key => $config): ?>
                <div class="config-item">
                    <span class="config-key"><?= htmlspecialchars($config['config_desc']) ?>:</span>
                    <span class="config-value <?= $config['is_encrypted'] ? 'encrypted' : '' ?>">
                        <?php if ($config['is_encrypted']): ?>
                            ****<?= substr($config['config_value'], -4) ?>
                        <?php else: ?>
                            <?= htmlspecialchars($config['config_value']) ?>
                        <?php endif; ?>
                    </span>
                </div>
                <?php endforeach; ?>
                <div class="config-item">
                    <span class="config-key">最后更新时间:</span>
                    <span class="config-value">
                        <?= isset($currentConfig['secret_id']) ? date('Y-m-d H:i:s', strtotime($currentConfig['secret_id']['updated_at'])) : '未设置' ?>
                    </span>
                </div>
            </div>
            
            <!-- 测试配置按钮 -->
            <form method="POST" style="display: inline;">
                <input type="hidden" name="action" value="test_config">
                <button type="submit" class="btn btn-outline-info">
                    <i class="fas fa-vial"></i> 测试配置
                </button>
            </form>
        </div>

        <!-- 配置更新表单 -->
        <div class="config-card">
            <h5><i class="fas fa-edit"></i>更新配置</h5>
            <div class="mb-3" style="background: #e3f2fd; border-left: 4px solid #2196f3; padding: 15px; border-radius: 5px;">
                <i class="fas fa-info-circle text-primary mr-2"></i>
                <strong>使用说明：</strong>只有在需要修改配置时才填写以下表单。如果不需要修改某项配置，请保持该字段为空。
            </div>
            <form method="POST">
                <input type="hidden" name="action" value="update_config">
                
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="secret_id" class="form-label">
                            <i class="fas fa-key text-primary"></i> Secret ID
                        </label>
                        <input type="text" class="form-control" id="secret_id" name="secret_id"
                               value=""
                               placeholder="仅在需要修改时填写">
                        <div class="form-text">腾讯云控制台获取的API密钥ID</div>
                    </div>
                    
                    <div class="col-md-6 mb-3">
                        <label for="secret_key" class="form-label">
                            <i class="fas fa-lock text-warning"></i> Secret Key
                        </label>
                        <div class="input-group">
                            <input type="password" class="form-control" id="secret_key" name="secret_key"
                                   value=""
                                   placeholder="仅在需要修改时填写">
                            <button class="btn btn-outline-secondary toggle-secret-btn" type="button" onclick="toggleSecretKey()">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                        <div class="form-text">腾讯云控制台获取的API密钥Key</div>
                    </div>
                </div>
                
                <div class="mb-4">
                    <label for="region" class="form-label">
                        <i class="fas fa-globe text-success"></i> 服务地域
                    </label>
                    <select class="form-select" id="region" name="region">
                        <option value="">仅在需要修改时选择</option>
                        <option value="ap-beijing" <?= ($currentConfig['region']['config_value'] ?? '') === 'ap-beijing' ? 'selected' : '' ?>>北京 (ap-beijing)</option>
                        <option value="ap-shanghai" <?= ($currentConfig['region']['config_value'] ?? '') === 'ap-shanghai' ? 'selected' : '' ?>>上海 (ap-shanghai)</option>
                        <option value="ap-guangzhou" <?= ($currentConfig['region']['config_value'] ?? '') === 'ap-guangzhou' ? 'selected' : '' ?>>广州 (ap-guangzhou)</option>
                        <option value="ap-chengdu" <?= ($currentConfig['region']['config_value'] ?? '') === 'ap-chengdu' ? 'selected' : '' ?>>成都 (ap-chengdu)</option>
                        <option value="ap-hongkong" <?= ($currentConfig['region']['config_value'] ?? '') === 'ap-hongkong' ? 'selected' : '' ?>>香港 (ap-hongkong)</option>
                        <option value="ap-singapore" <?= ($currentConfig['region']['config_value'] ?? '') === 'ap-singapore' ? 'selected' : '' ?>>新加坡 (ap-singapore)</option>
                    </select>
                    <div class="form-text">选择腾讯云服务所在地域</div>
                </div>
                
                <div class="d-flex" style="gap: 15px;">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> 保存配置
                    </button>
                    <button type="button" class="btn btn-outline-secondary" onclick="resetForm()">
                        <i class="fas fa-undo"></i> 重置
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- 不需要Bootstrap的JavaScript，使用纯JavaScript实现功能 -->
    <script>
        function resetForm() {
            if (confirm('确定要重置表单吗？')) {
                document.querySelector('form').reset();
            }
        }

        // 关闭提示消息
        function closeAlert(button) {
            const alert = button.closest('.alert');
            if (alert) {
                alert.style.opacity = '0';
                alert.style.transition = 'opacity 0.3s ease';
                setTimeout(function() {
                    if (alert.parentNode) {
                        alert.parentNode.removeChild(alert);
                    }
                }, 300);
            }
        }

        // 显示/隐藏密钥
        function toggleSecretKey() {
            const secretKeyInput = document.getElementById('secret_key');
            const toggleBtn = document.querySelector('.toggle-secret-btn');

            if (secretKeyInput.type === 'password') {
                secretKeyInput.type = 'text';
                toggleBtn.innerHTML = '<i class="fas fa-eye-slash"></i>';
            } else {
                secretKeyInput.type = 'password';
                toggleBtn.innerHTML = '<i class="fas fa-eye"></i>';
            }
        }

        // 验证表单
        function validateForm() {
            const secretId = document.getElementById('secret_id').value.trim();
            const secretKey = document.getElementById('secret_key').value.trim();
            const region = document.getElementById('region').value;

            // 至少需要填写一项
            if (!secretId && !secretKey && !region) {
                alert('请至少填写一项需要更新的配置');
                return false;
            }

            // 如果填写了Secret ID，检查长度
            if (secretId && secretId.length < 10) {
                alert('Secret ID 长度不能少于10位');
                return false;
            }

            // 如果填写了Secret Key，检查长度
            if (secretKey && secretKey.length < 10) {
                alert('Secret Key 长度不能少于10位');
                return false;
            }

            return true;
        }

        // 自动隐藏提示消息（只处理带有alert-dismissible类的消息）
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert.alert-dismissible');
            alerts.forEach(function(alert) {
                alert.style.opacity = '0';
                alert.style.transition = 'opacity 0.5s ease';
                setTimeout(function() {
                    if (alert.parentNode) {
                        alert.parentNode.removeChild(alert);
                    }
                }, 500);
            });
        }, 5000);

        // 表单提交前验证
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.querySelector('form[method="POST"]');
            if (form) {
                form.addEventListener('submit', function(e) {
                    if (!validateForm()) {
                        e.preventDefault();
                    }
                });
            }
        });
    </script>
</body>
</html>
