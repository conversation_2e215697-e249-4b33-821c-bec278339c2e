<?php
/**
 * API统计数据导出功能
 * 支持Excel和CSV格式导出
 */

require_once '../includes/config.php';
require_once '../api/api_stats_logger.php';

// 检查登录状态
if (!isset($_SESSION['user'])) {
    http_response_code(403);
    exit('未授权访问');
}

// 只允许管理员访问
if ($_SESSION['user']['role'] !== 'admin') {
    http_response_code(403);
    exit('权限不足');
}

// 获取导出参数
$exportType = $_GET['export'] ?? 'excel';
$apiName = $_GET['api_name'] ?? '';
$startDate = $_GET['start_date'] ?? date('Y-m-d', strtotime('-7 days'));
$endDate = $_GET['end_date'] ?? date('Y-m-d');

// 获取统计数据
$overviewStats = getApiOverviewStats();
// 获取所有详细统计数据（不分页，用于导出）
$detailStats = getApiUsageStatsWithPagination($apiName, $startDate, $endDate, 1, 10000);

// API名称映射
$apiNames = [
    'ocr_recognition' => 'OCR文字识别',
    'image_compress' => '图片压缩',
    'portrait_segmentation' => '人像分割'
];

// 格式化字节数
function formatBytes($bytes, $precision = 2) {
    if ($bytes == 0) return '0 B';
    $units = array('B', 'KB', 'MB', 'GB', 'TB');
    $base = log($bytes, 1024);
    return round(pow(1024, $base - floor($base)), $precision) . ' ' . $units[floor($base)];
}

if ($exportType === 'excel') {
    // 导出Excel格式
    header('Content-Type: application/vnd.ms-excel');
    header('Content-Disposition: attachment; filename="API统计数据_' . date('Y-m-d_H-i-s') . '.xls"');
    header('Cache-Control: max-age=0');
    
    echo '<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="http://www.w3.org/TR/REC-html40">';
    echo '<head><meta charset="UTF-8"><title>API统计数据</title></head>';
    echo '<body>';
    
    // 概览统计表
    echo '<h2>API使用概览统计</h2>';
    echo '<table border="1">';
    echo '<tr>';
    echo '<th>API名称</th>';
    echo '<th>总调用次数</th>';
    echo '<th>成功次数</th>';
    echo '<th>失败次数</th>';
    echo '<th>成功率(%)</th>';
    echo '<th>平均响应时间(秒)</th>';
    echo '<th>首次调用日期</th>';
    echo '<th>最近调用日期</th>';
    echo '</tr>';
    
    foreach ($overviewStats as $stat) {
        echo '<tr>';
        echo '<td>' . ($apiNames[$stat['api_name']] ?? $stat['api_name']) . '</td>';
        echo '<td>' . $stat['total_calls'] . '</td>';
        echo '<td>' . $stat['success_calls'] . '</td>';
        echo '<td>' . $stat['failed_calls'] . '</td>';
        echo '<td>' . $stat['success_rate'] . '</td>';
        echo '<td>' . $stat['avg_processing_time'] . '</td>';
        echo '<td>' . $stat['first_call'] . '</td>';
        echo '<td>' . $stat['last_call'] . '</td>';
        echo '</tr>';
    }
    echo '</table>';
    
    // 详细统计表
    if (!empty($detailStats)) {
        echo '<br><h2>详细统计数据 (' . $startDate . ' 至 ' . $endDate . ')</h2>';
        echo '<table border="1">';
        echo '<tr>';
        echo '<th>日期</th>';
        echo '<th>API名称</th>';
        echo '<th>总调用次数</th>';
        echo '<th>成功次数</th>';
        echo '<th>失败次数</th>';
        echo '<th>成功率(%)</th>';
        echo '<th>平均响应时间(秒)</th>';
        echo '<th>IP数量</th>';
        echo '<th>请求数据量</th>';
        echo '<th>响应数据量</th>';
        echo '</tr>';
        
        foreach ($detailStats as $stat) {
            $successRate = $stat['total_calls'] > 0 ? round(($stat['success_calls'] / $stat['total_calls']) * 100, 2) : 0;
            echo '<tr>';
            echo '<td>' . $stat['call_date'] . '</td>';
            echo '<td>' . ($apiNames[$stat['api_name']] ?? $stat['api_name']) . '</td>';
            echo '<td>' . $stat['total_calls'] . '</td>';
            echo '<td>' . $stat['success_calls'] . '</td>';
            echo '<td>' . $stat['failed_calls'] . '</td>';
            echo '<td>' . $successRate . '</td>';
            echo '<td>' . $stat['avg_processing_time'] . '</td>';
            echo '<td>' . ($stat['unique_ips'] ?? 0) . '</td>';
            echo '<td>' . formatBytes($stat['total_request_size']) . '</td>';
            echo '<td>' . formatBytes($stat['total_response_size']) . '</td>';
            echo '</tr>';
        }
        echo '</table>';
    }
    
    echo '</body></html>';
    
} elseif ($exportType === 'csv') {
    // 导出CSV格式
    header('Content-Type: text/csv; charset=UTF-8');
    header('Content-Disposition: attachment; filename="API统计数据_' . date('Y-m-d_H-i-s') . '.csv"');
    header('Cache-Control: max-age=0');
    
    // 输出BOM以支持中文
    echo "\xEF\xBB\xBF";
    
    $output = fopen('php://output', 'w');
    
    // 概览统计数据
    fputcsv($output, ['API使用概览统计'], ',', '"');
    fputcsv($output, ['API名称', '总调用次数', '成功次数', '失败次数', '成功率(%)', '平均响应时间(秒)', '首次调用日期', '最近调用日期'], ',', '"');
    
    foreach ($overviewStats as $stat) {
        fputcsv($output, [
            $apiNames[$stat['api_name']] ?? $stat['api_name'],
            $stat['total_calls'],
            $stat['success_calls'],
            $stat['failed_calls'],
            $stat['success_rate'],
            $stat['avg_processing_time'],
            $stat['first_call'],
            $stat['last_call']
        ], ',', '"');
    }
    
    // 空行分隔
    fputcsv($output, [], ',', '"');
    
    // 详细统计数据
    if (!empty($detailStats)) {
        fputcsv($output, ['详细统计数据 (' . $startDate . ' 至 ' . $endDate . ')'], ',', '"');
        fputcsv($output, ['日期', 'API名称', '总调用次数', '成功次数', '失败次数', '成功率(%)', '平均响应时间(秒)', 'IP数量', '请求数据量', '响应数据量'], ',', '"');
        
        foreach ($detailStats as $stat) {
            $successRate = $stat['total_calls'] > 0 ? round(($stat['success_calls'] / $stat['total_calls']) * 100, 2) : 0;
            fputcsv($output, [
                $stat['call_date'],
                $apiNames[$stat['api_name']] ?? $stat['api_name'],
                $stat['total_calls'],
                $stat['success_calls'],
                $stat['failed_calls'],
                $successRate,
                $stat['avg_processing_time'],
                $stat['unique_ips'] ?? 0,
                formatBytes($stat['total_request_size']),
                formatBytes($stat['total_response_size'])
            ], ',', '"');
        }
    }
    
    fclose($output);
} else {
    http_response_code(400);
    echo '不支持的导出格式';
}
?>
