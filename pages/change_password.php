<?php
require '../includes/config.php';

// 检查用户是否已登录
if (!isset($_SESSION['user'])) {
    header("HTTP/1.1 403 Forbidden");
    exit(json_encode(['status' => 'error', 'message' => '未登录']));
}

// 处理修改密码请求
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        $data = json_decode(file_get_contents('php://input'), true);
        
        // 验证数据
        if (!isset($data['user_id']) || !isset($data['password'])) {
            exit(json_encode(['status' => 'error', 'message' => '缺少必要参数']));
        }
        
        // 验证用户只能修改自己的密码（除非是管理员）
        if ($_SESSION['user']['role'] != 'admin' && $data['user_id'] != $_SESSION['user']['id']) {
            exit(json_encode(['status' => 'error', 'message' => '无权修改其他用户的密码']));
        }
        
        // 修改密码
        $stmt = $pdo->prepare("UPDATE users SET password=? WHERE id=?");
        $stmt->execute([
            password_hash($data['password'], PASSWORD_DEFAULT),
            $data['user_id']
        ]);
        
        // 记录操作日志
        logAction('修改密码', 'users', ['user_id' => $data['user_id']]);
        
        echo json_encode(['status' => 'success', 'message' => '密码修改成功']);
    } catch (PDOException $e) {
        exit(json_encode(['status' => 'error', 'message' => $e->getMessage()]));
    }
} 