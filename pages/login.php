<?php include '../includes/config.php';

// 如果用户已登录，重定向到首页
if(isset($_SESSION['user'])) {
    header("Location: ../index.php");
    exit;
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>平台常用工具微信小程序后台管理系统 - 登录</title>

    <!-- 预加载字体文件 -->
    <link rel="preload" href="../assets/webfonts/fa-solid-900.woff2" as="font" type="font/woff2" crossorigin="anonymous">

    <link href="../assets/css/bootstrap.min.css" rel="stylesheet">
    <link href="../assets/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 0;
            font-family: 'Microsoft YaHei', sans-serif;
            position: relative;
            overflow: hidden;
        }

        /* 背景动画效果 */
        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><radialGradient id="a" cx="50%" cy="50%" r="50%"><stop offset="0%" stop-color="rgba(255,255,255,0.1)"/><stop offset="100%" stop-color="rgba(255,255,255,0)"/></radialGradient></defs><circle cx="20" cy="20" r="20" fill="url(%23a)"/><circle cx="80" cy="80" r="30" fill="url(%23a)"/></svg>') repeat;
            animation: float 20s ease-in-out infinite;
            pointer-events: none;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        .container {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            width: 100%;
            padding: 20px;
            position: relative;
            z-index: 1;
        }

        .login-box {
            max-width: 420px;
            width: 100%;
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            animation: slideUp 0.8s ease-out;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .login-header {
            text-align: center;
            margin-bottom: 35px;
        }

        .login-header .logo {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .login-header .logo i {
            font-size: 35px;
            color: white;
        }

        .login-header h2 {
            color: #333;
            font-weight: 700;
            margin-bottom: 8px;
            font-size: 24px;
        }

        .login-header p {
            color: #666;
            font-size: 14px;
            margin: 0;
        }

        .form-group {
            margin-bottom: 25px;
            position: relative;
        }

        .form-group label {
            font-weight: 600;
            color: #555;
            margin-bottom: 8px;
            font-size: 14px;
        }

        .input-group {
            position: relative;
        }

        .input-group-prepend .input-group-text {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            border-radius: 10px 0 0 10px;
            width: 50px;
            justify-content: center;
        }

        .form-control {
            height: 50px;
            border: 2px solid #e1e5e9;
            border-left: none;
            border-radius: 0 10px 10px 0 !important;
            padding-left: 15px;
            font-size: 15px;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.9);
        }

        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
            background: white;
            border-radius: 0 10px 10px 0 !important;
        }

        /* 确保输入框组合的圆角正确显示 */
        .input-group .form-control:last-child {
            border-radius: 0 10px 10px 0 !important;
        }

        .input-group .input-group-prepend .input-group-text:first-child {
            border-radius: 10px 0 0 10px !important;
        }

        .remember-options {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 25px;
            flex-wrap: wrap;
            gap: 30px;
        }

        .custom-checkbox {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            color: #666;
            cursor: pointer;
            user-select: none;
        }

        .custom-checkbox input[type="checkbox"] {
            width: 18px;
            height: 18px;
            accent-color: #667eea;
            cursor: pointer;
        }

        .btn-login {
            height: 50px;
            border-radius: 10px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            font-weight: 600;
            font-size: 16px;
            color: white;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
            background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
        }

        .btn-login:active {
            transform: translateY(0);
        }

        .btn-login::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .btn-login:hover::before {
            left: 100%;
        }

        .alert {
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 25px;
            border: none;
            font-size: 14px;
        }

        .alert-danger {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
            color: white;
        }

        .alert-warning {
            background: linear-gradient(135deg, #feca57 0%, #ff9ff3 100%);
            color: white;
        }

        .alert i {
            margin-right: 8px;
        }

        /* 响应式设计 */
        @media (max-width: 480px) {
            .login-box {
                margin: 20px;
                padding: 30px 25px;
            }

            .login-header h2 {
                font-size: 20px;
            }

            .remember-options {
                justify-content: space-around;
                gap: 20px;
            }
        }

        /* 加载动画 */
        .loading {
            pointer-events: none;
        }

        .loading .btn-login {
            background: #ccc;
        }

        .loading .btn-login::after {
            content: '';
            position: absolute;
            width: 20px;
            height: 20px;
            margin: auto;
            border: 2px solid transparent;
            border-top-color: #ffffff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
<div class="container">
    <div class="login-box">
        <div class="login-header">
            <div class="logo">
                <i class="fas fa-tools"></i>
            </div>
            <h2>平台常用工具微信小程序</h2>
            <p>后台管理系统</p>
        </div>

        <?php if(isset($_GET['error'])): ?>
            <?php if($_GET['error'] == 1): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle"></i>
                    用户名或密码错误
                    <?php if(isset($_GET['attempts'])): ?>
                        <div class="mt-1">
                            <small>您还有 <strong><?= intval($_GET['attempts']) ?></strong> 次尝试机会</small>
                        </div>
                    <?php endif; ?>
                </div>
            <?php elseif($_GET['error'] == 2): ?>
                <div class="alert alert-warning">
                    <i class="fas fa-ban"></i>
                    您的IP地址已被临时锁定
                    <?php if(isset($_GET['minutes'])): ?>
                        <div class="mt-1">
                            <small>由于多次登录失败，请在 <strong><?= intval($_GET['minutes']) ?></strong> 分钟后再试</small>
                        </div>
                    <?php else: ?>
                        <div class="mt-1">
                            <small>由于多次登录失败，请在 <strong>60</strong> 分钟后再试</small>
                        </div>
                    <?php endif; ?>
                </div>
            <?php elseif($_GET['error'] == 3): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle"></i>
                    系统错误，请稍后再试
                </div>
            <?php endif; ?>
        <?php endif; ?>

        <form id="loginForm" action="../includes/do_login.php" method="post">
            <div class="form-group">
                <label for="username"><i class="fas fa-user mr-2"></i>用户名</label>
                <div class="input-group">
                    <div class="input-group-prepend">
                        <span class="input-group-text"><i class="fas fa-user"></i></span>
                    </div>
                    <input type="text" id="username" name="username" class="form-control" placeholder="请输入用户名" required autofocus>
                </div>
            </div>
            <div class="form-group">
                <label for="password"><i class="fas fa-lock mr-2"></i>密码</label>
                <div class="input-group">
                    <div class="input-group-prepend">
                        <span class="input-group-text"><i class="fas fa-lock"></i></span>
                    </div>
                    <input type="password" id="password" name="password" class="form-control" placeholder="请输入密码" required>
                    <input type="hidden" id="rememberUsername" name="remember_username" value="0">
                    <input type="hidden" id="rememberPassword" name="remember_password" value="0">
                </div>
            </div>

            <div class="remember-options">
                <div class="custom-checkbox">
                    <input type="checkbox" id="remember_user" onchange="toggleRememberUsername()">
                    <label for="remember_user">记住账号</label>
                </div>
                <div class="custom-checkbox">
                    <input type="checkbox" id="remember_pass" onchange="toggleRememberPassword()">
                    <label for="remember_pass">记住密码</label>
                </div>
            </div>

            <button type="submit" class="btn btn-login btn-block">
                <i class="fas fa-sign-in-alt mr-2"></i>登录
            </button>
        </form>
    </div>
</div>

<script src="../assets/js/jquery.min.js"></script>
<script>
$(document).ready(function() {
    // 页面加载时恢复保存的账号和密码
    loadSavedCredentials();

    // 表单提交时的处理
    $('#loginForm').on('submit', function(e) {
        // 添加加载状态
        $(this).addClass('loading');
        $('.btn-login').html('<i class="fas fa-spinner fa-spin mr-2"></i>登录中...');

        // 保存凭据
        saveCredentials();
    });
});

// 切换记住用户名
function toggleRememberUsername() {
    const checkbox = document.getElementById('remember_user');
    const hiddenInput = document.getElementById('rememberUsername');
    hiddenInput.value = checkbox.checked ? '1' : '0';

    if (!checkbox.checked) {
        // 如果取消记住用户名，也清除保存的用户名
        localStorage.removeItem('saved_username');
    }
}

// 切换记住密码
function toggleRememberPassword() {
    const checkbox = document.getElementById('remember_pass');
    const hiddenInput = document.getElementById('rememberPassword');
    hiddenInput.value = checkbox.checked ? '1' : '0';

    if (!checkbox.checked) {
        // 如果取消记住密码，也清除保存的密码
        localStorage.removeItem('saved_password');
        // 同时取消记住用户名的选择
        document.getElementById('remember_user').checked = false;
        document.getElementById('rememberUsername').value = '0';
        localStorage.removeItem('saved_username');
    } else {
        // 如果选择记住密码，自动选择记住用户名
        document.getElementById('remember_user').checked = true;
        document.getElementById('rememberUsername').value = '1';
    }
}

// 保存凭据到本地存储
function saveCredentials() {
    const username = document.getElementById('username').value;
    const password = document.getElementById('password').value;
    const rememberUser = document.getElementById('remember_user').checked;
    const rememberPass = document.getElementById('remember_pass').checked;

    if (rememberUser) {
        localStorage.setItem('saved_username', username);
        localStorage.setItem('remember_username', 'true');
    } else {
        localStorage.removeItem('saved_username');
        localStorage.removeItem('remember_username');
    }

    if (rememberPass) {
        // 注意：在生产环境中，不建议在本地存储明文密码
        // 这里仅作演示，实际应用中应该考虑更安全的方式
        localStorage.setItem('saved_password', btoa(password)); // 简单的base64编码
        localStorage.setItem('remember_password', 'true');
    } else {
        localStorage.removeItem('saved_password');
        localStorage.removeItem('remember_password');
    }
}

// 加载保存的凭据
function loadSavedCredentials() {
    const savedUsername = localStorage.getItem('saved_username');
    const savedPassword = localStorage.getItem('saved_password');
    const rememberUsername = localStorage.getItem('remember_username') === 'true';
    const rememberPassword = localStorage.getItem('remember_password') === 'true';

    if (rememberUsername && savedUsername) {
        document.getElementById('username').value = savedUsername;
        document.getElementById('remember_user').checked = true;
        document.getElementById('rememberUsername').value = '1';
    }

    if (rememberPassword && savedPassword) {
        try {
            document.getElementById('password').value = atob(savedPassword); // base64解码
            document.getElementById('remember_pass').checked = true;
            document.getElementById('rememberPassword').value = '1';
            // 记住密码时自动记住用户名
            document.getElementById('remember_user').checked = true;
            document.getElementById('rememberUsername').value = '1';
        } catch (e) {
            // 如果解码失败，清除保存的密码
            localStorage.removeItem('saved_password');
            localStorage.removeItem('remember_password');
        }
    }
}

// 清除所有保存的凭据（可以在需要时调用）
function clearSavedCredentials() {
    localStorage.removeItem('saved_username');
    localStorage.removeItem('saved_password');
    localStorage.removeItem('remember_username');
    localStorage.removeItem('remember_password');

    document.getElementById('username').value = '';
    document.getElementById('password').value = '';
    document.getElementById('remember_user').checked = false;
    document.getElementById('remember_pass').checked = false;
    document.getElementById('rememberUsername').value = '0';
    document.getElementById('rememberPassword').value = '0';
}
</script>
</body>
</html>