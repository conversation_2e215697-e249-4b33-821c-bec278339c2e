<?php
require '../includes/config.php';
requireRole(['admin','manager']);

// 处理表单提交
if($_SERVER['REQUEST_METHOD'] == 'POST') {
    $name = trim($_POST['name']);
    $location = trim($_POST['location']);
    $remark = trim($_POST['remark']);

    try {
        // 检查名称唯一性
        $check = $pdo->prepare("SELECT id FROM power_cabinet WHERE name = ?");
        $check->execute([$name]);
        if($check->fetch() && empty($_POST['id'])) {
            die(json_encode(['status'=>'error','message'=>'电源柜名称已存在']));
        }

        if(isset($_POST['id']) && !empty($_POST['id'])) {
            // 更新
            $stmt = $pdo->prepare("UPDATE power_cabinet SET name=?, location=?, remark=? WHERE id=?");
            $stmt->execute([$name, $location, $remark, $_POST['id']]);
            logAction('update', 'power_cabinet', ['id' => $_POST['id']]);
            
            // 添加调试信息
            error_log("更新电源柜成功: ID=" . $_POST['id']);
        } else {
            // 新建
            $stmt = $pdo->prepare("INSERT INTO power_cabinet (name, location, remark) VALUES (?,?,?)");
            $stmt->execute([$name, $location, $remark]);
            $newId = $pdo->lastInsertId();
            logAction('create', 'power_cabinet', ['name' => $name, 'id' => $newId]);
            
            // 添加调试信息
            error_log("创建电源柜成功: ID=" . $newId);
        }
        
        // 检查是否是AJAX请求
        if(!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
            echo json_encode(['status'=>'success', 'message'=>'操作成功']);
        } else {
            // 直接表单提交，重定向到第一页
            header("Location: power_cabinet.php?added=1");
        }
        exit();
    } catch(PDOException $e) {
        error_log("数据库错误: " . $e->getMessage());
        die(json_encode(['status'=>'error','message'=>$e->getMessage()]));
    }
}

// 删除处理
if(isset($_GET['delete'])) {
    try {
        $pdo->beginTransaction();
        $pdo->prepare("DELETE FROM rcp_cabinet WHERE power_id = ?")->execute([$_GET['delete']]);
        $pdo->prepare("DELETE FROM power_cabinet WHERE id = ?")->execute([$_GET['delete']]);
        $pdo->commit();
        
        // 保持分页和搜索状态
        $page = isset($_GET['page']) ? intval($_GET['page']) : 1;
        $search = isset($_GET['search']) ? urlencode($_GET['search']) : '';
        header("Location: power_cabinet.php?page=$page&search=$search&deleted=1");
        exit();
    } catch(PDOException $e) {
        $pdo->rollBack();
        die("删除失败：".$e->getMessage());
    }
}

// 分页和搜索参数
$page = isset($_GET['page']) ? intval($_GET['page']) : 1;
$limit = 10; // 每页显示10条
$offset = ($page - 1) * $limit;
$search = isset($_GET['search']) ? trim($_GET['search']) : '';

// 获取电源柜数据（添加搜索和分页）
$params = [];
$whereClause = "";

if (!empty($search)) {
    $whereClause = " WHERE p.name LIKE ? OR p.location LIKE ?";
    $params = ["%$search%", "%$search%"];
}

// 获取总记录数
$countSql = "SELECT COUNT(*) FROM power_cabinet p" . $whereClause;
$stmt = $pdo->prepare($countSql);
$stmt->execute($params);
$totalRecords = $stmt->fetchColumn();
$totalPages = ceil($totalRecords / $limit);

// 获取分页数据
$sql = "SELECT p.*, 
        (SELECT GROUP_CONCAT(name) FROM rcp_cabinet WHERE power_id = p.id) AS rcp_names
        FROM power_cabinet p" . 
        $whereClause . " ORDER BY p.id ASC LIMIT $offset, $limit";
$stmt = $pdo->prepare($sql);
$stmt->execute($params);
$cabinets = $stmt->fetchAll();
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>电源柜管理</title>
    <link rel="stylesheet" href="../assets/css/bootstrap.min.css">
    <link href="../assets/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/apple-style.css" rel="stylesheet">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <style>
        /* 表格文字居中 */
        .table th, .table td {
            text-align: center;
            vertical-align: middle;
        }
        
        /* 电源柜名称样式 */
        .power-name {
            cursor: pointer;
            color: #dc3545;
            text-decoration: underline;
        }
        
        .power-name:hover {
            color: #c82333;
        }
        
        /* RCP柜列表样式 */
        .rcp-list {
            display: none;
            margin-top: 10px;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 4px;
            border: 1px solid #dee2e6;
        }
    </style>
<script src="../assets/js/fix_frontend_errors.js"></script></head>
<body>
<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="page-header">
        <h3><i class="fas fa-battery-full mr-2"></i>电源柜管理</h3>
        <button class="btn btn-primary" data-toggle="modal" data-target="#powerModal">
            <i class="fas fa-plus mr-1"></i> 添加电源柜
        </button>
    </div>
    <!-- 搜索框 -->
    <div class="row mb-3">
        <div class="col-md-6">
        </div>
        <div class="col-md-6">
            <form class="form-inline justify-content-end" method="get">
                <small class="form-text text-muted mr-2">提示：可使用空格分隔多个关键词进行组合搜索</small>
                <div class="input-group">
                    <input type="text" class="form-control" name="search" placeholder="搜索电源柜..." value="<?= htmlspecialchars($search) ?>">
                    <div class="input-group-append">
                        <button class="btn btn-outline-secondary" type="submit">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <table class="table table-bordered table-hover mt-3">
        <thead class="thead-light">
            <tr>
                <th>序号</th>
                <th>电源柜名称</th>
                <th>位置</th>
                <th>备注</th>
                <th>操作</th>
            </tr>
        </thead>
        <tbody>
            <?php 
            $index = $offset + 1;
            foreach($cabinets as $cab): 
            ?>
            <tr>
                <td><?= $index++ ?></td>
                <td>
                    <span class="power-name" data-id="<?= $cab['id'] ?>"><?= htmlspecialchars($cab['name'] ?? '') ?></span>
                    <div id="rcp-list-<?= $cab['id'] ?>" class="rcp-list">
                        <h6>关联RCP柜：</h6>
                        <ul class="list-group">
                            <?php if($cab['rcp_names']): ?>
                                <?php foreach(explode(',', $cab['rcp_names']) as $rcp): ?>
                                <li class="list-group-item"><?= htmlspecialchars($rcp) ?></li>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <li class="list-group-item">暂无关联RCP柜</li>
                            <?php endif; ?>
                        </ul>
                    </div>
                </td>
                <td><?= htmlspecialchars($cab['location'] ?? '') ?></td>
                <td><?= htmlspecialchars($cab['remark'] ?? '') ?></td>
                <td>
                    <button class="btn btn-sm btn-warning edit-btn"
                            data-id="<?= $cab['id'] ?>"
                            data-name="<?= htmlspecialchars($cab['name'] ?? '') ?>"
                            data-location="<?= htmlspecialchars($cab['location'] ?? '') ?>"
                            data-remark="<?= htmlspecialchars($cab['remark'] ?? '') ?>">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-sm btn-danger delete-btn" 
                            data-id="<?= $cab['id'] ?>">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            </tr>
            <?php endforeach; ?>
            
            <?php if(count($cabinets) == 0): ?>
            <tr>
                <td colspan="5" class="text-center">没有找到匹配的记录</td>
            </tr>
            <?php endif; ?>
        </tbody>
    </table>

    <!-- 分页导航 -->
    <?php if($totalPages > 1): ?>
    <nav>
        <ul class="pagination justify-content-center">
            <li class="page-item <?= ($page <= 1) ? 'disabled' : '' ?>">
                <a class="page-link" href="?page=<?= $page-1 ?>&search=<?= urlencode($search) ?>">上一页</a>
            </li>
            
            <?php for($i = 1; $i <= $totalPages; $i++): ?>
            <li class="page-item <?= ($page == $i) ? 'active' : '' ?>">
                <a class="page-link" href="?page=<?= $i ?>&search=<?= urlencode($search) ?>"><?= $i ?></a>
            </li>
            <?php endfor; ?>
            
            <li class="page-item <?= ($page >= $totalPages) ? 'disabled' : '' ?>">
                <a class="page-link" href="?page=<?= $page+1 ?>&search=<?= urlencode($search) ?>">下一页</a>
            </li>
        </ul>
    </nav>
    <?php endif; ?>

    <!-- 模态框 -->
    <div class="modal fade" id="powerModal">
        <div class="modal-dialog">
            <div class="modal-content">
                <form id="powerForm" method="post" action="power_cabinet.php">
                    <input type="hidden" name="id" id="powerId">
                    <div class="modal-header">
                        <h5 class="modal-title">电源柜信息</h5>
                        <button type="button" class="close" data-dismiss="modal">&times;</button>
                    </div>
                    <div class="modal-body">
                        <div class="form-group">
                            <label>电源柜名称 *</label>
                            <input type="text" name="name" class="form-control" required>
                        </div>
                        <div class="form-group">
                            <label>位置信息 *</label>
                            <input type="text" name="location" class="form-control" required>
                        </div>
                        <div class="form-group">
                            <label>备注说明</label>
                            <textarea name="remark" class="form-control"></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                        <button type="button" id="saveButton" class="btn btn-primary">保存</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script src="../assets/js/jquery.min.js"></script>
<script src="../assets/js/bootstrap.bundle.min.js"></script>
<script>
// 点击电源柜名称显示关联RCP柜
$(document).on('click', '.power-name', function() {
    const powerId = $(this).data('id');
    const rcpList = $(`#rcp-list-${powerId}`);
    const isVisible = rcpList.is(':visible');
    
    // 先关闭所有打开的RCP柜列表
    $('.rcp-list').slideUp();
    
    // 如果当前点击的RCP柜列表已经是打开状态，则不需要再次打开
    if (!isVisible) {
        rcpList.slideDown();
    }
});

// 修复编辑按钮 - 使用data属性而不是JSON
$('.edit-btn').click(function(e){
    // 阻止事件冒泡
    e.stopPropagation();
    
    const id = $(this).data('id');
    const name = $(this).data('name');
    const location = $(this).data('location');
    const remark = $(this).data('remark');
    
    $('#powerId').val(id);
    $('input[name="name"]').val(name);
    $('input[name="location"]').val(location);
    $('textarea[name="remark"]').val(remark);
    $('#powerModal').modal('show');
});

// 删除确认
$('.delete-btn').click(function(e){
    // 阻止事件冒泡
    e.stopPropagation();
    
    if(confirm('确定删除该电源柜及其所有RCP柜？')) {
        window.location.href = 'power_cabinet.php?delete=' + $(this).data('id') + '&page=<?= $page ?>&search=<?= urlencode($search) ?>';
    }
});

// 清空模态框（添加新电源柜时）
$('[data-target="#powerModal"]').click(function() {
    $('#powerId').val('');
    $('#powerForm')[0].reset();
});

// 使用直接表单提交而不是AJAX
$('#saveButton').click(function() {
    // 表单验证
    const name = $('input[name="name"]').val().trim();
    const location = $('input[name="location"]').val().trim();
    
    if(!name) {
        alert('电源柜名称不能为空');
        return false;
    }
    
    if(!location) {
        alert('位置信息不能为空');
        return false;
    }
    
    // 显示加载状态
    const submitBtn = $(this);
    const originalText = submitBtn.html();
    submitBtn.html('<i class="fas fa-spinner fa-spin"></i> 处理中...').prop('disabled', true);
    
    // 直接提交表单
    $('#powerForm').submit();
});

// 添加成功和删除成功的提示
$(document).ready(function() {
    // 检查URL参数
    const urlParams = new URLSearchParams(window.location.search);
    
    // 添加成功提示
    if (urlParams.get('added') === '1') {
        alert('电源柜添加成功！');
    }
    
    // 删除成功提示
    if (urlParams.get('deleted') === '1') {
        alert('删除成功！');
    }
});
</script>
</body>
</html>