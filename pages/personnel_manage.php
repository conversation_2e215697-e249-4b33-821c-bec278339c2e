<?php require '../includes/config.php';

// 检查是否登录
if(!isset($_SESSION['user'])) {
    header("Location: login.php");
    exit;
}

// 只允许管理员访问
if($_SESSION['user']['role'] != 'admin') {
    header("Location: dashboard.php");
    exit;
}

// 处理添加人员
if(isset($_POST['add_personnel'])) {
    try {
        $dept = $_POST['dept'];
        $name = $_POST['name'];
        $fixed = floor(floatval($_POST['fixed_amount'])); // 确保是整数
        $floating = floor(floatval($_POST['floating_amount'])); // 确保是整数
        $is_visible = isset($_POST['is_visible']) ? 1 : 0; // 可见性字段
        $is_fixed_bonus = isset($_POST['is_fixed_bonus']) ? 1 : 0; // 固定奖金字段
        $floating_ratio = $is_fixed_bonus ? 0 : 1.0; // 根据固定奖金状态设置浮动比例

        // 获取最大排序值
        $maxOrderStmt = $pdo->query("SELECT MAX(sort_order) as max_order FROM jintie_personnel");
        $maxOrder = $maxOrderStmt->fetch(PDO::FETCH_ASSOC)['max_order'] ?? 0;
        $newOrder = $maxOrder + 1;

        $stmt = $pdo->prepare("INSERT INTO jintie_personnel (dept, name, fixed_amount, floating_amount, sort_order, is_visible, is_fixed_bonus, floating_ratio) VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
        $stmt->execute([$dept, $name, $fixed, $floating, $newOrder, $is_visible, $is_fixed_bonus, $floating_ratio]);
        
        $_SESSION['message'] = "人员添加成功！";
        header("Location: personnel_manage.php");
        exit;
    } catch (PDOException $e) {
        $_SESSION['error'] = "添加失败: " . $e->getMessage();
    }
}

// 处理更新人员
if(isset($_POST['update_personnel'])) {
    try {
        $id = $_POST['personnel_id'];
        $dept = $_POST['dept'];
        $name = $_POST['name'];
        $fixed = floor(floatval($_POST['fixed_amount'])); // 确保是整数
        $floating = floor(floatval($_POST['floating_amount'])); // 确保是整数
        $is_visible = isset($_POST['is_visible']) ? 1 : 0; // 可见性字段
        $is_fixed_bonus = isset($_POST['is_fixed_bonus']) ? 1 : 0; // 固定奖金字段
        $floating_ratio = $is_fixed_bonus ? 0 : 1.0; // 根据固定奖金状态设置浮动比例

        $stmt = $pdo->prepare("UPDATE jintie_personnel SET dept = ?, name = ?, fixed_amount = ?, floating_amount = ?, is_visible = ?, is_fixed_bonus = ?, floating_ratio = ? WHERE id = ?");
        $stmt->execute([$dept, $name, $fixed, $floating, $is_visible, $is_fixed_bonus, $floating_ratio, $id]);
        
        $_SESSION['message'] = "人员信息更新成功！";
        header("Location: personnel_manage.php");
        exit;
    } catch (PDOException $e) {
        $_SESSION['error'] = "更新失败: " . $e->getMessage();
    }
}

// 处理删除人员
if(isset($_POST['delete_personnel'])) {
    try {
        $id = $_POST['personnel_id'];
        
        $stmt = $pdo->prepare("DELETE FROM jintie_personnel WHERE id = ?");
        $stmt->execute([$id]);
        
        // 重新排序
        $pdo->query("SET @rank = 0");
        $pdo->query("UPDATE jintie_personnel SET sort_order = (@rank:=@rank+1) ORDER BY sort_order ASC");
        
        $_SESSION['message'] = "人员删除成功！";
        header("Location: personnel_manage.php");
        exit;
    } catch (PDOException $e) {
        $_SESSION['error'] = "删除失败: " . $e->getMessage();
    }
}

// 处理AJAX显示状态切换请求
if(isset($_POST['ajax_visibility_toggle']) && isset($_POST['personnel_id'])) {
    header('Content-Type: application/json');

    try {
        $id = intval($_POST['personnel_id']);
        $isVisible = intval($_POST['is_visible'] ?? 0); // 直接转换为整数

        $stmt = $pdo->prepare("UPDATE jintie_personnel SET is_visible = ? WHERE id = ?");
        $result = $stmt->execute([$isVisible, $id]);

        if ($result) {
            echo json_encode(['success' => true, 'message' => '显示状态更新成功']);
        } else {
            echo json_encode(['success' => false, 'message' => '更新失败']);
        }
        exit;
    } catch (PDOException $e) {
        echo json_encode(['success' => false, 'message' => '更新显示状态失败: ' . $e->getMessage()]);
        exit;
    }
}

// 处理AJAX固定奖金状态切换请求
if(isset($_POST['ajax_fixed_bonus_toggle']) && isset($_POST['personnel_id'])) {
    header('Content-Type: application/json');

    try {
        $id = intval($_POST['personnel_id']);
        $isFixedBonus = intval($_POST['is_fixed_bonus'] ?? 0); // 直接转换为整数
        $floating_ratio = $isFixedBonus ? 0 : 1.0; // 根据固定奖金状态设置浮动比例

        $stmt = $pdo->prepare("UPDATE jintie_personnel SET is_fixed_bonus = ?, floating_ratio = ? WHERE id = ?");
        $result = $stmt->execute([$isFixedBonus, $floating_ratio, $id]);

        if ($result) {
            echo json_encode(['success' => true, 'message' => '固定奖金状态更新成功']);
        } else {
            echo json_encode(['success' => false, 'message' => '更新失败']);
        }
        exit;
    } catch (PDOException $e) {
        echo json_encode(['success' => false, 'message' => '更新固定奖金状态失败: ' . $e->getMessage()]);
        exit;
    }
}

// 处理AJAX调整顺序请求
if(isset($_POST['ajax_move']) && isset($_POST['personnel_id']) && isset($_POST['direction'])) {
    header('Content-Type: application/json');

    try {
        $id = intval($_POST['personnel_id']);
        $direction = $_POST['direction']; // 'up' 或 'down'

        // 获取当前人员的顺序
        $stmt = $pdo->prepare("SELECT sort_order FROM jintie_personnel WHERE id = ?");
        $stmt->execute([$id]);
        $currentOrder = $stmt->fetchColumn();

        if (!$currentOrder) {
            echo json_encode(['success' => false, 'message' => '人员不存在']);
            exit;
        }

        $pdo->beginTransaction();

        if($direction === 'up' && $currentOrder > 1) {
            // 向上移动，交换当前人员与上一个人员的顺序
            $pdo->exec("UPDATE jintie_personnel SET sort_order = 0 WHERE sort_order = " . ($currentOrder - 1));
            $pdo->exec("UPDATE jintie_personnel SET sort_order = " . ($currentOrder - 1) . " WHERE id = " . $id);
            $pdo->exec("UPDATE jintie_personnel SET sort_order = " . $currentOrder . " WHERE sort_order = 0");
        } else if($direction === 'down') {
            // 检查是否还有下一个人员
            $stmt = $pdo->prepare("SELECT COUNT(*) FROM jintie_personnel WHERE sort_order = ?");
            $stmt->execute([$currentOrder + 1]);
            if($stmt->fetchColumn() > 0) {
                // 向下移动，交换当前人员与下一个人员的顺序
                $pdo->exec("UPDATE jintie_personnel SET sort_order = 0 WHERE sort_order = " . ($currentOrder + 1));
                $pdo->exec("UPDATE jintie_personnel SET sort_order = " . ($currentOrder + 1) . " WHERE id = " . $id);
                $pdo->exec("UPDATE jintie_personnel SET sort_order = " . $currentOrder . " WHERE sort_order = 0");
            } else {
                $pdo->rollback();
                echo json_encode(['success' => false, 'message' => '已经是最后一个']);
                exit;
            }
        } else {
            $pdo->rollback();
            echo json_encode(['success' => false, 'message' => '无法移动']);
            exit;
        }

        $pdo->commit();

        // 返回更新后的人员列表
        $stmt = $pdo->query("SELECT * FROM jintie_personnel ORDER BY sort_order ASC");
        $personnel = $stmt->fetchAll();

        echo json_encode(['success' => true, 'message' => '顺序调整成功', 'personnel' => $personnel]);
        exit;

    } catch (PDOException $e) {
        $pdo->rollback();
        echo json_encode(['success' => false, 'message' => '调整顺序失败: ' . $e->getMessage()]);
        exit;
    }
}

// 处理调整顺序（保留原有的POST方式作为备用）
if(isset($_POST['move_up']) || isset($_POST['move_down'])) {
    try {
        $id = $_POST['personnel_id'];
        $currentOrder = $_POST['current_order'];

        if(isset($_POST['move_up']) && $currentOrder > 1) {
            // 向上移动，交换当前人员与上一个人员的顺序
            $pdo->exec("UPDATE jintie_personnel SET sort_order = 0 WHERE sort_order = " . ($currentOrder - 1));
            $pdo->exec("UPDATE jintie_personnel SET sort_order = " . ($currentOrder - 1) . " WHERE id = " . $id);
            $pdo->exec("UPDATE jintie_personnel SET sort_order = " . $currentOrder . " WHERE sort_order = 0");
        } else if(isset($_POST['move_down'])) {
            // 向下移动，交换当前人员与下一个人员的顺序
            $pdo->exec("UPDATE jintie_personnel SET sort_order = 0 WHERE sort_order = " . ($currentOrder + 1));
            $pdo->exec("UPDATE jintie_personnel SET sort_order = " . ($currentOrder + 1) . " WHERE id = " . $id);
            $pdo->exec("UPDATE jintie_personnel SET sort_order = " . $currentOrder . " WHERE sort_order = 0");
        }

        $_SESSION['message'] = "顺序调整成功！";
        header("Location: personnel_manage.php");
        exit;
    } catch (PDOException $e) {
        $_SESSION['error'] = "调整顺序失败: " . $e->getMessage();
    }
}

// 处理显示状态切换
if(isset($_POST['is_visible_toggle'])) {
    try {
        $id = $_POST['personnel_id'];
        $isVisible = isset($_POST['is_visible']) ? 1 : 0;
        
        $stmt = $pdo->prepare("UPDATE jintie_personnel SET is_visible = ? WHERE id = ?");
        $stmt->execute([$isVisible, $id]);
        
        $_SESSION['message'] = "显示状态更新成功！";
        header("Location: personnel_manage.php");
        exit;
    } catch (PDOException $e) {
        $_SESSION['error'] = "更新显示状态失败: " . $e->getMessage();
    }
}

// 处理固定奖金状态切换
if(isset($_POST['is_fixed_bonus_toggle'])) {
    try {
        $id = $_POST['personnel_id'];
        $isFixedBonus = isset($_POST['is_fixed_bonus']) ? 1 : 0;
        $floating_ratio = $isFixedBonus ? 0 : 1.0; // 根据固定奖金状态设置浮动比例

        $stmt = $pdo->prepare("UPDATE jintie_personnel SET is_fixed_bonus = ?, floating_ratio = ? WHERE id = ?");
        $stmt->execute([$isFixedBonus, $floating_ratio, $id]);
        
        $_SESSION['message'] = "固定奖金状态更新成功！";
        header("Location: personnel_manage.php");
        exit;
    } catch (PDOException $e) {
        $_SESSION['error'] = "更新固定奖金状态失败: " . $e->getMessage();
    }
}

// 获取人员数据
try {
    $stmt = $pdo->query("SELECT * FROM jintie_personnel ORDER BY sort_order ASC");
    $personnel = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $_SESSION['error'] = "获取人员数据失败: " . $e->getMessage();
    $personnel = [];
}
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>津贴人员管理</title>
    <link rel="stylesheet" href="../assets/css/bootstrap.min.css">
    <link href="../assets/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/apple-style.css" rel="stylesheet">
    <script src="../assets/js/jquery.min.js"></script>
    <script src="../assets/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/fix_frontend_errors.js"></script>
    <style>
        /* 页面特定样式 */

        /* 优化的表格样式 - 增大文字，紧凑布局 */
        .table th {
            padding: 6px 10px !important;
            font-size: 15px !important;
            font-weight: 600 !important;
            background-color: #f8f9fa !important;
            border-bottom: 2px solid #dee2e6 !important;
        }

        .table td {
            padding: 6px 10px !important;
            font-size: 15px !important;
            line-height: 1.3 !important;
            vertical-align: middle !important;
        }

        /* 按钮组优化样式 */
        .btn-group-sm > .btn {
            padding: 4px 8px !important;
            font-size: 12px !important;
            border-radius: 4px !important;
        }

        /* 徽章优化样式 */
        .badge {
            padding: 4px 8px !important;
            font-size: 13px !important;
            font-weight: 500 !important;
            border-radius: 6px !important;
        }
        
        /* 数字输入框样式 */
        input[type="number"] {
            -moz-appearance: textfield; /* 移除Firefox中的上下箭头 */
        }
        
        /* 隐藏输入框的上下箭头，除非鼠标悬停 */
        input[type="number"]::-webkit-inner-spin-button, 
        input[type="number"]::-webkit-outer-spin-button { 
            opacity: 0;
        }
        
        input[type="number"]:hover::-webkit-inner-spin-button, 
        input[type="number"]:hover::-webkit-outer-spin-button { 
            opacity: 1;
        }
        
        /* 浮动提示样式 */
        .floating-alert {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            min-width: 250px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
            border-left: 4px solid;
            animation: slideIn 0.5s ease forwards;
        }
        
        .floating-alert.alert-success {
            border-left-color: #28a745;
        }
        
        .floating-alert.alert-danger {
            border-left-color: #dc3545;
        }
        
        @keyframes slideIn {
            0% { transform: translateX(100%); opacity: 0; }
            100% { transform: translateX(0); opacity: 1; }
        }
        
        @keyframes slideOut {
            0% { transform: translateX(0); opacity: 1; }
            100% { transform: translateX(100%); opacity: 0; }
        }
        
        .floating-alert.hiding {
            animation: slideOut 0.5s ease forwards;
        }
        
        /* 确保操作按钮位于一行 */
        .btn-toolbar {
            display: flex;
            flex-wrap: nowrap;
            justify-content: center;
        }
        
        .btn-toolbar .btn, 
        .btn-toolbar form {
            flex-shrink: 0;
            margin: 0 1px;
        }
        
        /* 按钮样式优化 */
        .btn-action {
            padding: 0.25rem 0.5rem;
            font-size: 13px;
            white-space: nowrap;
            border-radius: 6px;
        }

        /* 操作按钮组优化 */
        .btn-group .btn {
            border-radius: 6px !important;
            margin: 0 1px;
        }

        .btn-group .btn:first-child {
            margin-left: 0;
        }

        .btn-group .btn:last-child {
            margin-right: 0;
        }
        
        /* 确保序号单行显示 */
        .table td {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        
        /* 加强表格线条 */
        .table-bordered th,
        .table-bordered td {
            border: 1px solid #dee2e6;
        }
        
        /* 表格行高度优化 - 更紧凑 */
        .table tr {
            height: 45px;
        }

        /* 表格hover效果改进 */
        .table-hover tbody tr:hover {
            background-color: rgba(0, 123, 255, 0.08);
            transition: background-color 0.15s ease;
        }

        /* 表格整体优化 */
        .table {
            margin-bottom: 0 !important;
        }

        .table thead th {
            border-top: none !important;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        /* 姓名列字体加粗 */
        .font-weight-medium {
            font-weight: 600 !important;
            color: #2c3e50 !important;
        }

        /* 数字列居中对齐 */
        .number-cell {
            text-align: center !important;
            font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace !important;
            font-weight: 500 !important;
        }
        
        /* 添加FormCheck行内样式 */
        .form-check-inline {
            display: flex;
            align-items: center;
            padding-left: 0;
            margin-top: 8px;
        }
        
        .form-check-inline .form-check-input {
            position: static;
            margin-top: 0;
            margin-right: 0.3125rem;
            margin-left: 0;
        }
        
        .form-check-inline .form-check-label {
            margin-bottom: 0;
        }
        
        /* 添加人员表单优化样式 */
        .add-form {
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            border: 1px solid #e9ecef;
            padding: 20px;
            margin-bottom: 0;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.04);
        }

        /* 表单标签优化 */
        .add-form .form-label {
            font-weight: 600;
            font-size: 14px;
            color: #495057;
            margin-bottom: 6px;
        }

        /* 表单控件优化 */
        .add-form .form-control {
            border-radius: 8px;
            border: 1.5px solid #e9ecef;
            padding: 10px 12px;
            font-size: 14px;
            transition: all 0.2s ease;
            background-color: #ffffff;
        }

        .add-form .form-control:focus {
            border-color: #007aff;
            box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
            background-color: #ffffff;
        }

        /* 复选框区域优化 */
        .add-form .form-check {
            padding-left: 1.5rem;
            margin-bottom: 8px;
        }

        .add-form .form-check-input {
            width: 1.1rem;
            height: 1.1rem;
            margin-top: 0.15rem;
        }

        .add-form .form-check-label {
            font-size: 14px;
            font-weight: 500;
            color: #495057;
            margin-left: 0.3rem;
        }

        /* 添加按钮优化 */
        .add-form .btn-primary {
            background: linear-gradient(135deg, #007aff 0%, #0056b3 100%);
            border: none;
            border-radius: 8px;
            padding: 12px 20px;
            font-weight: 600;
            font-size: 14px;
            box-shadow: 0 2px 8px rgba(0, 122, 255, 0.3);
            transition: all 0.2s ease;
        }

        .add-form .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 122, 255, 0.4);
            background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
        }

        .add-form .btn-primary:active {
            transform: translateY(0);
        }

        /* 卡片头部优化 */
        .card-header.bg-white {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%) !important;
            border-bottom: 1px solid #e9ecef !important;
        }

        .card-header .text-primary {
            color: #007aff !important;
            font-weight: 600;
        }

        /* 表单图标样式 */
        .add-form .form-label i {
            width: 14px;
            font-size: 12px;
        }

        .add-form .form-check-label i {
            width: 12px;
            font-size: 11px;
            color: #6c757d;
        }

        /* 输入框占位符样式 */
        .add-form .form-control::placeholder {
            color: #adb5bd;
            font-style: italic;
        }

        /* 选择框默认选项样式 */
        .add-form select.form-control option:first-child {
            color: #adb5bd;
        }

        /* 响应式优化 */
        @media (max-width: 768px) {
            .add-form {
                padding: 15px;
            }

            .add-form .row {
                margin: 0 -8px;
            }

            .add-form .row > div {
                padding: 0 8px;
            }
        }
        
        /* 实时显示复选框状态 */
        .checkbox-preview {
            margin-left: 10px;
            font-weight: normal;
        }
        
        .checkbox-on {
            color: #28a745;
        }
        
        .checkbox-off {
            color: #dc3545;
        }

        /* 移动按钮样式优化 */
        .move-btn {
            transition: all 0.2s ease;
            border-radius: 6px !important;
        }

        .move-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .move-btn:hover:not(:disabled) {
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        /* 加载状态样式 */
        .move-btn .fa-spinner {
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 开关样式优化 */
        .custom-control-label::before {
            border-radius: 12px !important;
        }

        .custom-control-label::after {
            border-radius: 50% !important;
        }

        .custom-switch .custom-control-input:checked ~ .custom-control-label::before {
            background-color: #007aff !important;
            border-color: #007aff !important;
        }
    </style>
</head>
<body>
<div class="container-fluid px-4">
    <!-- 页面标题 -->
    <div class="page-header">
        <h3><i class="fas fa-users mr-2"></i>津贴人员管理</h3>
        <a href="jintie.php" class="btn btn-secondary">
            <i class="fas fa-arrow-left mr-1"></i> 返回津贴计算表
        </a>
    </div>
        <!-- 消息提示 -->
        <?php if(isset($_SESSION['message'])): ?>
        <div class="alert alert-success alert-dismissible fade show floating-alert">
            <?= $_SESSION['message'] ?>
            <button type="button" class="close" data-dismiss="alert">&times;</button>
        </div>
        <?php unset($_SESSION['message']); endif; ?>
        
        <?php if(isset($_SESSION['error'])): ?>
        <div class="alert alert-danger alert-dismissible fade show floating-alert">
            <?= $_SESSION['error'] ?>
            <button type="button" class="close" data-dismiss="alert">&times;</button>
        </div>
        <?php unset($_SESSION['error']); endif; ?>
        
        <!-- 添加人员表单 -->
        <div class="card mb-4 border-0 shadow-sm">
            <div class="card-header bg-white border-bottom-0 py-3">
                <h5 class="mb-0 text-primary"><i class="fas fa-user-plus mr-2"></i>添加人员</h5>
            </div>
            <div class="card-body p-0">
                <form method="post" action="" class="add-form">
                    <div class="row align-items-end">
                        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                            <label for="dept" class="form-label">
                                <i class="fas fa-briefcase mr-1 text-muted"></i>岗位
                            </label>
                            <select name="dept" id="dept" class="form-control" required>
                                <option value="">请选择岗位</option>
                                <option value="生产">生产</option>
                                <option value="监控">监控</option>
                                <option value="CB26">CB26</option>
                            </select>
                        </div>
                        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                            <label for="name" class="form-label">
                                <i class="fas fa-user mr-1 text-muted"></i>姓名
                            </label>
                            <input type="text" class="form-control" id="name" name="name" placeholder="请输入姓名" required>
                        </div>
                        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                            <label for="fixed_amount" class="form-label">
                                <i class="fas fa-coins mr-1 text-muted"></i>固定部分
                            </label>
                            <input type="number" class="form-control" id="fixed_amount" name="fixed_amount" value="300" min="0" step="1" required>
                        </div>
                        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                            <label for="floating_amount" class="form-label">
                                <i class="fas fa-chart-line mr-1 text-muted"></i>浮动部分
                            </label>
                            <input type="number" class="form-control" id="floating_amount" name="floating_amount" value="300" min="0" step="1" required>
                        </div>
                        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                            <label class="form-label">
                                <i class="fas fa-cog mr-1 text-muted"></i>状态设置
                            </label>
                            <div class="d-flex flex-column">
                                <div class="form-check mb-1">
                                    <input class="form-check-input" type="checkbox" id="is_visible" name="is_visible" checked>
                                    <label class="form-check-label" for="is_visible">
                                        <i class="fas fa-eye mr-1"></i>显示
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="is_fixed_bonus" name="is_fixed_bonus">
                                    <label class="form-check-label" for="is_fixed_bonus">
                                        <i class="fas fa-lock mr-1"></i>固定奖金
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                            <button type="submit" name="add_personnel" class="btn btn-primary w-100">
                                <i class="fas fa-plus mr-2"></i>添加人员
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- 人员列表 -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-list mr-2"></i>人员列表</h5>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <th class="text-center" width="8%">序号</th>
                                <th class="text-center" width="12%">岗位</th>
                                <th class="text-center" width="15%">姓名</th>
                                <th class="text-center" width="12%">固定部分</th>
                                <th class="text-center" width="12%">浮动部分</th>
                                <th class="text-center" width="12%">显示状态</th>
                                <th class="text-center" width="12%">固定奖金</th>
                                <th class="text-center" width="17%">操作</th>
                            </tr>
                        </thead>
                        <tbody id="personnelTableBody">
                            <?php foreach($personnel as $index => $person): ?>
                            <tr>
                                <td class="text-center align-middle"><?= $person['sort_order'] ?></td>
                                <td class="text-center align-middle">
                                    <span class="badge badge-<?= $person['dept'] == '生产' ? 'primary' : ($person['dept'] == '监控' ? 'warning' : 'info') ?>">
                                        <?= $person['dept'] ?>
                                    </span>
                                </td>
                                <td class="text-center align-middle font-weight-medium"><?= $person['name'] ?></td>
                                <td class="text-center align-middle number-cell"><?= number_format($person['fixed_amount'], 0) ?></td>
                                <td class="text-center align-middle number-cell"><?= number_format($person['floating_amount'], 0) ?></td>
                                <td class="text-center align-middle">
                                    <div class="custom-control custom-switch">
                                        <input type="checkbox" class="custom-control-input visibility-toggle"
                                               id="visibility_<?= $person['id'] ?>"
                                               data-personnel-id="<?= $person['id'] ?>"
                                               <?= intval($person['is_visible']) == 1 ? 'checked' : '' ?>>
                                        <label class="custom-control-label" for="visibility_<?= $person['id'] ?>"></label>
                                    </div>
                                </td>
                                <td class="text-center align-middle">
                                    <div class="custom-control custom-switch">
                                        <input type="checkbox" class="custom-control-input fixed-bonus-toggle"
                                               id="fixed_bonus_<?= $person['id'] ?>"
                                               data-personnel-id="<?= $person['id'] ?>"
                                               <?= isset($person['is_fixed_bonus']) && intval($person['is_fixed_bonus']) == 1 ? 'checked' : '' ?>>
                                        <label class="custom-control-label" for="fixed_bonus_<?= $person['id'] ?>"></label>
                                    </div>
                                </td>
                                <td class="text-center align-middle">
                                    <div class="btn-group btn-group-sm" role="group">
                                        <button type="button" class="btn btn-outline-primary edit-btn"
                                                data-id="<?= $person['id'] ?>"
                                                data-dept="<?= $person['dept'] ?>"
                                                data-name="<?= $person['name'] ?>"
                                                data-fixed="<?= $person['fixed_amount'] ?>"
                                                data-floating="<?= $person['floating_amount'] ?>"
                                                data-visible="<?= $person['is_visible'] ?>"
                                                data-fixed-bonus="<?= isset($person['is_fixed_bonus']) ? $person['is_fixed_bonus'] : 0 ?>"
                                                title="编辑">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button type="button" class="btn btn-outline-info move-btn"
                                                data-id="<?= $person['id'] ?>"
                                                data-direction="up"
                                                <?= $index == 0 ? 'disabled' : '' ?>
                                                title="上移">
                                            <i class="fas fa-arrow-up"></i>
                                        </button>
                                        <button type="button" class="btn btn-outline-info move-btn"
                                                data-id="<?= $person['id'] ?>"
                                                data-direction="down"
                                                <?= $index == count($personnel) - 1 ? 'disabled' : '' ?>
                                                title="下移">
                                            <i class="fas fa-arrow-down"></i>
                                        </button>
                                        <form method="post" action="" style="display:inline;" onsubmit="return confirm('确定要删除此人员吗？');">
                                            <input type="hidden" name="personnel_id" value="<?= $person['id'] ?>">
                                            <button type="submit" name="delete_personnel" class="btn btn-outline-danger" title="删除">
                                                <i class="fas fa-trash-alt"></i>
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 编辑模态框 -->
    <div class="modal fade" id="editModal" tabindex="-1" role="dialog" aria-labelledby="editModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editModalLabel"><i class="fas fa-edit mr-2"></i>编辑人员信息</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <form method="post" action="" id="editForm">
                        <input type="hidden" name="personnel_id" id="edit_id">

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="edit_dept" class="form-label">岗位</label>
                                    <select name="dept" id="edit_dept" class="form-control" required>
                                        <option value="生产">生产</option>
                                        <option value="监控">监控</option>
                                        <option value="CB26">CB26</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="edit_name" class="form-label">姓名</label>
                                    <input type="text" class="form-control" id="edit_name" name="name" required>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="edit_fixed_amount" class="form-label">固定部分</label>
                                    <input type="number" class="form-control" id="edit_fixed_amount" name="fixed_amount" min="0" step="1" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="edit_floating_amount" class="form-label">浮动部分</label>
                                    <input type="number" class="form-control" id="edit_floating_amount" name="floating_amount" min="0" step="1" required>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="edit_is_visible" name="is_visible">
                                        <label class="form-check-label" for="edit_is_visible">显示此人员</label>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="edit_is_fixed_bonus" name="is_fixed_bonus">
                                        <label class="form-check-label" for="edit_is_fixed_bonus">固定奖金</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">
                        <i class="fas fa-times mr-1"></i>取消
                    </button>
                    <button type="submit" form="editForm" name="update_personnel" class="btn btn-primary">
                        <i class="fas fa-save mr-1"></i>保存更改
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // 复选框标签更新函数
        function updateCheckboxLabel(id) {
            const checkbox = document.getElementById(id);
            const statusSpan = document.getElementById(id + '_status');
            
            if (checkbox.checked) {
                statusSpan.className = 'checkbox-preview checkbox-on';
                statusSpan.textContent = id === 'is_visible' ? '(显示)' : '(固定)';
            } else {
                statusSpan.className = 'checkbox-preview checkbox-off';
                statusSpan.textContent = id === 'is_visible' ? '(隐藏)' : '(普通)';
            }
        }
        
        // 编辑模态框复选框标签更新函数
        function updateEditCheckboxLabel(id) {
            const checkbox = document.getElementById(id);
            const statusSpan = document.getElementById(id + '_status');
            
            if (checkbox.checked) {
                statusSpan.className = 'checkbox-preview checkbox-on';
                statusSpan.textContent = id === 'edit_is_visible' ? '(显示)' : '(固定)';
            } else {
                statusSpan.className = 'checkbox-preview checkbox-off';
                statusSpan.textContent = id === 'edit_is_visible' ? '(隐藏)' : '(普通)';
            }
        }
        
        $(document).ready(function() {
            
            // 处理移动按钮点击事件
            $('.move-btn').click(function() {
                const button = $(this);
                const personnelId = button.data('id');
                const direction = button.data('direction');

                // 禁用所有移动按钮，防止重复点击
                $('.move-btn').prop('disabled', true);

                // 显示加载状态
                const originalHtml = button.html();
                button.html('<i class="fas fa-spinner fa-spin"></i>');

                $.ajax({
                    url: 'personnel_manage.php',
                    method: 'POST',
                    data: {
                        ajax_move: 1,
                        personnel_id: personnelId,
                        direction: direction
                    },
                    dataType: 'json',
                    success: function(response) {
                        if (response.success) {
                            // 重新构建表格内容
                            updatePersonnelTable(response.personnel);
                            showMessage(response.message, 'success');
                        } else {
                            showMessage(response.message, 'error');
                        }
                    },
                    error: function(xhr, status, error) {
                        showMessage('操作失败：' + error, 'error');
                    },
                    complete: function() {
                        // 恢复按钮状态
                        button.html(originalHtml);
                        $('.move-btn').prop('disabled', false);
                    }
                });
            });

            // 更新人员表格
            function updatePersonnelTable(personnel) {
                const tbody = $('#personnelTableBody');
                tbody.empty();

                personnel.forEach(function(person, index) {
                    const isVisible = parseInt(person.is_visible) === 1;
                    const isFixedBonus = parseInt(person.is_fixed_bonus) === 1;
                    const deptBadgeClass = person.dept === '生产' ? 'primary' : (person.dept === '监控' ? 'warning' : 'info');

                    const row = `
                        <tr>
                            <td class="text-center align-middle">${person.sort_order}</td>
                            <td class="text-center align-middle">
                                <span class="badge badge-${deptBadgeClass}">
                                    ${person.dept}
                                </span>
                            </td>
                            <td class="text-center align-middle font-weight-medium">${person.name}</td>
                            <td class="text-center align-middle number-cell">${parseInt(person.fixed_amount).toLocaleString()}</td>
                            <td class="text-center align-middle number-cell">${parseInt(person.floating_amount).toLocaleString()}</td>
                            <td class="text-center align-middle">
                                <div class="custom-control custom-switch">
                                    <input type="checkbox" class="custom-control-input visibility-toggle"
                                           id="visibility_${person.id}"
                                           data-personnel-id="${person.id}"
                                           ${isVisible ? 'checked' : ''}>
                                    <label class="custom-control-label" for="visibility_${person.id}"></label>
                                </div>
                            </td>
                            <td class="text-center align-middle">
                                <div class="custom-control custom-switch">
                                    <input type="checkbox" class="custom-control-input fixed-bonus-toggle"
                                           id="fixed_bonus_${person.id}"
                                           data-personnel-id="${person.id}"
                                           ${isFixedBonus ? 'checked' : ''}>
                                    <label class="custom-control-label" for="fixed_bonus_${person.id}"></label>
                                </div>
                            </td>
                            <td class="text-center align-middle">
                                <div class="btn-group btn-group-sm" role="group">
                                    <button type="button" class="btn btn-outline-primary edit-btn"
                                            data-id="${person.id}"
                                            data-dept="${person.dept}"
                                            data-name="${person.name}"
                                            data-fixed="${person.fixed_amount}"
                                            data-floating="${person.floating_amount}"
                                            data-visible="${person.is_visible}"
                                            data-fixed-bonus="${person.is_fixed_bonus || 0}"
                                            title="编辑">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button type="button" class="btn btn-outline-info move-btn"
                                            data-id="${person.id}"
                                            data-direction="up"
                                            ${index == 0 ? 'disabled' : ''}
                                            title="上移">
                                        <i class="fas fa-arrow-up"></i>
                                    </button>
                                    <button type="button" class="btn btn-outline-info move-btn"
                                            data-id="${person.id}"
                                            data-direction="down"
                                            ${index == personnel.length - 1 ? 'disabled' : ''}
                                            title="下移">
                                        <i class="fas fa-arrow-down"></i>
                                    </button>
                                    <form method="post" action="" style="display:inline;" onsubmit="return confirm('确定要删除此人员吗？');">
                                        <input type="hidden" name="personnel_id" value="${person.id}">
                                        <button type="submit" name="delete_personnel" class="btn btn-outline-danger" title="删除">
                                            <i class="fas fa-trash-alt"></i>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                    `;
                    tbody.append(row);
                });

                // 重新绑定编辑按钮事件
                bindEditButtons();
                // 重新绑定移动按钮事件
                bindMoveButtons();
                // 重新绑定开关事件
                bindToggleEvents();
            }

            // 绑定编辑按钮事件
            function bindEditButtons() {
                $(document).off('click', '.edit-btn').on('click', '.edit-btn', function(e) {
                    e.preventDefault();

                    var id = $(this).data('id');
                    var dept = $(this).data('dept');
                    var name = $(this).data('name');
                    var fixed = $(this).data('fixed');
                    var floating = $(this).data('floating');
                    var visible = $(this).data('visible');
                    var fixedBonus = $(this).data('fixed-bonus');

                    console.log('编辑按钮点击:', {id, dept, name, fixed, floating, visible, fixedBonus});

                    $('#edit_id').val(id);
                    $('#edit_dept').val(dept);
                    $('#edit_name').val(name);
                    $('#edit_fixed_amount').val(Math.floor(fixed));
                    $('#edit_floating_amount').val(Math.floor(floating));
                    $('#edit_is_visible').prop('checked', visible == 1);
                    $('#edit_is_fixed_bonus').prop('checked', fixedBonus == 1);

                    $('#editModal').modal('show');
                });
            }

            // 绑定移动按钮事件
            function bindMoveButtons() {
                $('.move-btn').off('click').on('click', function() {
                    const button = $(this);
                    const personnelId = button.data('id');
                    const direction = button.data('direction');

                    // 禁用所有移动按钮，防止重复点击
                    $('.move-btn').prop('disabled', true);

                    // 显示加载状态
                    const originalHtml = button.html();
                    button.html('<i class="fas fa-spinner fa-spin"></i>');

                    $.ajax({
                        url: 'personnel_manage.php',
                        method: 'POST',
                        data: {
                            ajax_move: 1,
                            personnel_id: personnelId,
                            direction: direction
                        },
                        dataType: 'json',
                        success: function(response) {
                            if (response.success) {
                                // 重新构建表格内容
                                updatePersonnelTable(response.personnel);
                                showMessage(response.message, 'success');
                            } else {
                                showMessage(response.message, 'error');
                            }
                        },
                        error: function(xhr, status, error) {
                            showMessage('操作失败：' + error, 'error');
                        },
                        complete: function() {
                            // 恢复按钮状态
                            button.html(originalHtml);
                            $('.move-btn').prop('disabled', false);
                        }
                    });
                });
            }

            // 显示消息
            function showMessage(message, type) {
                const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
                const alertHtml = `
                    <div class="alert ${alertClass} alert-dismissible fade show floating-alert" role="alert">
                        ${message}
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                `;

                // 移除现有的提示
                $('.floating-alert').remove();

                // 添加新提示
                $('body').append(alertHtml);

                // 3秒后自动关闭
                setTimeout(function() {
                    $('.floating-alert').addClass('hiding');
                    setTimeout(function() {
                        $('.floating-alert').alert('close');
                    }, 500);
                }, 3000);
            }

            // 绑定开关事件
            function bindToggleEvents() {
                // 显示状态开关事件
                $(document).off('change', '.visibility-toggle').on('change', '.visibility-toggle', function() {
                    const checkbox = $(this);
                    const personnelId = checkbox.data('personnel-id');
                    const isVisible = checkbox.is(':checked');

                    // 禁用开关，防止重复操作
                    checkbox.prop('disabled', true);

                    $.ajax({
                        url: 'personnel_manage.php',
                        method: 'POST',
                        data: {
                            ajax_visibility_toggle: 1,
                            personnel_id: personnelId,
                            is_visible: isVisible ? 1 : 0
                        },
                        dataType: 'json',
                        success: function(response) {
                            if (response.success) {
                                showMessage(response.message, 'success');
                            } else {
                                // 操作失败，恢复开关状态
                                checkbox.prop('checked', !isVisible);
                                showMessage(response.message, 'error');
                            }
                        },
                        error: function(xhr, status, error) {
                            // 操作失败，恢复开关状态
                            checkbox.prop('checked', !isVisible);
                            showMessage('操作失败：' + error, 'error');
                        },
                        complete: function() {
                            // 重新启用开关
                            checkbox.prop('disabled', false);
                        }
                    });
                });

                // 固定奖金开关事件
                $(document).off('change', '.fixed-bonus-toggle').on('change', '.fixed-bonus-toggle', function() {
                    const checkbox = $(this);
                    const personnelId = checkbox.data('personnel-id');
                    const isFixedBonus = checkbox.is(':checked');

                    // 禁用开关，防止重复操作
                    checkbox.prop('disabled', true);

                    $.ajax({
                        url: 'personnel_manage.php',
                        method: 'POST',
                        data: {
                            ajax_fixed_bonus_toggle: 1,
                            personnel_id: personnelId,
                            is_fixed_bonus: isFixedBonus ? 1 : 0
                        },
                        dataType: 'json',
                        success: function(response) {
                            if (response.success) {
                                showMessage(response.message, 'success');
                            } else {
                                // 操作失败，恢复开关状态
                                checkbox.prop('checked', !isFixedBonus);
                                showMessage(response.message, 'error');
                            }
                        },
                        error: function(xhr, status, error) {
                            // 操作失败，恢复开关状态
                            checkbox.prop('checked', !isFixedBonus);
                            showMessage('操作失败：' + error, 'error');
                        },
                        complete: function() {
                            // 重新启用开关
                            checkbox.prop('disabled', false);
                        }
                    });
                });
            }

            // 初始绑定事件
            bindEditButtons();
            bindMoveButtons();
            bindToggleEvents();

            // 3秒后自动关闭提示消息
            setTimeout(function() {
                $('.floating-alert').addClass('hiding');
                setTimeout(function() {
                    $('.floating-alert').alert('close');
                }, 500);
            }, 3000);
        });
    </script>
</div>
</body>
</html>