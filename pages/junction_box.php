<?php
require '../includes/config.php';
requireRole(['admin','manager']);

// 处理表单提交
if($_SERVER['REQUEST_METHOD'] == 'POST') {
    // 添加调试信息
    error_log("收到POST请求: " . print_r($_POST, true));
    
    $rcpId = $_POST['rcp_id'];
    $name = trim($_POST['name']);
    $remark = $_POST['remark'];

    try {
        // 检查名称唯一性
        $check = $pdo->prepare("SELECT id FROM junction_box WHERE name = ?");
        $check->execute([$name]);
        if($check->fetch() && empty($_POST['id'])) {
            die(json_encode(['status'=>'error','message'=>'分线箱名称已存在']));
        }

        if(isset($_POST['id']) && !empty($_POST['id'])) {
            // 更新
            $stmt = $pdo->prepare("UPDATE junction_box SET rcp_id=?, name=?, remark=? WHERE id=?");
            $stmt->execute([$rcpId, $name, $remark, $_POST['id']]);
            logAction('update', 'junction_box', ['id' => $_POST['id']]);
            
            // 添加调试信息
            error_log("更新分线箱成功: ID=" . $_POST['id']);
        } else {
            // 新建
            $stmt = $pdo->prepare("INSERT INTO junction_box (rcp_id, name, remark) VALUES (?,?,?)");
            $stmt->execute([$rcpId, $name, $remark]);
            $newId = $pdo->lastInsertId();
            logAction('create', 'junction_box', ['name' => $name, 'id' => $newId]);
            
            // 添加调试信息
            error_log("创建分线箱成功: ID=" . $newId);
        }
        
        // 检查是否是AJAX请求
        if(!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
            echo json_encode(['status'=>'success', 'message'=>'操作成功']);
        } else {
            // 直接表单提交，重定向到第一页
            header("Location: junction_box.php?added=1");
        }
        exit();
    } catch(PDOException $e) {
        error_log("数据库错误: " . $e->getMessage());
        die(json_encode(['status'=>'error','message'=>$e->getMessage()]));
    }
}

// 删除处理
if(isset($_GET['delete'])) {
    $stmt = $pdo->prepare("DELETE FROM junction_box WHERE id = ?");
    $stmt->execute([$_GET['delete']]);
    logAction('delete', 'junction_box', ['id' => $_GET['delete']]);
    
    // 保持分页和搜索状态
    $page = isset($_GET['page']) ? intval($_GET['page']) : 1;
    $search = isset($_GET['search']) ? urlencode($_GET['search']) : '';
    header("Location: junction_box.php?page=$page&search=$search&deleted=1");
    exit();
}

// 获取所有RCP柜和电源柜
$rcpCabinets = $pdo->query("
    SELECT r.*, p.name AS power_name 
    FROM rcp_cabinet r
    JOIN power_cabinet p ON r.power_id = p.id
")->fetchAll();

// 分页和搜索参数
$page = isset($_GET['page']) ? intval($_GET['page']) : 1;
$limit = 10; // 每页显示10条
$offset = ($page - 1) * $limit;
$search = isset($_GET['search']) ? trim($_GET['search']) : '';

// 获取分线箱数据（添加搜索和分页）
$params = [];
$whereClause = "";

if (!empty($search)) {
    $whereClause = " WHERE j.name LIKE ? OR r.name LIKE ? OR p.name LIKE ?";
    $params = ["%$search%", "%$search%", "%$search%"];
}

// 获取总记录数
$countSql = "SELECT COUNT(*) FROM junction_box j
             JOIN rcp_cabinet r ON j.rcp_id = r.id
             JOIN power_cabinet p ON r.power_id = p.id" . $whereClause;
$stmt = $pdo->prepare($countSql);
$stmt->execute($params);
$totalRecords = $stmt->fetchColumn();
$totalPages = ceil($totalRecords / $limit);

// 获取分页数据 - 修改查询以包含power_id并调整排序
$sql = "SELECT j.*, r.name AS rcp_name, r.power_id, p.name AS power_name 
        FROM junction_box j
        JOIN rcp_cabinet r ON j.rcp_id = r.id
        JOIN power_cabinet p ON r.power_id = p.id" . 
        $whereClause . " ORDER BY r.power_id ASC, j.rcp_id ASC, j.id ASC LIMIT $offset, $limit";
$stmt = $pdo->prepare($sql);
$stmt->execute($params);
$boxes = $stmt->fetchAll();
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>分线箱管理</title>
    <link rel="stylesheet" href="../assets/css/bootstrap.min.css">
    <link href="../assets/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/apple-style.css" rel="stylesheet">
    <script src="../assets/js/jquery.min.js"></script>
    <script src="../assets/js/bootstrap.bundle.min.js"></script>
    <style>
        /* 表格文字居中 */
        .table th, .table td {
            text-align: center;
            vertical-align: middle;
        }
        
        /* RCP柜名称样式 */
        .rcp-name {
            cursor: pointer;
            color: #28a745;
            text-decoration: underline;
        }
        
        .rcp-name:hover {
            color: #218838;
        }
        
        /* 分线箱名称样式 */
        .box-name {
            cursor: pointer;
            color: #007bff;
            text-decoration: underline;
        }
        
        .box-name:hover {
            color: #0056b3;
        }
        
        /* 位号列表样式 */
        .tag-list {
            display: none;
            margin-top: 10px;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 4px;
            border: 1px solid #dee2e6;
        }
        
        .tag-list table {
            margin-bottom: 0;
        }
    </style>
</head>
<body>
<div class="container-fluid px-4">
    <!-- 页面标题 -->
    <div class="page-header">
        <h3><i class="fas fa-box mr-2"></i>分线箱管理</h3>
        <button class="btn btn-primary" data-toggle="modal" data-target="#boxModal">
            <i class="fas fa-plus"></i> 添加分线箱
        </button>
    </div>

    <div class="row mb-3">
        <div class="col-md-6">
        </div>
        <div class="col-md-6">
            <form class="form-inline justify-content-end" method="get">
                <small class="form-text text-muted mr-2">提示：可使用空格分隔多个关键词进行组合搜索</small>
                <div class="input-group">
                    <input type="text" class="form-control" name="search" placeholder="搜索分线箱..." value="<?= htmlspecialchars($search) ?>">
                    <div class="input-group-append">
                        <button class="btn btn-outline-secondary" type="submit">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <table class="table table-bordered table-hover mt-3">
        <thead class="thead-light">
            <tr>
                <th>序号</th>
                <th>所属电源柜</th>
                <th>所属RCP柜</th>
                <th>分线箱名称</th>
                <th>备注</th>
                <th>操作</th>
            </tr>
        </thead>
        <tbody>
            <?php 
            $index = $offset + 1; 
            $currentRcpId = null;
            $rcpRowspan = 0;
            
            // 预处理计算每个RCP柜的行数
            $rcpCounts = [];
            
            foreach($boxes as $box) {
                if (!isset($rcpCounts[$box['rcp_id']])) {
                    $rcpCounts[$box['rcp_id']] = 0;
                }
                $rcpCounts[$box['rcp_id']]++;
            }
            
            foreach($boxes as $box): 
                $showRcp = false;
                
                // 检查是否需要显示RCP柜和电源柜
                if ($currentRcpId !== $box['rcp_id']) {
                    $currentRcpId = $box['rcp_id'];
                    $rcpRowspan = $rcpCounts[$currentRcpId];
                    $showRcp = true;
                }
            ?>
            <tr>
                <td><?= $index++ ?></td>
                
                <?php if ($showRcp): ?>
                <td rowspan="<?= $rcpRowspan ?>"><?= htmlspecialchars($box['power_name']) ?></td>
                <td rowspan="<?= $rcpRowspan ?>">
                    <span class="rcp-name" data-rcp-id="<?= $box['rcp_id'] ?>"><?= htmlspecialchars($box['rcp_name']) ?></span>
                    <div id="rcp-tags-<?= $box['rcp_id'] ?>" class="tag-list">
                        <div class="text-center">
                            <div class="spinner-border spinner-border-sm" role="status">
                                <span class="sr-only">加载中...</span>
                            </div>
                            <span>加载位号数据...</span>
                        </div>
                    </div>
                </td>
                <?php endif; ?>
                
                <td>
                    <span class="box-name" data-id="<?= $box['id'] ?>"><?= htmlspecialchars($box['name']) ?></span>
                    <div id="tags-<?= $box['id'] ?>" class="tag-list">
                        <div class="text-center">
                            <div class="spinner-border spinner-border-sm" role="status">
                                <span class="sr-only">加载中...</span>
                            </div>
                            <span>加载位号数据...</span>
                        </div>
                    </div>
                </td>
                <td><?= htmlspecialchars($box['remark'] ?? '') ?></td>
                <td>
                    <button class="btn btn-sm btn-warning edit-btn"
                            data-id="<?= $box['id'] ?>"
                            data-rcp-id="<?= $box['rcp_id'] ?>"
                            data-name="<?= htmlspecialchars($box['name']) ?>"
                            data-remark="<?= htmlspecialchars($box['remark'] ?? '') ?>">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-sm btn-danger delete-btn" 
                            data-id="<?= $box['id'] ?>">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            </tr>
            <?php endforeach; ?>
            
            <?php if(count($boxes) == 0): ?>
            <tr>
                <td colspan="6" class="text-center">没有找到匹配的记录</td>
            </tr>
            <?php endif; ?>
        </tbody>
    </table>

    <!-- 分页导航 -->
    <?php if($totalPages > 1): ?>
    <nav>
        <ul class="pagination justify-content-center">
            <li class="page-item <?= ($page <= 1) ? 'disabled' : '' ?>">
                <a class="page-link" href="?page=<?= $page-1 ?>&search=<?= urlencode($search) ?>">上一页</a>
            </li>
            
            <?php for($i = 1; $i <= $totalPages; $i++): ?>
            <li class="page-item <?= ($page == $i) ? 'active' : '' ?>">
                <a class="page-link" href="?page=<?= $i ?>&search=<?= urlencode($search) ?>"><?= $i ?></a>
            </li>
            <?php endfor; ?>
            
            <li class="page-item <?= ($page >= $totalPages) ? 'disabled' : '' ?>">
                <a class="page-link" href="?page=<?= $page+1 ?>&search=<?= urlencode($search) ?>">下一页</a>
            </li>
        </ul>
    </nav>
    <?php endif; ?>

    <!-- 模态框 -->
    <div class="modal fade" id="boxModal">
        <div class="modal-dialog">
            <div class="modal-content">
                <form id="boxForm" method="post" action="junction_box.php">
                    <input type="hidden" name="id" id="boxId">
                    <div class="modal-header">
                        <h5 class="modal-title">分线箱信息</h5>
                        <button type="button" class="close" data-dismiss="modal">&times;</button>
                    </div>
                    <div class="modal-body">
                        <div class="form-group">
                            <label>所属RCP柜</label>
                            <select name="rcp_id" class="form-control" required>
                                <?php foreach($rcpCabinets as $rcp): ?>
                                <option value="<?= $rcp['id'] ?>"><?= $rcp['power_name'] ?> - <?= $rcp['name'] ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>分线箱名称</label>
                            <input type="text" name="name" class="form-control" required>
                        </div>
                        <div class="form-group">
                            <label>备注</label>
                            <textarea name="remark" class="form-control"></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                        <button type="button" id="saveButton" class="btn btn-primary">保存</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
// 修复编辑按钮 - 使用data属性而不是JSON
$('.edit-btn').click(function(){
    const id = $(this).data('id');
    const rcpId = $(this).data('rcp-id');
    const name = $(this).data('name');
    const remark = $(this).data('remark');
    
    $('#boxId').val(id);
    $('select[name="rcp_id"]').val(rcpId);
    $('input[name="name"]').val(name);
    $('textarea[name="remark"]').val(remark);
    $('#boxModal').modal('show');
});

// 删除确认
$('.delete-btn').click(function(){
    if(confirm('确定删除此分线箱？')) {
        window.location = 'junction_box.php?delete=' + $(this).data('id') + '&page=<?= $page ?>&search=<?= urlencode($search) ?>';
    }
});

// 清空模态框（添加新分线箱时）
$('[data-target="#boxModal"]').click(function() {
    $('#boxId').val('');
    $('#boxForm')[0].reset();
    // 确保第一个RCP柜被选中
    if($('select[name="rcp_id"] option').length > 0) {
        $('select[name="rcp_id"]').val($('select[name="rcp_id"] option:first').val());
    }
});

// 使用直接表单提交而不是AJAX
$('#saveButton').click(function() {
    // 表单验证
    const name = $('input[name="name"]').val().trim();
    if(!name) {
        alert('分线箱名称不能为空');
        return false;
    }
    
    // 显示加载状态
    const submitBtn = $(this);
    const originalText = submitBtn.html();
    submitBtn.html('<i class="fas fa-spinner fa-spin"></i> 处理中...').prop('disabled', true);
    
    // 直接提交表单
    $('#boxForm').submit();
});

// 添加成功和删除成功的提示
$(document).ready(function() {
    // 检查URL参数
    const urlParams = new URLSearchParams(window.location.search);
    
    // 添加成功提示
    if (urlParams.get('added') === '1') {
        alert('分线箱添加成功！');
    }
    
    // 删除成功提示
    if (urlParams.get('deleted') === '1') {
        alert('删除成功！');
    }
});

// 点击分线箱名称显示位号
$(document).on('click', '.box-name', function() {
    const boxId = $(this).data('id');
    const tagList = $(`#tags-${boxId}`);
    const isVisible = tagList.is(':visible');
    
    // 先关闭所有打开的位号列表
    $('.tag-list').slideUp();
    
    // 如果当前点击的位号列表已经是打开状态，则不需要再次打开
    if (isVisible) {
        return;
    }
    
    // 打开当前点击的位号列表
    tagList.slideDown();
    
    // 检查是否已加载数据
    if (tagList.data('loaded')) {
        return;
    }
    
    // 加载位号数据
    $.ajax({
        url: '../api/get_tags.php',
        type: 'GET',
        data: { box_id: boxId },
        dataType: 'json',
        success: function(response) {
            if (response.status === 'success') {
                // 构建位号表格
                let tableHtml = `
                    <table class="table table-sm table-striped">
                        <thead>
                            <tr>
                                <th>序号</th>
                                <th>位号</th>
                                <th>位号名称</th>
                                <th>备注</th>
                            </tr>
                        </thead>
                        <tbody>
                `;
                
                if (response.data.length > 0) {
                    response.data.forEach((tag, index) => {
                        tableHtml += `
                            <tr>
                                <td>${index + 1}</td>
                                <td>${tag.tag_number || ''}</td>
                                <td>${tag.tag_name || ''}</td>
                                <td>${tag.remark || ''}</td>
                            </tr>
                        `;
                    });
                } else {
                    tableHtml += `
                        <tr>
                            <td colspan="4" class="text-center">该分线箱下没有位号</td>
                        </tr>
                    `;
                }
                
                tableHtml += `
                        </tbody>
                    </table>
                `;
                
                tagList.html(tableHtml);
                tagList.data('loaded', true);
            } else {
                tagList.html(`<div class="alert alert-danger">${response.message || '加载位号失败'}</div>`);
            }
        },
        error: function(xhr, status, error) {
            console.error('加载位号失败:', error);
            console.log('响应:', xhr.responseText);
            tagList.html('<div class="alert alert-danger">加载位号失败，请稍后再试</div>');
        }
    });
});

// 点击RCP柜名称显示位号 - 修改表格结构，删除分线箱列
$(document).on('click', '.rcp-name', function() {
    const rcpId = $(this).data('rcp-id');
    const tagList = $(`#rcp-tags-${rcpId}`);
    const isVisible = tagList.is(':visible');
    
    // 先关闭所有打开的位号列表
    $('.tag-list').slideUp();
    
    // 如果当前点击的位号列表已经是打开状态，则不需要再次打开
    if (isVisible) {
        return;
    }
    
    // 打开当前点击的位号列表
    tagList.slideDown();
    
    // 检查是否已加载数据
    if (tagList.data('loaded')) {
        return;
    }
    
    // 加载位号数据
    $.ajax({
        url: '../api/get_rcp_tags.php',
        type: 'GET',
        data: { rcp_id: rcpId },
        dataType: 'json',
        success: function(response) {
            if (response.status === 'success') {
                // 构建位号表格 - 删除分线箱列
                let tableHtml = `
                    <table class="table table-sm table-striped">
                        <thead>
                            <tr>
                                <th>序号</th>
                                <th>位号</th>
                                <th>位号名称</th>
                                <th>备注</th>
                            </tr>
                        </thead>
                        <tbody>
                `;
                
                if (response.data.length > 0) {
                    response.data.forEach((tag, index) => {
                        tableHtml += `
                            <tr>
                                <td>${index + 1}</td>
                                <td>${tag.tag_number || ''}</td>
                                <td>${tag.tag_name || ''}</td>
                                <td>${tag.remark || ''}</td>
                            </tr>
                        `;
                    });
                } else {
                    tableHtml += `
                        <tr>
                            <td colspan="4" class="text-center">该RCP柜下没有直接关联的位号</td>
                        </tr>
                    `;
                }
                
                tableHtml += `
                        </tbody>
                    </table>
                `;
                
                tagList.html(tableHtml);
                tagList.data('loaded', true);
            } else {
                tagList.html(`<div class="alert alert-danger">${response.message || '加载位号失败'}</div>`);
            }
        },
        error: function(xhr, status, error) {
            console.error('加载位号失败:', error);
            console.log('响应:', xhr.responseText);
            tagList.html('<div class="alert alert-danger">加载位号失败，请稍后再试</div>');
        }
    });
});
</script>

<!-- 添加错误修复脚本 -->
<script src="../assets/js/fix_frontend_errors.js"></script>
</body>
</html>