<?php
require_once '../includes/config.php';

// 检查用户是否已登录且为管理员
if (!isset($_SESSION['user']) || $_SESSION['user']['role'] !== 'admin') {
    header('Location: login.php');
    exit;
}

$user = $_SESSION['user'];
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>意见反馈管理 - 平台常用工具微信小程序后台管理</title>
    <link href="../assets/css/bootstrap.min.css" rel="stylesheet">
    <link href="../assets/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/apple-style.css" rel="stylesheet">
    <style>
        .feedback-card {
            background: #ffffff;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
            border: 1px solid rgba(0, 0, 0, 0.04);
            transition: all 0.2s ease;
        }
        
        .feedback-card:hover {
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
            transform: translateY(-1px);
        }
        
        .feedback-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 12px;
        }
        
        .feedback-type {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
            margin-right: 8px;
        }
        
        .feedback-type.建议 { background: #e3f2fd; color: #1976d2; }
        .feedback-type.问题反馈 { background: #fff3e0; color: #f57c00; }
        .feedback-type.功能需求 { background: #f3e5f5; color: #7b1fa2; }
        .feedback-type.其他 { background: #e8f5e8; color: #388e3c; }
        
        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-badge.unread { background: #fff3cd; color: #856404; }
        .status-badge.read { background: #cce5ff; color: #004085; }
        .status-badge.processed { background: #d4edda; color: #155724; }
        

        
        .feedback-content {
            color: #666;
            line-height: 1.5;
            margin-bottom: 12px;
            max-height: 60px;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        
        .feedback-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 13px;
            color: #999;
        }
        
        .user-info {
            display: flex;
            align-items: center;
        }
        
        .user-avatar {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background: #007aff;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: 500;
            margin-right: 8px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
            margin-bottom: 24px;
        }
        
        .stat-card {
            background: #ffffff;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
            border: 1px solid rgba(0, 0, 0, 0.04);
        }
        
        .stat-number {
            font-size: 32px;
            font-weight: 600;
            color: #007aff;
            margin-bottom: 4px;
        }
        
        .stat-label {
            font-size: 14px;
            color: #666;
            font-weight: 500;
        }
        
        .filters-container {
            background: #ffffff;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 24px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
            border: 1px solid rgba(0, 0, 0, 0.04);
        }
        
        .filter-row {
            display: flex;
            gap: 16px;
            align-items: end;
            flex-wrap: wrap;
        }
        
        .filter-group {
            display: flex;
            flex-direction: column;
            min-width: 120px;
        }

        .filter-group:last-child {
            min-width: auto;
        }

        .button-group {
            gap: 12px !important;
        }

        .button-group .btn + .btn {
            margin-left: 12px !important;
        }
        
        .filter-group label {
            font-size: 13px;
            color: #666;
            margin-bottom: 4px;
            font-weight: 500;
        }
        
        .form-control, .form-select {
            border-radius: 8px;
            border: 1px solid #d1d5db;
            padding: 8px 12px;
            font-size: 14px;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: #007aff;
            box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
        }
        
        .btn-apple {
            background: #007aff;
            color: white;
            border: none;
            border-radius: 8px;
            padding: 8px 16px;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s ease;
        }
        
        .btn-apple:hover {
            background: #0056b3;
            color: white;
            transform: translateY(-1px);
        }
        
        .btn-apple-secondary {
            background: #f8f9fa;
            color: #666;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            padding: 8px 16px;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s ease;
        }
        
        .btn-apple-secondary:hover {
            background: #e9ecef;
            color: #495057;
            transform: translateY(-1px);
        }
        
        .pagination {
            justify-content: center;
            margin-top: 24px;
        }
        
        .page-link {
            border-radius: 8px;
            border: 1px solid #d1d5db;
            color: #666;
            margin: 0 2px;
        }
        
        .page-link:hover {
            background: #f8f9fa;
            color: #007aff;
        }
        
        .page-item.active .page-link {
            background: #007aff;
            border-color: #007aff;
        }
        
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #999;
        }
        
        .empty-state i {
            font-size: 48px;
            margin-bottom: 16px;
            color: #ddd;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
            color: #999;
        }

        /* 禁用按钮样式 */
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .btn-secondary {
            background-color: #6c757d;
            border-color: #6c757d;
            color: white;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .filter-row {
                flex-direction: column;
                align-items: stretch;
            }

            .filter-group {
                min-width: 100%;
            }

            .filter-group:last-child {
                margin-top: 16px;
            }

            .filter-group:last-child .d-flex {
                justify-content: center;
            }
        }
        
        .loading i {
            font-size: 24px;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 反馈详情模态框样式优化 */
        #feedbackDetailModal .modal-dialog {
            max-width: 800px;
        }

        #feedbackDetailModal .table td:first-child {
            font-weight: 500;
            color: #6c757d;
            white-space: nowrap;
        }

        #feedbackDetailModal .table td {
            border: none;
            padding: 8px 12px;
            vertical-align: middle;
        }

        #feedbackDetailModal .table {
            margin-bottom: 0;
        }

        #feedbackDetailModal .bg-light {
            background-color: #f8f9fa !important;
            border: 1px solid #e9ecef;
        }

        /* 删除按钮样式 */
        #deleteBtn {
            background-color: #dc3545;
            border-color: #dc3545;
            color: white;
        }

        #deleteBtn:hover {
            background-color: #c82333;
            border-color: #bd2130;
        }

        /* 删除模态框样式 */
        #deleteFeedbackModal .modal-header .modal-title {
            color: #dc3545;
        }

        #deleteFeedbackModal .alert-danger {
            border-color: #f5c6cb;
            background-color: #f8d7da;
            color: #721c24;
        }

        /* 标签页样式 */
        .nav-tabs-container {
            background: #ffffff;
            border-radius: 12px;
            padding: 20px 20px 0;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
            border: 1px solid rgba(0, 0, 0, 0.04);
        }

        .nav-tabs {
            border-bottom: 2px solid #f8f9fa;
        }

        .nav-tabs .nav-link {
            border: none;
            color: #8e8e93;
            font-weight: 500;
            padding: 12px 20px;
            margin-right: 8px;
            border-radius: 8px 8px 0 0;
            transition: all 0.2s ease;
        }

        .nav-tabs .nav-link:hover {
            color: #007aff;
            background-color: rgba(0, 122, 255, 0.05);
        }

        .nav-tabs .nav-link.active {
            color: #007aff;
            background-color: #ffffff;
            border-bottom: 2px solid #007aff;
        }

        /* 违规记录样式 */
        .violation-card {
            background: #ffffff;
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 12px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
            border: 1px solid rgba(0, 0, 0, 0.04);
            border-left: 4px solid #dc3545;
        }

        .violation-content {
            background-color: #f8f9fa;
            padding: 12px;
            border-radius: 8px;
            margin: 8px 0;
            font-family: monospace;
            font-size: 14px;
        }

        .sensitive-word-tag {
            display: inline-block;
            background-color: #dc3545;
            color: white;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 12px;
            margin: 2px;
        }

        /* 敏感词管理样式 */
        .sensitive-word-item {
            display: inline-block;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 20px;
            padding: 6px 12px;
            margin: 4px;
            position: relative;
        }

        .sensitive-word-item .word-text {
            margin-right: 8px;
        }

        .sensitive-word-item .word-category {
            font-size: 11px;
            color: #6c757d;
            margin-right: 8px;
        }

        .sensitive-word-item .delete-btn {
            background: none;
            border: none;
            color: #dc3545;
            cursor: pointer;
            font-size: 12px;
        }

        .add-word-form {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 12px;
            border: 1px solid #dee2e6;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <!-- 页面头部 -->
        <div class="page-header">
            <h3><i class="fas fa-comments mr-2"></i>意见反馈管理</h3>
            <div>
                <button class="btn btn-apple" onclick="refreshCurrentTab()">
                    <i class="fas fa-sync-alt mr-1"></i>刷新数据
                </button>
            </div>
        </div>

        <!-- 标签页导航 -->
        <div class="nav-tabs-container mb-4">
            <ul class="nav nav-tabs" id="managementTabs" role="tablist">
                <li class="nav-item">
                    <a class="nav-link active" id="feedback-tab" data-toggle="tab" href="#feedback-panel" role="tab" aria-controls="feedback-panel" aria-selected="true">
                        <i class="fas fa-comments mr-1"></i>反馈管理
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" id="violations-tab" data-toggle="tab" href="#violations-panel" role="tab" aria-controls="violations-panel" aria-selected="false">
                        <i class="fas fa-exclamation-triangle mr-1"></i>违规记录
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" id="sensitive-words-tab" data-toggle="tab" href="#sensitive-words-panel" role="tab" aria-controls="sensitive-words-panel" aria-selected="false">
                        <i class="fas fa-filter mr-1"></i>敏感词管理
                    </a>
                </li>
            </ul>
        </div>

        <!-- 标签页内容 -->
        <div class="tab-content" id="managementTabsContent">
            <!-- 反馈管理面板 -->
            <div class="tab-pane fade show active" id="feedback-panel" role="tabpanel">
                <!-- 统计卡片 -->
                <div class="stats-grid" id="statsGrid">
                    <div class="stat-card">
                        <div class="stat-number" id="totalCount">-</div>
                        <div class="stat-label">总反馈数</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="unreadCount">-</div>
                        <div class="stat-label">未读</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="readCount">-</div>
                        <div class="stat-label">已读</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="processedCount">-</div>
                        <div class="stat-label">已处理</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="recent7Days">-</div>
                        <div class="stat-label">最近7天</div>
                    </div>
                </div>

        <!-- 筛选器 -->
        <div class="filters-container">
            <div class="filter-row">
                <div class="filter-group">
                    <label>状态筛选</label>
                    <select class="form-select" id="statusFilter">
                        <option value="">全部状态</option>
                        <option value="unread">未读</option>
                        <option value="read">已读</option>
                        <option value="processed">已处理</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label>类型筛选</label>
                    <select class="form-select" id="typeFilter">
                        <option value="">全部类型</option>
                        <option value="建议">建议</option>
                        <option value="问题反馈">问题反馈</option>
                        <option value="功能需求">功能需求</option>
                        <option value="其他">其他</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label>搜索关键词</label>
                    <input type="text" class="form-control" id="searchInput" placeholder="搜索内容或联系方式">
                </div>
                <div class="filter-group">
                    <label>&nbsp;</label> <!-- 占位符，保持对齐 -->
                    <div class="d-flex button-group">
                        <button class="btn btn-apple" onclick="applyFilters()">
                            <i class="fas fa-search me-1"></i>搜索
                        </button>
                        <button class="btn btn-apple-secondary" onclick="resetFilters()">
                            <i class="fas fa-undo me-1"></i>重置
                        </button>
                    </div>
                </div>
            </div>
        </div>

                <!-- 反馈列表 -->
                <div id="feedbackList">
                    <div class="loading">
                        <i class="fas fa-spinner fa-spin"></i>
                        <div class="mt-2">加载中...</div>
                    </div>
                </div>

                <!-- 分页 -->
                <nav id="paginationNav" style="display: none;">
                    <ul class="pagination" id="pagination"></ul>
                </nav>
            </div>

            <!-- 违规记录面板 -->
            <div class="tab-pane fade" id="violations-panel" role="tabpanel">
                <div class="violations-container">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5><i class="fas fa-exclamation-triangle mr-2"></i>违规记录</h5>
                        <button class="btn btn-danger btn-sm" onclick="clearAllViolations()">
                            <i class="fas fa-trash mr-1"></i>清空记录
                        </button>
                    </div>

                    <!-- 违规记录列表 -->
                    <div id="violationsList">
                        <div class="loading">
                            <i class="fas fa-spinner fa-spin"></i>
                            <div class="mt-2">加载中...</div>
                        </div>
                    </div>

                    <!-- 违规记录分页 -->
                    <nav id="violationsPaginationNav" style="display: none;">
                        <ul class="pagination" id="violationsPagination"></ul>
                    </nav>
                </div>
            </div>

            <!-- 敏感词管理面板 -->
            <div class="tab-pane fade" id="sensitive-words-panel" role="tabpanel">
                <div class="sensitive-words-container">
                    <div class="row">
                        <div class="col-md-8">
                            <h5><i class="fas fa-filter mr-2"></i>敏感词库</h5>
                            <div class="sensitive-words-list" id="sensitiveWordsList">
                                <div class="loading">
                                    <i class="fas fa-spinner fa-spin"></i>
                                    <div class="mt-2">加载中...</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <h5><i class="fas fa-plus mr-2"></i>添加敏感词</h5>
                            <div class="add-word-form">
                                <div class="mb-3">
                                    <label class="form-label">敏感词</label>
                                    <input type="text" class="form-control" id="newSensitiveWord" placeholder="输入敏感词">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">分类</label>
                                    <select class="form-select" id="wordCategory">
                                        <option value="暴力">暴力相关</option>
                                        <option value="脏话">脏话粗口</option>
                                        <option value="非法">非法活动</option>
                                        <option value="色情">色情相关</option>
                                        <option value="政治">政治敏感</option>
                                        <option value="其他">其他</option>
                                    </select>
                                </div>
                                <button class="btn btn-apple w-100" onclick="addSensitiveWord()">
                                    <i class="fas fa-plus me-1"></i>添加
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 反馈详情模态框 -->
    <div class="modal fade" id="feedbackDetailModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">反馈详情</h5>
                    <button type="button" class="close" onclick="closeFeedbackDetail()" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body" id="feedbackDetailContent">
                    <!-- 详情内容将通过JavaScript动态加载 -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-danger" onclick="showDeleteModal()" id="deleteBtn">
                        <i class="fas fa-trash me-1"></i>删除反馈
                    </button>
                    <div class="ms-auto">
                        <button type="button" class="btn btn-apple-secondary me-2" onclick="closeFeedbackDetail()">关闭</button>
                        <button type="button" class="btn btn-apple" onclick="showUpdateModal()" id="processBtn">标记为已处理</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 处理反馈模态框 -->
    <div class="modal fade" id="updateFeedbackModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">处理反馈</h5>
                    <button type="button" class="close" onclick="closeUpdateModal()" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <input type="hidden" id="updateFeedbackId">
                    <p>确认要将此反馈标记为已处理吗？</p>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        标记为已处理后，此反馈将从未处理列表中移除。
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-apple-secondary" onclick="closeUpdateModal()">取消</button>
                    <button type="button" class="btn btn-apple" onclick="markAsProcessed()">标记为已处理</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 删除反馈模态框 -->
    <div class="modal fade" id="deleteFeedbackModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title text-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>删除反馈
                    </h5>
                    <button type="button" class="close" onclick="closeDeleteModal()" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <input type="hidden" id="deleteFeedbackId">
                    <p class="mb-3">确认要删除此反馈吗？</p>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>警告：</strong>删除操作不可恢复，反馈及其相关数据将被永久删除。
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="confirmDelete">
                        <label class="form-check-label" for="confirmDelete">
                            我确认要删除此反馈
                        </label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-apple-secondary" onclick="closeDeleteModal()">取消</button>
                    <button type="button" class="btn btn-danger" onclick="deleteFeedback()" id="confirmDeleteBtn" disabled>
                        <i class="fas fa-trash me-1"></i>确认删除
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="../assets/js/jquery.min.js"></script>
    <script src="../assets/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/fix_frontend_errors.js"></script>
    <script>
        let currentPage = 1;
        let currentFilters = {};
        let currentFeedbackId = null;

        $(document).ready(function() {
            // 更新父窗口中的菜单高亮状态和localStorage
            if (window.parent && window.parent.document) {
                window.parent.localStorage.setItem('currentPage', 'pages/feedback_management.php');

                const menuItems = window.parent.document.querySelectorAll('a[target="contentFrame"]');
                menuItems.forEach(item => {
                    item.classList.remove('active-menu-item');
                });
                const feedbackLink = window.parent.document.querySelector('a[data-page="pages/feedback_management.php"]');
                if (feedbackLink) {
                    feedbackLink.classList.add('active-menu-item');
                }
            }

            // 初始化页面
            loadStats();
            loadFeedbackList();

            // 绑定搜索框回车事件
            $('#searchInput').on('keypress', function(e) {
                if (e.which === 13) {
                    applyFilters();
                }
            });

            // 绑定删除确认复选框事件
            $(document).on('change', '#confirmDelete', function() {
                const isChecked = $(this).is(':checked');
                $('#confirmDeleteBtn').prop('disabled', !isChecked);
            });

            // 绑定标签页切换事件
            $('#managementTabs a[data-toggle="tab"]').on('shown.bs.tab', function (e) {
                const targetTab = e.target.id;

                switch(targetTab) {
                    case 'violations-tab':
                        loadViolationsList();
                        break;
                    case 'sensitive-words-tab':
                        loadSensitiveWords();
                        break;
                }
            });
        });

        // 加载统计数据
        function loadStats() {
            $.ajax({
                url: '../api/feedback_api.php',
                method: 'GET',
                dataType: 'json',
                data: {
                    action: 'get_feedback_stats'
                },
                success: function(response) {
                    if (response && response.success) {
                        const data = response.data;
                        $('#totalCount').text(data.total || 0);
                        $('#unreadCount').text(data.unread || 0);
                        $('#readCount').text(data.read || 0);
                        $('#processedCount').text(data.processed || 0);
                        $('#recent7Days').text(data.recent_7_days || 0);
                    } else {
                        showError('加载统计数据失败：' + (response ? response.message : '响应格式错误'));
                        // 设置默认值
                        $('#totalCount, #unreadCount, #readCount, #processedCount, #recent7Days').text('0');
                    }
                },
                error: function(xhr, status, error) {
                    showError('加载统计数据失败：' + error);
                    // 设置默认值
                    $('#totalCount, #pendingCount, #processingCount, #resolvedCount, #recent7Days').text('0');
                }
            });
        }

        // 加载反馈列表
        function loadFeedbackList(page = 1) {
            currentPage = page;

            const params = {
                action: 'get_feedback_list',
                page: page,
                limit: 10,
                ...currentFilters
            };

            $('#feedbackList').html(`
                <div class="loading">
                    <i class="fas fa-spinner fa-spin"></i>
                    <div class="mt-2">加载中...</div>
                </div>
            `);

            $.ajax({
                url: '../api/feedback_api.php',
                method: 'GET',
                dataType: 'json',
                data: params,
                success: function(response) {
                    if (response.success) {
                        renderFeedbackList(response.data.list);
                        renderPagination(response.data);
                    } else {
                        showError('加载反馈列表失败：' + response.message);
                    }
                },
                error: function() {
                    showError('加载反馈列表失败，请检查网络连接');
                }
            });
        }

        // 渲染反馈列表
        function renderFeedbackList(feedbacks) {
            if (feedbacks.length === 0) {
                $('#feedbackList').html(`
                    <div class="empty-state">
                        <i class="fas fa-inbox"></i>
                        <div class="mt-2">暂无反馈数据</div>
                    </div>
                `);
                return;
            }

            let html = '';
            feedbacks.forEach(feedback => {
                const userInitial = feedback.user_info.real_name ?
                    feedback.user_info.real_name.charAt(0) :
                    (feedback.user_info.username ? feedback.user_info.username.charAt(0) : '匿');

                html += `
                    <div class="feedback-card" onclick="showFeedbackDetail(${feedback.id})">
                        <div class="feedback-header">
                            <div>
                                <span class="feedback-type ${feedback.feedback_type}">${feedback.feedback_type}</span>
                                <span class="status-badge ${feedback.status}">${feedback.status_text}</span>
                            </div>
                            <div class="text-muted" style="font-size: 12px;">
                                ${formatDateTime(feedback.created_at)}
                            </div>
                        </div>
                        <div class="feedback-content">
                            ${escapeHtml(feedback.content)}
                        </div>
                        <div class="feedback-meta">
                            <div class="user-info">
                                <div class="user-avatar">${userInitial}</div>
                                <span>${feedback.user_info.real_name || feedback.user_info.username || '匿名用户'}</span>
                                ${feedback.contact_info ? `<span class="ms-2 text-muted">• ${escapeHtml(feedback.contact_info)}</span>` : ''}
                            </div>
                            <div>
                                ${feedback.admin_reply ? `<i class="fas fa-reply text-success me-1"></i>已回复` : ''}
                            </div>
                        </div>
                    </div>
                `;
            });

            $('#feedbackList').html(html);
        }

        // 渲染分页
        function renderPagination(data) {
            if (data.total_pages <= 1) {
                $('#paginationNav').hide();
                return;
            }

            let html = '';
            const currentPage = data.page;
            const totalPages = data.total_pages;

            // 上一页
            if (currentPage > 1) {
                html += `<li class="page-item"><a class="page-link" href="#" onclick="loadFeedbackList(${currentPage - 1})">上一页</a></li>`;
            }

            // 页码
            const startPage = Math.max(1, currentPage - 2);
            const endPage = Math.min(totalPages, currentPage + 2);

            if (startPage > 1) {
                html += `<li class="page-item"><a class="page-link" href="#" onclick="loadFeedbackList(1)">1</a></li>`;
                if (startPage > 2) {
                    html += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
                }
            }

            for (let i = startPage; i <= endPage; i++) {
                const activeClass = i === currentPage ? 'active' : '';
                html += `<li class="page-item ${activeClass}"><a class="page-link" href="#" onclick="loadFeedbackList(${i})">${i}</a></li>`;
            }

            if (endPage < totalPages) {
                if (endPage < totalPages - 1) {
                    html += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
                }
                html += `<li class="page-item"><a class="page-link" href="#" onclick="loadFeedbackList(${totalPages})">${totalPages}</a></li>`;
            }

            // 下一页
            if (currentPage < totalPages) {
                html += `<li class="page-item"><a class="page-link" href="#" onclick="loadFeedbackList(${currentPage + 1})">下一页</a></li>`;
            }

            $('#pagination').html(html);
            $('#paginationNav').show();
        }

        // 应用筛选器
        function applyFilters() {
            currentFilters = {
                status: $('#statusFilter').val(),
                type: $('#typeFilter').val(),
                search: $('#searchInput').val().trim()
            };

            // 移除空值
            Object.keys(currentFilters).forEach(key => {
                if (!currentFilters[key]) {
                    delete currentFilters[key];
                }
            });

            loadFeedbackList(1);
        }

        // 重置筛选器
        function resetFilters() {
            $('#statusFilter').val('');
            $('#typeFilter').val('');
            $('#searchInput').val('');
            currentFilters = {};
            loadFeedbackList(1);
        }

        // 刷新当前标签页数据
        function refreshCurrentTab() {
            const activeTab = document.querySelector('.nav-link.active').id;

            switch(activeTab) {
                case 'feedback-tab':
                    loadStats();
                    loadFeedbackList(currentPage);
                    break;
                case 'violations-tab':
                    loadViolationsList();
                    break;
                case 'sensitive-words-tab':
                    loadSensitiveWords();
                    break;
            }
        }

        // 刷新反馈数据（保持向后兼容）
        function refreshData() {
            loadStats();
            loadFeedbackList(currentPage);
        }

        // 关闭反馈详情模态框
        function closeFeedbackDetail() {
            $('#feedbackDetailModal').modal('hide');
        }

        // 关闭更新模态框
        function closeUpdateModal() {
            $('#updateFeedbackModal').modal('hide');
        }

        // 显示反馈详情
        function showFeedbackDetail(feedbackId) {
            currentFeedbackId = feedbackId;

            $.ajax({
                url: '../api/feedback_api.php',
                method: 'GET',
                dataType: 'json',
                data: {
                    action: 'get_feedback_detail',
                    feedback_id: feedbackId
                },
                success: function(response) {
                    if (response.success) {
                        renderFeedbackDetail(response.data);
                        $('#feedbackDetailModal').modal('show');
                    } else {
                        showError('加载反馈详情失败：' + response.message);
                    }
                },
                error: function() {
                    showError('加载反馈详情失败，请检查网络连接');
                }
            });
        }

        // 渲染反馈详情
        function renderFeedbackDetail(feedback) {
            const userInfo = feedback.user_info;
            const userName = userInfo.real_name || userInfo.username || '匿名用户';

            const html = `
                <!-- 基本信息在上方 -->
                <div class="mb-4">
                    <h6 class="text-muted mb-3">基本信息</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-sm">
                                <tr>
                                    <td style="width: 80px;">反馈类型</td>
                                    <td><span class="feedback-type ${feedback.feedback_type}">${feedback.feedback_type}</span></td>
                                </tr>
                                <tr>
                                    <td>当前状态</td>
                                    <td><span class="status-badge ${feedback.status}">${feedback.status_text}</span></td>
                                </tr>
                                <tr>
                                    <td>提交用户</td>
                                    <td>${userName}</td>
                                </tr>
                                ${feedback.contact_info ? `
                                    <tr>
                                        <td>联系方式</td>
                                        <td>${escapeHtml(feedback.contact_info)}</td>
                                    </tr>
                                ` : ''}
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-sm">
                                <tr>
                                    <td style="width: 80px;">提交时间</td>
                                    <td>${formatDateTime(feedback.created_at)}</td>
                                </tr>
                                <tr>
                                    <td>更新时间</td>
                                    <td>${formatDateTime(feedback.updated_at)}</td>
                                </tr>
                                ${feedback.read_at ? `
                                    <tr>
                                        <td>已读时间</td>
                                        <td>${formatDateTime(feedback.read_at)}</td>
                                    </tr>
                                ` : ''}
                                ${feedback.processed_at ? `
                                    <tr>
                                        <td>处理时间</td>
                                        <td>${formatDateTime(feedback.processed_at)}</td>
                                    </tr>
                                ` : ''}
                            </table>
                        </div>
                    </div>
                </div>

                <!-- 详情内容在下方 -->
                <div>
                    <h6 class="text-muted mb-3">反馈内容</h6>
                    <div class="p-3 bg-light rounded">
                        ${escapeHtml(feedback.content).replace(/\n/g, '<br>')}
                    </div>
                </div>
            `;

            $('#feedbackDetailContent').html(html);

            // 根据状态控制按钮
            const processBtn = $('#processBtn');
            if (feedback.status === 'processed') {
                processBtn.text('已处理').prop('disabled', true).removeClass('btn-apple').addClass('btn-secondary');
            } else {
                processBtn.text('标记为已处理').prop('disabled', false).removeClass('btn-secondary').addClass('btn-apple');
            }
        }

        // 显示更新模态框
        function showUpdateModal() {
            if (!currentFeedbackId) return;

            // 检查按钮是否被禁用（已处理状态）
            if ($('#processBtn').prop('disabled')) {
                showError('此反馈已经处理完成');
                return;
            }

            $('#updateFeedbackId').val(currentFeedbackId);
            closeFeedbackDetail();
            $('#updateFeedbackModal').modal('show');
        }

        // 标记为已处理
        function markAsProcessed() {
            const feedbackId = $('#updateFeedbackId').val();

            if (!feedbackId) {
                showError('反馈ID不能为空');
                return;
            }

            const data = {
                action: 'update_feedback',
                feedback_id: feedbackId,
                action_type: 'mark_processed'
            };

            $.ajax({
                url: '../api/feedback_api.php',
                method: 'POST',
                dataType: 'json',
                contentType: 'application/json',
                data: JSON.stringify(data),
                success: function(response) {
                    if (response.success) {
                        showSuccess('反馈已标记为已处理');
                        closeUpdateModal();
                        refreshData();
                    } else {
                        showError('操作失败：' + response.message);
                    }
                },
                error: function(xhr, status, error) {
                    showError('操作失败，请检查网络连接');
                }
            });
        }

        // 显示删除模态框
        function showDeleteModal() {
            if (!currentFeedbackId) return;

            $('#deleteFeedbackId').val(currentFeedbackId);
            $('#confirmDelete').prop('checked', false);
            $('#confirmDeleteBtn').prop('disabled', true);
            closeFeedbackDetail();
            $('#deleteFeedbackModal').modal('show');
        }

        // 关闭删除模态框
        function closeDeleteModal() {
            $('#deleteFeedbackModal').modal('hide');
            $('#deleteFeedbackId').val('');
            $('#confirmDelete').prop('checked', false);
            $('#confirmDeleteBtn').prop('disabled', true);
        }

        // 删除反馈
        function deleteFeedback() {
            const feedbackId = $('#deleteFeedbackId').val();

            if (!feedbackId) {
                showError('反馈ID不能为空');
                return;
            }

            if (!$('#confirmDelete').is(':checked')) {
                showError('请先确认删除操作');
                return;
            }

            const data = {
                action: 'delete_feedback',
                feedback_id: feedbackId
            };

            // 禁用删除按钮，防止重复提交
            $('#confirmDeleteBtn').prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-1"></i>删除中...');

            $.ajax({
                url: '../api/feedback_api.php',
                method: 'POST',
                dataType: 'json',
                contentType: 'application/json',
                data: JSON.stringify(data),
                success: function(response) {
                    if (response.success) {
                        showSuccess('反馈已成功删除');
                        closeDeleteModal();
                        refreshData();
                    } else {
                        showError('删除失败：' + response.message);
                        $('#confirmDeleteBtn').prop('disabled', false).html('<i class="fas fa-trash me-1"></i>确认删除');
                    }
                },
                error: function(xhr, status, error) {
                    showError('删除失败，请检查网络连接');
                    $('#confirmDeleteBtn').prop('disabled', false).html('<i class="fas fa-trash me-1"></i>确认删除');
                }
            });
        }

        // 工具函数
        function formatDateTime(dateTimeStr) {
            if (!dateTimeStr) return '-';
            const date = new Date(dateTimeStr);
            return date.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit'
            });
        }

        function escapeHtml(text) {
            if (!text) return '';
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        function showSuccess(message) {
            showMessage(message, 'success');
        }

        function showError(message) {
            showMessage(message, 'error');
        }

        function showMessage(message, type) {
            const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
            const iconClass = type === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle';

            const alert = $(`
                <div class="alert ${alertClass} alert-dismissible fade show position-fixed"
                     style="top: 20px; right: 20px; z-index: 9999; min-width: 300px;">
                    <i class="fas ${iconClass} me-2"></i>
                    ${message}
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
            `);

            $('body').append(alert);

            // 3秒后自动关闭
            setTimeout(() => {
                alert.alert('close');
            }, 3000);
        }

        // ==================== 违规记录管理 ====================

        // 加载违规记录列表
        function loadViolationsList(page = 1) {
            $('#violationsList').html(`
                <div class="loading">
                    <i class="fas fa-spinner fa-spin"></i>
                    <div class="mt-2">加载中...</div>
                </div>
            `);

            $.ajax({
                url: '../api/feedback_api.php',
                method: 'GET',
                dataType: 'json',
                data: {
                    action: 'get_violations_list',
                    page: page,
                    limit: 10
                },
                success: function(response) {
                    if (response.success) {
                        renderViolationsList(response.data.list);
                        renderViolationsPagination(response.data);
                    } else {
                        showError('加载违规记录失败：' + response.message);
                    }
                },
                error: function() {
                    showError('加载违规记录失败，请检查网络连接');
                }
            });
        }

        // 渲染违规记录列表
        function renderViolationsList(violations) {
            if (violations.length === 0) {
                $('#violationsList').html(`
                    <div class="empty-state">
                        <i class="fas fa-shield-alt"></i>
                        <div class="mt-2">暂无违规记录</div>
                    </div>
                `);
                return;
            }

            let html = '';
            violations.forEach(violation => {
                const sensitiveWords = JSON.parse(violation.sensitive_words || '[]');
                const userInfo = JSON.parse(violation.user_info || '{}');

                html += `
                    <div class="violation-card">
                        <div class="d-flex justify-content-between align-items-start mb-2">
                            <div>
                                <strong>违规时间：</strong>${formatDateTime(violation.created_at)}
                            </div>
                            <button class="btn btn-sm btn-outline-danger" onclick="deleteViolation(${violation.id})">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                        <div class="mb-2">
                            <strong>用户信息：</strong>
                            ${userInfo.real_name || userInfo.username || '匿名用户'}
                            ${userInfo.contact_info ? ` (${userInfo.contact_info})` : ''}
                            ${userInfo.ip_address ? ` - IP: ${userInfo.ip_address}` : ''}
                        </div>
                        <div class="mb-2">
                            <strong>触发敏感词：</strong>
                            ${sensitiveWords.map(word => `<span class="sensitive-word-tag">${escapeHtml(word)}</span>`).join('')}
                        </div>
                        <div class="mb-2">
                            <strong>违规内容：</strong>
                        </div>
                        <div class="violation-content">
                            ${escapeHtml(violation.content)}
                        </div>
                    </div>
                `;
            });

            $('#violationsList').html(html);
        }

        // 渲染违规记录分页
        function renderViolationsPagination(data) {
            // 实现分页逻辑（类似反馈列表的分页）
            // 这里简化处理
            $('#violationsPaginationNav').hide();
        }

        // 删除单个违规记录
        function deleteViolation(violationId) {
            if (!confirm('确认删除此违规记录吗？')) {
                return;
            }

            $.ajax({
                url: '../api/feedback_api.php',
                method: 'POST',
                dataType: 'json',
                contentType: 'application/json',
                data: JSON.stringify({
                    action: 'delete_violation',
                    violation_id: violationId
                }),
                success: function(response) {
                    if (response.success) {
                        showSuccess('违规记录已删除');
                        loadViolationsList();
                    } else {
                        showError('删除失败：' + response.message);
                    }
                },
                error: function() {
                    showError('删除失败，请检查网络连接');
                }
            });
        }

        // 清空所有违规记录
        function clearAllViolations() {
            if (!confirm('确认清空所有违规记录吗？此操作不可恢复！')) {
                return;
            }

            $.ajax({
                url: '../api/feedback_api.php',
                method: 'POST',
                dataType: 'json',
                contentType: 'application/json',
                data: JSON.stringify({
                    action: 'clear_all_violations'
                }),
                success: function(response) {
                    if (response.success) {
                        showSuccess('所有违规记录已清空');
                        loadViolationsList();
                    } else {
                        showError('清空失败：' + response.message);
                    }
                },
                error: function() {
                    showError('清空失败，请检查网络连接');
                }
            });
        }

        // ==================== 敏感词管理 ====================

        // 加载敏感词列表
        function loadSensitiveWords() {
            $('#sensitiveWordsList').html(`
                <div class="loading">
                    <i class="fas fa-spinner fa-spin"></i>
                    <div class="mt-2">加载中...</div>
                </div>
            `);

            $.ajax({
                url: '../api/feedback_api.php',
                method: 'GET',
                dataType: 'json',
                data: {
                    action: 'get_sensitive_words'
                },
                success: function(response) {
                    if (response.success) {
                        renderSensitiveWords(response.data);
                    } else {
                        showError('加载敏感词失败：' + response.message);
                    }
                },
                error: function() {
                    showError('加载敏感词失败，请检查网络连接');
                }
            });
        }

        // 渲染敏感词列表
        function renderSensitiveWords(words) {
            if (words.length === 0) {
                $('#sensitiveWordsList').html(`
                    <div class="empty-state">
                        <i class="fas fa-filter"></i>
                        <div class="mt-2">暂无敏感词</div>
                    </div>
                `);
                return;
            }

            // 按分类分组
            const categories = {};
            words.forEach(word => {
                const category = word.category || '其他';
                if (!categories[category]) {
                    categories[category] = [];
                }
                categories[category].push(word);
            });

            let html = '';
            Object.keys(categories).forEach(category => {
                html += `
                    <div class="mb-4">
                        <h6 class="text-muted mb-2">${category}</h6>
                        <div class="sensitive-words-group">
                `;

                categories[category].forEach(word => {
                    html += `
                        <div class="sensitive-word-item">
                            <span class="word-text">${escapeHtml(word.word)}</span>
                            <button class="delete-btn" onclick="deleteSensitiveWord(${word.id})" title="删除">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    `;
                });

                html += `
                        </div>
                    </div>
                `;
            });

            $('#sensitiveWordsList').html(html);
        }

        // 添加敏感词
        function addSensitiveWord() {
            const word = $('#newSensitiveWord').val().trim();
            const category = $('#wordCategory').val();

            if (!word) {
                showError('请输入敏感词');
                return;
            }

            $.ajax({
                url: '../api/feedback_api.php',
                method: 'POST',
                dataType: 'json',
                contentType: 'application/json',
                data: JSON.stringify({
                    action: 'add_sensitive_word',
                    word: word,
                    category: category
                }),
                success: function(response) {
                    if (response.success) {
                        showSuccess('敏感词添加成功');
                        $('#newSensitiveWord').val('');
                        loadSensitiveWords();
                    } else {
                        showError('添加失败：' + response.message);
                    }
                },
                error: function() {
                    showError('添加失败，请检查网络连接');
                }
            });
        }

        // 删除敏感词
        function deleteSensitiveWord(wordId) {
            if (!confirm('确认删除此敏感词吗？')) {
                return;
            }

            $.ajax({
                url: '../api/feedback_api.php',
                method: 'POST',
                dataType: 'json',
                contentType: 'application/json',
                data: JSON.stringify({
                    action: 'delete_sensitive_word',
                    word_id: wordId
                }),
                success: function(response) {
                    if (response.success) {
                        showSuccess('敏感词已删除');
                        loadSensitiveWords();
                    } else {
                        showError('删除失败：' + response.message);
                    }
                },
                error: function() {
                    showError('删除失败，请检查网络连接');
                }
            });
        }
    </script>
</body>
</html>
