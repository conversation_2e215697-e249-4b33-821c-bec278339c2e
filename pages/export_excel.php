<?php
session_start();

// 检查登录状态
if (!isset($_SESSION['username'])) {
    header('Location: login.php');
    exit();
}

// 调试信息
error_log("导出请求 - 方法: " . $_SERVER['REQUEST_METHOD']);
error_log("POST数据: " . print_r($_POST, true));

// 检查是否有POST数据
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    die('错误：请求方法不是POST，当前方法：' . $_SERVER['REQUEST_METHOD']);
}

if (!isset($_POST['employee_data_json'])) {
    die('错误：没有接收到employee_data_json数据。接收到的POST字段：' . implode(', ', array_keys($_POST)));
}

// 解析员工数据
$employeeData = json_decode($_POST['employee_data_json'], true);
if (!$employeeData) {
    die('错误：无法解析员工数据。JSON错误：' . json_last_error_msg() . '。原始数据：' . substr($_POST['employee_data_json'], 0, 200));
}

// 获取其他参数
$workersCount = $_POST['workers_count'] ?? 15;
$jintieTotal = $_POST['jintie_total'] ?? 10000;
$distribution = $_POST['distribution'] ?? 0;
$recordMonth = $_POST['record_month'] ?? date('Y-m');
$recordDate = $_POST['record_date'] ?? date('Y-m-d');

// 设置文件名
$filename = "津贴数据_{$recordMonth}_{$recordDate}.csv";

// 设置HTTP头，强制下载
header('Content-Type: text/csv; charset=UTF-8');
header('Content-Disposition: attachment; filename="' . $filename . '"');
header('Cache-Control: no-cache, must-revalidate');
header('Expires: Mon, 26 Jul 1997 05:00:00 GMT');

// 输出UTF-8 BOM，确保Excel正确显示中文
echo "\xEF\xBB\xBF";

// 创建CSV内容
$output = fopen('php://output', 'w');

// 写入标题行
$headers = [
    '序号',
    '岗位', 
    '姓名',
    '固定部分',
    '浮动部分',
    '请假天数',
    '应得固定',
    '浮动比例',
    '扣罚',
    '个人奖励',
    '总金额奖励',
    '应得浮动',
    '总金额',
    '备注'
];
fputcsv($output, $headers);

// 写入数据行
foreach ($employeeData as $index => $employee) {
    $row = [
        $index + 1,
        $employee['dept'] ?? '生产',
        $employee['name'] ?? '',
        $employee['fixed'] ?? 0,
        $employee['floating'] ?? 0,
        $employee['days'] ?? 0,
        $employee['fixed_actual'] ?? 0,
        $employee['floating_ratio'] ?? 1,
        $employee['deduction'] ?? 0,
        $employee['personal_bonus'] ?? 0,
        $employee['general_bonus'] ?? 0,
        $employee['floating_actual'] ?? 0,
        $employee['total'] ?? 0,
        $employee['remark'] ?? ''
    ];
    fputcsv($output, $row);
}

// 添加统计信息
fputcsv($output, []); // 空行
fputcsv($output, ['统计信息']);
fputcsv($output, ['应出勤天数', $workersCount]);
fputcsv($output, ['津贴总额', $jintieTotal]);
fputcsv($output, ['实际分配', $distribution]);
fputcsv($output, ['记录月份', $recordMonth]);
fputcsv($output, ['记录日期', $recordDate]);

// 计算各部门统计
$productionTotal = 0;
$monitoringTotal = 0;
$cb26Total = 0;
$totalAmount = 0;

foreach ($employeeData as $employee) {
    $total = $employee['total'] ?? 0;
    $dept = $employee['dept'] ?? '生产';
    
    $totalAmount += $total;
    
    if ($dept === '生产') {
        $productionTotal += $total;
    } elseif ($dept === '监控') {
        $monitoringTotal += $total;
    } elseif ($dept === 'CB26') {
        $cb26Total += $total;
    }
}

fputcsv($output, []); // 空行
fputcsv($output, ['部门统计']);
fputcsv($output, ['生产部门总计', $productionTotal]);
fputcsv($output, ['监控部门总计', $monitoringTotal]);
fputcsv($output, ['CB26部门总计', $cb26Total]);
fputcsv($output, ['总计', $totalAmount]);

fclose($output);
exit();
?>
