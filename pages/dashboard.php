<?php require '../includes/config.php';

// 获取统计数据
$powerCabinetCount = $pdo->query("SELECT COUNT(*) FROM power_cabinet")->fetchColumn();
$rcpCabinetCount = $pdo->query("SELECT COUNT(*) FROM rcp_cabinet")->fetchColumn();
$junctionBoxCount = $pdo->query("SELECT COUNT(*) FROM junction_box")->fetchColumn();
$terminalCount = $pdo->query("SELECT COUNT(*) FROM terminal")->fetchColumn();

// 获取最近添加的位号
$recentTerminals = $pdo->query("
    SELECT t.*, r.name as rcp_name, j.name as junction_name 
    FROM terminal t
    LEFT JOIN rcp_cabinet r ON t.rcp_id = r.id
    LEFT JOIN junction_box j ON t.junction_id = j.id
    ORDER BY t.id DESC LIMIT 5
")->fetchAll();

// 获取最近的操作日志
$recentLogs = $pdo->query("
    SELECT * FROM operation_logs 
    ORDER BY created_at DESC LIMIT 5
")->fetchAll();
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>控制面板 - 平台常用工具微信小程序后台管理系统</title>

    <!-- 预加载字体文件 -->
    <link rel="preload" href="../assets/webfonts/fa-solid-900.woff2" as="font" type="font/woff2" crossorigin="anonymous">

    <link rel="stylesheet" href="../assets/css/bootstrap.min.css">
    <link href="../assets/css/all.min.css" rel="stylesheet">
    <script src="../assets/js/chart.min.js"></script>
    <style>
        body {
            background-color: #f2f2f7;
            min-height: 100vh;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            color: #1d1d1f;
            line-height: 1.47059;
            font-weight: 400;
        }

        .container-fluid {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .dashboard-header {
            background: #ffffff;
            padding: 24px 32px;
            border-radius: 12px;
            margin-bottom: 24px;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.04);
            border: 1px solid rgba(0, 0, 0, 0.04);
        }

        .dashboard-header h1 {
            font-size: 28px;
            font-weight: 600;
            color: #1d1d1f;
            margin-bottom: 4px;
            letter-spacing: -0.003em;
        }

        .dashboard-header p {
            font-size: 17px;
            color: #86868b;
            margin: 0;
            font-weight: 400;
        }

        .stat-card {
            background: #ffffff;
            border-radius: 12px;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.04);
            border: 1px solid rgba(0, 0, 0, 0.04);
            transition: all 0.2s ease-in-out;
            margin-bottom: 16px;
            overflow: hidden;
            position: relative;
        }

        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.08);
        }

        .stat-card .card-body {
            padding: 20px;
            position: relative;
        }

        .stat-card .card-title {
            font-size: 13px;
            font-weight: 500;
            color: #86868b;
            margin-bottom: 8px;
            text-transform: none;
            letter-spacing: -0.08px;
        }

        .stat-card .display-4 {
            font-size: 32px;
            font-weight: 600;
            margin-bottom: 4px;
            color: #1d1d1f;
            letter-spacing: -0.003em;
        }

        .stat-card .icon {
            font-size: 24px;
            position: absolute;
            right: 20px;
            top: 20px;
            opacity: 0.6;
            transition: all 0.2s ease;
        }

        .stat-card:hover .icon {
            opacity: 0.8;
            transform: scale(1.05);
        }

        .stat-card .trend {
            font-size: 13px;
            font-weight: 400;
            color: #86868b;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .stat-card .trend i {
            font-size: 11px;
        }
        
        .power-cabinet-card .icon {
            color: #34c759;
        }

        .rcp-cabinet-card .icon {
            color: #007aff;
        }

        .junction-box-card .icon {
            color: #5856d6;
        }

        .terminal-card .icon {
            color: #ff3b30;
        }
        
        .recent-card {
            background: #ffffff;
            border-radius: 12px;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.04);
            border: 1px solid rgba(0, 0, 0, 0.04);
            margin-bottom: 24px;
            overflow: hidden;
            transition: all 0.2s ease-in-out;
            height: 100%;
            display: flex;
            flex-direction: column;
        }

        .recent-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.08);
        }

        .recent-card .card-header {
            background: #ffffff;
            border-bottom: 1px solid rgba(0, 0, 0, 0.04);
            padding: 20px 24px;
            font-size: 17px;
            font-weight: 600;
            color: #1d1d1f;
            letter-spacing: -0.022em;
        }

        .recent-card .card-header .btn {
            background: #f2f2f7;
            border: none;
            color: #007aff;
            font-size: 14px;
            font-weight: 500;
            padding: 6px 12px;
            border-radius: 8px;
            transition: all 0.2s ease;
        }

        .recent-card .card-header .btn:hover {
            background: #e5e5ea;
            color: #0051d5;
        }

        .recent-card .list-group-item {
            border: none;
            border-bottom: 1px solid rgba(0, 0, 0, 0.04);
            padding: 16px 24px;
            transition: background-color 0.2s ease;
        }

        .recent-card .list-group-item:hover {
            background-color: #f9f9f9;
        }

        .recent-card .list-group-item:last-child {
            border-bottom: none;
        }

        .chart-container {
            position: relative;
            height: 300px;
            padding: 20px;
        }

        /* 确保卡片内容区域填充剩余空间 */
        .recent-card .card-body {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .recent-card .list-group {
            flex: 1;
        }

        /* 统一设置侧边栏卡片的最小高度 */
        .sidebar-card {
            min-height: 400px;
        }

        /* 图表卡片的最小高度 */
        .chart-card {
            min-height: 400px;
        }

        /* 最近位号卡片的最小高度 */
        .recent-terminals-card {
            min-height: 400px;
        }

        /* 确保行内所有列高度一致 */
        .row.equal-height {
            display: flex;
            flex-wrap: wrap;
        }

        .row.equal-height > [class*='col-'] {
            display: flex;
            flex-direction: column;
        }

        /* 空状态时的最小高度 */
        .list-group:empty::after {
            content: "暂无数据";
            display: block;
            text-align: center;
            color: #86868b;
            padding: 40px 20px;
            font-size: 15px;
        }
        
        .badge-custom {
            padding: 4px 8px;
            font-size: 11px;
            font-weight: 500;
            border-radius: 6px;
            letter-spacing: 0.06em;
        }

        .badge-power {
            background-color: rgba(52, 199, 89, 0.1);
            color: #34c759;
        }

        .badge-rcp {
            background-color: rgba(0, 122, 255, 0.1);
            color: #007aff;
        }

        .badge-junction {
            background-color: rgba(88, 86, 214, 0.1);
            color: #5856d6;
        }

        .badge-terminal {
            background-color: rgba(255, 59, 48, 0.1);
            color: #ff3b30;
        }

        .quick-link {
            display: block;
            padding: 16px 20px;
            border-radius: 12px;
            background: #ffffff;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.04);
            border: 1px solid rgba(0, 0, 0, 0.04);
            margin-bottom: 12px;
            text-decoration: none;
            color: #1d1d1f;
            transition: all 0.2s ease-in-out;
            font-size: 15px;
            font-weight: 500;
        }

        .quick-link:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.08);
            text-decoration: none;
            color: #1d1d1f;
        }

        .quick-link i {
            font-size: 18px;
            margin-right: 12px;
            vertical-align: middle;
            width: 20px;
            text-align: center;
        }

        .quick-link .fas.fa-battery-full {
            color: #34c759;
        }

        .quick-link .fas.fa-cube {
            color: #007aff;
        }

        .quick-link .fas.fa-box-open {
            color: #5856d6;
        }

        .quick-link .fas.fa-tag {
            color: #ff3b30;
        }

        .quick-link .fas.fa-calculator {
            color: #ff9500;
        }
        
        .log-item {
            display: flex;
            flex-direction: column;
        }

        .log-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 4px;
        }

        .log-user {
            font-weight: 500;
            color: #1d1d1f;
            font-size: 15px;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .log-user i {
            color: #007aff;
            font-size: 14px;
        }

        .log-time {
            color: #86868b;
            font-size: 13px;
            font-weight: 400;
        }

        .log-action {
            color: #1d1d1f;
            font-size: 14px;
            line-height: 1.4;
            word-break: break-word;
        }

        .log-badge {
            display: inline-block;
            padding: 2px 6px;
            font-size: 11px;
            font-weight: 500;
            border-radius: 4px;
            margin-right: 6px;
            letter-spacing: 0.06em;
        }

        .log-badge-create {
            background-color: rgba(52, 199, 89, 0.1);
            color: #34c759;
        }

        .log-badge-update {
            background-color: rgba(255, 149, 0, 0.1);
            color: #ff9500;
        }

        .log-badge-delete {
            background-color: rgba(255, 59, 48, 0.1);
            color: #ff3b30;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .container-fluid {
                padding: 16px;
            }

            .dashboard-header {
                padding: 20px 24px;
            }

            .dashboard-header h1 {
                font-size: 24px;
            }

            .stat-card .display-4 {
                font-size: 28px;
            }

            .stat-card .icon {
                font-size: 20px;
            }

            .recent-card .card-header,
            .recent-card .list-group-item {
                padding: 16px 20px;
            }

            .quick-link {
                padding: 14px 16px;
            }
        }

        /* 加载动画 */
        .loading-animation {
            display: inline-block;
            width: 16px;
            height: 16px;
            border: 2px solid rgba(0, 122, 255, 0.2);
            border-radius: 50%;
            border-top-color: #007aff;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* 空状态样式 */
        .empty-state {
            text-align: center;
            padding: 40px 20px;
            color: #86868b;
        }

        .empty-state i {
            font-size: 48px;
            margin-bottom: 16px;
            opacity: 0.5;
        }

        .empty-state p {
            font-size: 15px;
            margin: 0;
        }
    </style>
</head>
<body>
<div class="container-fluid py-4">
    <!-- 头部欢迎区域 -->
    <div class="dashboard-header mb-4">
    <h1 class="display-4">欢迎使用平台常用工具微信小程序后台管理系统</h1>
        <p class="lead mb-0">今天是 <?= date('Y年m月d日') ?>，祝您工作愉快！</p>
    </div>
    
    <!-- 统计卡片区域 -->
    <div class="row">
        <div class="col-lg-3 col-md-6">
            <div class="card stat-card power-cabinet-card">
                <div class="card-body">
                    <h5 class="card-title">电源柜总数</h5>
                    <p class="display-4"><?= $powerCabinetCount ?></p>
                    <div class="trend">
                        <i class="fas fa-arrow-up"></i> 运行正常
                    </div>
                    <i class="fas fa-battery-full icon"></i>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="card stat-card rcp-cabinet-card">
                <div class="card-body">
                    <h5 class="card-title">RCP柜总数</h5>
                    <p class="display-4"><?= $rcpCabinetCount ?></p>
                    <div class="trend">
                        <i class="fas fa-chart-line"></i> 管理中
                    </div>
                    <i class="fas fa-cube icon"></i>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="card stat-card junction-box-card">
                <div class="card-body">
                    <h5 class="card-title">分线箱总数</h5>
                    <p class="display-4"><?= $junctionBoxCount ?></p>
                    <div class="trend">
                        <i class="fas fa-cogs"></i> 配置完善
                    </div>
                    <i class="fas fa-box-open icon"></i>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="card stat-card terminal-card">
                <div class="card-body">
                    <h5 class="card-title">位号总数</h5>
                    <p class="display-4"><?= $terminalCount ?></p>
                    <div class="trend">
                        <i class="fas fa-database"></i> 数据完整
                    </div>
                    <i class="fas fa-tag icon"></i>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 图表和最近数据区域 -->
    <div class="row equal-height">
        <!-- 图表区域 -->
        <div class="col-lg-8">
            <div class="card recent-card chart-card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <span><i class="fas fa-chart-bar mr-2"></i>系统概览</span>
                    <div>
                        <button class="btn btn-sm" id="refreshChart">
                            <i class="fas fa-sync-alt mr-1"></i> 刷新数据
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="overviewChart"></canvas>
                    </div>
                </div>
            </div>
            
            <!-- 最近添加的位号 -->
            <div class="card recent-card recent-terminals-card">
                <div class="card-header">
                    <i class="fas fa-clock mr-2"></i> 最近添加的位号
                </div>
                <ul class="list-group list-group-flush">
                    <?php if(count($recentTerminals) > 0): ?>
                        <?php foreach($recentTerminals as $terminal): ?>
                            <li class="list-group-item">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <strong><?= htmlspecialchars($terminal['code'] ?? '') ?></strong>
                                        <?php if(!empty($terminal['cable_name'])): ?>
                                            - <?= htmlspecialchars($terminal['cable_name'] ?? '') ?>
                                        <?php endif; ?>
                                    </div>
                                    <div>
                                        <?php if(!empty($terminal['rcp_name'])): ?>
                                            <span class="badge badge-custom badge-rcp"><?= htmlspecialchars($terminal['rcp_name'] ?? '') ?></span>
                                        <?php endif; ?>
                                        <?php if(!empty($terminal['junction_name'])): ?>
                                            <span class="badge badge-custom badge-junction"><?= htmlspecialchars($terminal['junction_name'] ?? '') ?></span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </li>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <li class="list-group-item text-center text-muted">暂无数据</li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
        
        <!-- 右侧区域 -->
        <div class="col-lg-4">
            <!-- 快速链接 -->
            <div class="card recent-card sidebar-card mb-4">
                <div class="card-header">
                    <i class="fas fa-rocket mr-2"></i> 快速访问
                </div>
                <div class="card-body">
                    <a href="power_cabinet.php" class="quick-link">
                        <i class="fas fa-battery-full text-success"></i> 电源柜管理
                    </a>
                    <a href="rcp_cabinet.php" class="quick-link">
                        <i class="fas fa-cube text-warning"></i> RCP柜管理
                    </a>
                    <a href="junction_box.php" class="quick-link">
                        <i class="fas fa-box-open text-info"></i> 分线箱管理
                    </a>
                    <a href="terminal.php" class="quick-link">
                        <i class="fas fa-tag text-primary"></i> 位号管理
                    </a>
                    <a href="jintie.php" class="quick-link">
                        <i class="fas fa-calculator"></i> 津贴计算
                    </a>
                    <a href="api_stats.php" class="quick-link">
                        <i class="fas fa-chart-bar text-info"></i> API统计
                    </a>
                    <a href="operation_logs.php" class="quick-link">
                        <i class="fas fa-clipboard-list text-secondary"></i> 操作日志
                    </a>
                </div>
            </div>
            
            <!-- 最近操作日志 -->
            <div class="card recent-card sidebar-card">
                <div class="card-header">
                    <i class="fas fa-history mr-2"></i> 最近操作日志
                </div>
                <ul class="list-group list-group-flush">
                    <?php if(count($recentLogs) > 0): ?>
                        <?php foreach($recentLogs as $log): ?>
                            <li class="list-group-item">
                                <div class="log-item">
                                    <div class="log-header">
                                        <span class="log-user">
                                            <i class="fas fa-user-circle mr-1"></i>
                                            <?= !empty($log['user']) ? htmlspecialchars($log['user']) : '系统' ?>
                                        </span>
                                        <span class="log-time">
                                            <?= date('m-d H:i', strtotime($log['created_at'])) ?>
                                        </span>
                                    </div>
                                    <div class="log-action">
                                        <?php
                                        $actionLower = strtolower($log['action']);
                                        $badgeClass = '';
                                        $icon = '';
                                        
                                        if (strpos($actionLower, 'create') !== false || strpos($actionLower, '创建') !== false || strpos($actionLower, '添加') !== false) {
                                            $badgeClass = 'log-badge-create';
                                            $icon = 'fa-plus-circle';
                                        } elseif (strpos($actionLower, 'update') !== false || strpos($actionLower, '更新') !== false || strpos($actionLower, '修改') !== false) {
                                            $badgeClass = 'log-badge-update';
                                            $icon = 'fa-edit';
                                        } elseif (strpos($actionLower, 'delete') !== false || strpos($actionLower, '删除') !== false) {
                                            $badgeClass = 'log-badge-delete';
                                            $icon = 'fa-trash-alt';
                                        }
                                        
                                        if (!empty($badgeClass)) {
                                            echo '<span class="log-badge ' . $badgeClass . '"><i class="fas ' . $icon . ' mr-1"></i>' . 
                                                 (strpos($actionLower, 'create') !== false ? '创建' : 
                                                 (strpos($actionLower, 'update') !== false ? '更新' : 
                                                 (strpos($actionLower, 'delete') !== false ? '删除' : '操作'))) . 
                                                 '</span>';
                                        }
                                        
                                        // 移除路径信息，只显示操作内容
                                        $cleanAction = preg_replace('/in\s+\/home2\/sunxiyue\/public_html\/api\/zdh\/.*?\.php(\s+on\s+line\s+\d+)?/i', '', $log['action']);
                                        echo htmlspecialchars($cleanAction);
                                        ?>
                                    </div>
                                </div>
                            </li>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <li class="list-group-item text-center text-muted">暂无操作日志</li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </div>
</div>

<script src="../assets/js/jquery.min.js"></script>
<script src="../assets/js/bootstrap.bundle.min.js"></script>
<script src="../assets/js/fix_frontend_errors.js"></script>
<script>
// 初始化图表和动画效果
document.addEventListener('DOMContentLoaded', function() {
    // 统计卡片入场动画
    const statCards = document.querySelectorAll('.stat-card');
    statCards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';

        setTimeout(() => {
            card.style.transition = 'all 0.4s ease-out';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100 + 200);
    });

    // 数字计数动画（修复NaN问题）
    setTimeout(() => {
        const countElements = document.querySelectorAll('.display-4');
        countElements.forEach(element => {
            const finalValue = parseInt(element.textContent) || 0;
            if (finalValue > 0) {
                let currentValue = 0;
                const increment = Math.max(1, Math.ceil(finalValue / 30));
                const timer = setInterval(() => {
                    currentValue += increment;
                    if (currentValue >= finalValue) {
                        currentValue = finalValue;
                        clearInterval(timer);
                    }
                    element.textContent = currentValue;
                }, 50);
            }
        });
    }, 800);

    const ctx = document.getElementById('overviewChart').getContext('2d');
    
    // 准备数据
    const data = {
        labels: ['电源柜', 'RCP柜', '分线箱', '位号'],
        datasets: [{
            label: '数量统计',
            data: [<?= $powerCabinetCount ?>, <?= $rcpCabinetCount ?>, <?= $junctionBoxCount ?>, <?= $terminalCount ?>],
            backgroundColor: [
                'rgba(17, 153, 142, 0.8)',
                'rgba(252, 70, 107, 0.8)',
                'rgba(102, 126, 234, 0.8)',
                'rgba(240, 147, 251, 0.8)'
            ],
            borderColor: [
                'rgba(17, 153, 142, 1)',
                'rgba(252, 70, 107, 1)',
                'rgba(102, 126, 234, 1)',
                'rgba(240, 147, 251, 1)'
            ],
            borderWidth: 1
        }]
    };
    
    // 图表配置
    const config = {
        type: 'bar',
        data: data,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    backgroundColor: 'rgba(0, 0, 0, 0.7)',
                    padding: 10,
                    titleFont: {
                        size: 14
                    },
                    bodyFont: {
                        size: 14
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(0, 0, 0, 0.05)'
                    }
                },
                x: {
                    grid: {
                        display: false
                    }
                }
            }
        }
    };
    
    // 创建图表
    const myChart = new Chart(ctx, config);
    
    // 刷新图表按钮
    document.getElementById('refreshChart').addEventListener('click', function() {
        const button = this;
        const originalText = button.innerHTML;

        // 显示加载状态
        button.innerHTML = '<span class="loading-animation"></span> 刷新中...';
        button.disabled = true;

        // 模拟刷新延迟
        setTimeout(() => {
            myChart.update('active');

            // 恢复按钮状态
            button.innerHTML = originalText;
            button.disabled = false;

            // 显示成功提示
            showNotification('数据已刷新', 'success');
        }, 1000);
    });

    // 通知函数
    function showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `alert alert-${type} position-fixed`;
        notification.style.cssText = `
            top: 20px;
            right: 20px;
            z-index: 9999;
            min-width: 250px;
            opacity: 0;
            transform: translateX(100%);
            transition: all 0.3s ease;
        `;
        notification.innerHTML = `<i class="fas fa-check-circle mr-2"></i>${message}`;

        document.body.appendChild(notification);

        // 显示动画
        setTimeout(() => {
            notification.style.opacity = '1';
            notification.style.transform = 'translateX(0)';
        }, 100);

        // 自动隐藏
        setTimeout(() => {
            notification.style.opacity = '0';
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 3000);
    }
    
    // 更新父窗口中的菜单高亮状态和localStorage
    if (window.parent && window.parent.document) {
        // 更新localStorage中保存的当前页面
        window.parent.localStorage.setItem('currentPage', 'pages/dashboard.php');
        
        // 更新父窗口中的菜单高亮状态
        const menuItems = window.parent.document.querySelectorAll('a[target="contentFrame"]');
        menuItems.forEach(item => {
            item.classList.remove('active-menu-item');
        });
        const dashboardLink = window.parent.document.querySelector('a[data-page="pages/dashboard.php"]');
        if (dashboardLink) {
            dashboardLink.classList.add('active-menu-item');
        }
    }
});
</script>
</body>
</html>