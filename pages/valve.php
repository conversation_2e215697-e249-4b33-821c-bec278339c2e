<?php
require '../includes/config.php';
requireRole(['admin','manager']);

// 设置较大的内存限制和执行时间
ini_set('memory_limit', '256M');
ini_set('max_execution_time', 300); // 5分钟
error_reporting(E_ALL & ~E_NOTICE & ~E_WARNING);

// 检查必要的PHP扩展
$requiredExtensions = ['zip', 'xml', 'gd', 'mbstring'];
$missingExtensions = [];
foreach ($requiredExtensions as $ext) {
    if (!extension_loaded($ext)) {
        $missingExtensions[] = $ext;
    }
}

if (!empty($missingExtensions)) {
    error_log('缺少必要的PHP扩展: ' . implode(', ', $missingExtensions));
}

// 正确引入 PhpSpreadsheet - 检查文件是否存在
$vendorAutoloadPath = __DIR__.'/../../vendor/autoload.php';
if (file_exists($vendorAutoloadPath)) {
    require_once $vendorAutoloadPath;
    error_log('PhpSpreadsheet loaded from: ' . $vendorAutoloadPath);
} else {
    // 如果找不到vendor目录，尝试其他可能的路径
    $altPaths = [
        __DIR__.'/vendor/autoload.php',
        dirname(__DIR__).'/vendor/autoload.php',
        'D:/WZ/Apache/Apache24/htdocs/vendor/autoload.php'
    ];
    
    $loaded = false;
    foreach ($altPaths as $path) {
        if (file_exists($path)) {
            require_once $path;
            $loaded = true;
            error_log('PhpSpreadsheet loaded from: ' . $path);
            break;
        }
    }
    
    if (!$loaded) {
        error_log('PhpSpreadsheet autoload.php not found, paths checked: ' . implode(', ', $altPaths));
        throw new Exception('无法加载Excel处理库(PhpSpreadsheet)，请确保正确安装了vendor目录。错误详情请查看服务器日志。');
    }
}

use PhpOffice\PhpSpreadsheet\IOFactory;

// 检查阀门表是否存在，如果不存在则创建
try {
    $tableCheck = $pdo->query("SHOW TABLES LIKE 'valve'");
    if ($tableCheck->rowCount() == 0) {
        // 创建阀门表
        $pdo->exec("CREATE TABLE IF NOT EXISTS valve (
            id INT(11) NOT NULL AUTO_INCREMENT,
            serial_number VARCHAR(50) NOT NULL COMMENT '阀门序号',
            platform_name VARCHAR(255) NOT NULL COMMENT '平台名称',
            valve_name VARCHAR(255) NOT NULL COMMENT '阀门名称',
            valve_code VARCHAR(255) NOT NULL COMMENT '阀门编号', 
            location VARCHAR(255) DEFAULT NULL COMMENT '所在位置',
            function_desc TEXT DEFAULT NULL COMMENT '阀门功能',
            valve_type VARCHAR(100) DEFAULT NULL COMMENT '阀门类型',
            flow_type VARCHAR(100) DEFAULT NULL COMMENT '流程类型',
            valve_size VARCHAR(50) DEFAULT NULL COMMENT '阀门尺寸',
            flange_type VARCHAR(100) DEFAULT NULL COMMENT '法兰形式',
            flange_holes VARCHAR(50) DEFAULT NULL COMMENT '法兰孔数',
            seal_type VARCHAR(100) DEFAULT NULL COMMENT '密封面形式',
            gasket_type VARCHAR(100) DEFAULT NULL COMMENT '垫子类型',
            operation_date DATE DEFAULT NULL COMMENT '投产时间',
            remark TEXT DEFAULT NULL COMMENT '备注',
            create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
            PRIMARY KEY (id),
            UNIQUE KEY (serial_number)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='阀门管理表'");
    } else {
        // 检查表结构，如果需要添加valve_code字段
        $columnCheck = $pdo->query("SHOW COLUMNS FROM valve LIKE 'valve_code'");
        if ($columnCheck->rowCount() == 0) {
            // valve_code字段不存在，添加它
            $pdo->exec("ALTER TABLE valve ADD COLUMN valve_code VARCHAR(255) NOT NULL COMMENT '阀门编号' AFTER serial_number");
            error_log("已添加valve_code字段到valve表");
        }
    }
} catch (PDOException $e) {
    // 记录错误但不中断执行
    error_log("创建或修改阀门表失败: " . $e->getMessage());
}

// 设置异常处理以便记录详细错误
set_exception_handler(function($e) {
    error_log('未捕获的导入异常: ' . $e->getMessage() . "\n" . $e->getTraceAsString());
    http_response_code(500);
    echo json_encode(['status' => 'error', 'message' => '导入过程中出现错误: ' . $e->getMessage()]);
    exit;
});

// 处理Excel导入
if(isset($_FILES['excel'])){
    try {
        // 检查上传是否成功
        if ($_FILES['excel']['error'] !== UPLOAD_ERR_OK) {
            $errorMessages = [
                UPLOAD_ERR_INI_SIZE => '上传文件超过php.ini中允许的最大文件大小',
                UPLOAD_ERR_FORM_SIZE => '上传文件超过表单允许的最大文件大小',
                UPLOAD_ERR_PARTIAL => '文件只有部分被上传',
                UPLOAD_ERR_NO_FILE => '没有文件被上传',
                UPLOAD_ERR_NO_TMP_DIR => '找不到临时文件夹',
                UPLOAD_ERR_CANT_WRITE => '文件写入失败',
                UPLOAD_ERR_EXTENSION => '文件上传被PHP扩展停止'
            ];
            
            $errorMessage = isset($errorMessages[$_FILES['excel']['error']]) 
                ? $errorMessages[$_FILES['excel']['error']] 
                : '文件上传失败，错误码：' . $_FILES['excel']['error'];
                
            throw new Exception($errorMessage);
        }
        
        // 验证文件类型并输出文件信息到日志（调试用）
        $fileType = $_FILES['excel']['type'];
        $fileName = $_FILES['excel']['name'];
        $fileSize = $_FILES['excel']['size'];
        $fileTmp = $_FILES['excel']['tmp_name'];
        
        error_log("Excel文件信息: 名称={$fileName}, 类型={$fileType}, 大小={$fileSize}字节, 临时文件={$fileTmp}");
        
        $validTypes = [
            'application/vnd.ms-excel',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'application/octet-stream'
        ];
        
        // 检查文件扩展名
        $fileExt = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));
        
        if (!in_array($fileExt, ['xls', 'xlsx'])) {
            throw new Exception('请上传Excel文件(.xlsx或.xls)，当前文件扩展名为: ' . $fileExt);
        }
        
        // 检查临时文件是否存在且可读
        if (!file_exists($fileTmp) || !is_readable($fileTmp)) {
            throw new Exception('无法读取上传的临时文件: ' . $fileTmp . ' (存在: ' . (file_exists($fileTmp) ? '是' : '否') . ', 可读: ' . (is_readable($fileTmp) ? '是' : '否') . ')');
        }
        
        // 读取Excel文件
        $pdo->beginTransaction();
        
        try {
            // 尝试创建合适的读取器
            error_log("尝试为文件创建Excel读取器: " . $fileTmp);
            $reader = IOFactory::createReaderForFile($fileTmp);
            
            if (!$reader) {
                throw new Exception("无法为文件创建合适的Excel读取器");
            }
            
            error_log("Excel读取器创建成功: " . get_class($reader));
            $reader->setReadDataOnly(true);
            
            // 尝试加载Excel文件
            error_log("正在加载Excel文件...");
            $spreadsheet = $reader->load($fileTmp);
            
            if (!$spreadsheet) {
                throw new Exception("无法加载Excel文件内容");
            }
            
            error_log("Excel文件加载成功");
            $sheet = $spreadsheet->getActiveSheet();
            
            if (!$sheet) {
                throw new Exception("无法获取Excel的活动工作表");
            }
            
            // 获取工作表的基本信息
            $highestRow = $sheet->getHighestRow();
            $highestColumn = $sheet->getHighestColumn();
            error_log("Excel工作表信息: 最高行={$highestRow}, 最高列={$highestColumn}");
            
            if ($highestRow < 5) {
                throw new Exception("Excel文件数据不足，需要至少5行数据（前4行为表头，从第5行开始导入）");
            }
            
            // 检查PhpSpreadsheet版本以确定使用哪种API
            $useNewApi = method_exists($sheet, 'getCell');
            $useOldApi = method_exists($sheet, 'getCellByColumnAndRow');
            
            error_log("PhpSpreadsheet API检测：useNewApi=" . ($useNewApi ? "是" : "否") . ", useOldApi=" . ($useOldApi ? "是" : "否"));
            
        } catch (Exception $e) {
            throw new Exception('读取Excel文件失败: ' . $e->getMessage() . "\n详细错误: " . $e->getTraceAsString());
        }
        
        $importCount = 0;
        
        // 从第5行开始读取数据
        foreach ($sheet->getRowIterator(5) as $row) {
            $rowIndex = $row->getRowIndex();
            
            try {
                // 按照用户提供的实际Excel表结构调整列映射
                // 判断使用哪种API读取单元格
                $columnNames = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O'];
                $columnIndexes = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]; // 对应每列的索引
                
                // 定义一个获取单元格值的函数，根据API可用性选择
                $getCellValue = function($colIndex, $rowIndex) use ($sheet, $useNewApi, $useOldApi, $columnNames, $columnIndexes) {
                    try {
                        if ($useNewApi) {
                            // 使用新API
                            return $sheet->getCell($columnNames[$colIndex] . $rowIndex)->getValue();
                        } elseif ($useOldApi) {
                            // 使用旧API
                            return $sheet->getCellByColumnAndRow($columnIndexes[$colIndex], $rowIndex)->getValue();
                        } else {
                            // 尝试使用坐标方式（兼容不同版本）
                            $coordinate = $columnNames[$colIndex] . $rowIndex;
                            if (method_exists($sheet, 'getCell')) {
                                return $sheet->getCell($coordinate)->getValue();
                            } else {
                                // 最后尝试直接访问单元格集合
                                return $sheet->getCellCollection()->get($coordinate)->getValue();
                            }
                        }
                    } catch (Exception $e) {
                        error_log("获取单元格({$columnNames[$colIndex]}{$rowIndex})值失败: " . $e->getMessage());
                        return ''; // 出错时返回空字符串
                    }
                };
                
                // 读取A-D列的必填字段
                $valve_seq = trim((string)$getCellValue(0, $rowIndex));
                $platform_name = trim((string)$getCellValue(1, $rowIndex));
                $valve_name = trim((string)$getCellValue(2, $rowIndex));
                // 不使用Excel中的D列，系统将自动生成阀门编号
                //$valve_code = trim((string)$getCellValue(3, $rowIndex));
                
                // 记录每行读取的必填字段值用于调试
                if ($rowIndex <= 10) { // 只记录前10行，避免日志过大
                    error_log("第{$rowIndex}行数据：序号=[{$valve_seq}], 平台名称=[{$platform_name}], 阀门名称=[{$valve_name}]");
                }
                
                // 其他可选列，如果没有数据则为空字符串
                $location = trim((string)($getCellValue(4, $rowIndex) ?? ''));
                $function_desc = trim((string)($getCellValue(5, $rowIndex) ?? ''));
                $valve_type = trim((string)($getCellValue(6, $rowIndex) ?? ''));
                $flow_type = trim((string)($getCellValue(7, $rowIndex) ?? ''));
                $valve_size = trim((string)($getCellValue(8, $rowIndex) ?? ''));
                $flange_type = trim((string)($getCellValue(9, $rowIndex) ?? ''));
                $flange_holes = trim((string)($getCellValue(10, $rowIndex) ?? ''));
                $seal_type = trim((string)($getCellValue(11, $rowIndex) ?? ''));
                $gasket_type = trim((string)($getCellValue(12, $rowIndex) ?? ''));
                $operation_date = trim((string)($getCellValue(13, $rowIndex) ?? ''));
                $remark = trim((string)($getCellValue(14, $rowIndex) ?? ''));
                
                // 完全忽略Excel中的D列，重新生成阀门编号
                // 构建基础部分（E-I列）
                $baseCodeParts = [];
                if (!empty($valve_type)) $baseCodeParts[] = $valve_type;
                if (!empty($valve_name)) $baseCodeParts[] = $valve_name;
                
                // 构建位置相关部分
                if (!empty($location)) $baseCodeParts[] = $location;
                
                // 添加流程类型
                if (!empty($flow_type)) $baseCodeParts[] = $flow_type;
                
                // 添加尺寸信息
                if (!empty($valve_size)) $baseCodeParts[] = $valve_size;
                
                // 生成阀门基础编号
                $baseCode = implode('-', $baseCodeParts);
                
                // 构建括号内部分（J-M列）
                $bracketParts = [];
                if (!empty($flange_type)) $bracketParts[] = $flange_type;
                if (!empty($flange_holes)) $bracketParts[] = $flange_holes;
                if (!empty($seal_type)) $bracketParts[] = $seal_type;
                if (!empty($gasket_type)) $bracketParts[] = $gasket_type;
                
                // 组合括号部分
                $bracketCode = !empty($bracketParts) ? '(' . implode('-', $bracketParts) . ')' : '';
                
                // 完整的阀门编号
                $valve_code = $baseCode . $bracketCode;
                
                // 如果自动生成的编号为空，使用阀门名称作为编号
                if (empty($valve_code)) {
                    $valve_code = $valve_name;
                }
                
                error_log("自动生成阀门编号: {$valve_code}");

                // 替换为按照新公式生成阀门编号
                // 按照公式: =A5&"#-"&E5&"-"&F5&"-"&G5&"-"&H5&"-"&I5&"("&LEFT(J5&IF(J5="","","-")&K5&IF(K5="","","-")&L5&IF(L5="","","-")&M5&IF(M5="","","-"),LEN(J5&IF(J5="","","-")&K5&IF(K5="","","-")&L5&IF(L5="","","-")&M5&IF(M5="","","-"))-1)&")"

                // 解析公式: A5(序号)#-E5(位置)-F5(功能)-G5(阀门类型)-H5(流程类型)-I5(阀门尺寸)(J5-K5-L5-M5)

                // 构建前半部分：序号#-位置-功能-阀门类型-流程类型-阀门尺寸
                $firstPart = $valve_seq . "#-";
                if (!empty($location)) $firstPart .= $location . "-";
                if (!empty($function_desc)) $firstPart .= $function_desc . "-";
                if (!empty($valve_type)) $firstPart .= $valve_type . "-";
                if (!empty($flow_type)) $firstPart .= $flow_type . "-";
                if (!empty($valve_size)) $firstPart .= $valve_size;

                // 移除末尾多余的连字符
                $firstPart = rtrim($firstPart, "-");

                // 处理括号部分：生成J5-K5-L5-M5，但需要根据是否为空来决定是否添加连字符
                $bracketParts = [];
                if (!empty($flange_type)) $bracketParts[] = $flange_type;
                if (!empty($flange_holes)) $bracketParts[] = $flange_holes;
                if (!empty($seal_type)) $bracketParts[] = $seal_type;
                if (!empty($gasket_type)) $bracketParts[] = $gasket_type;

                // 只有在有括号内容时才添加括号部分
                $bracketPart = "";
                if (!empty($bracketParts)) {
                    $bracketPart = "(" . implode("-", $bracketParts) . ")";
                }

                // 组合成最终的阀门编号
                $valve_code = $firstPart . $bracketPart;

                // 如果所有部分都为空，则使用阀门名称作为编号
                if ($valve_code == "#-" || empty($valve_code)) {
                    $valve_code = $valve_name;
                }

                error_log("按新公式生成阀门编号: {$valve_code}");

                // 删除原有逻辑：不再处理公式内容
                /*
                // 处理阀门编号，如果是公式则根据E-M列的值构建
                if (strpos($valve_code, '=') === 0) {
                    // 是一个Excel公式，则根据E-M列构建阀门编号
                    error_log("发现公式，原始值：" . $valve_code);
                    
                    // ... (以下是原来的公式处理逻辑，全部删除)
                }
                */
            } catch (Exception $e) {
                // 记录读取单元格错误但继续处理
                error_log("读取第{$rowIndex}行数据失败: " . $e->getMessage());
                continue;
            }
            
            // 检查必填字段：序号、平台名称、阀门名称和阀门编号
            if (empty($valve_seq) || empty($platform_name) || empty($valve_name)) {
                error_log("第{$rowIndex}行数据缺少必填字段，跳过");
                continue;
            }
            
            // 确保序号为字符串格式
            $valve_seq = (string)$valve_seq;
            
            // 如果序号为空，生成一个时间戳作为序号
            if (empty($valve_seq)) {
                $valve_seq = time() . rand(100, 999);
                error_log("第{$rowIndex}行序号为空，自动生成序号: {$valve_seq}");
            }
            
            // 检查是否已存在相同序号的记录
            try {
                $checkStmt = $pdo->prepare("SELECT COUNT(*) FROM valve WHERE serial_number = ?");
                $checkStmt->execute([$valve_seq]);
                $exists = $checkStmt->fetchColumn();
                
                if ($exists > 0) {
                    // 如果有相同序号，生成新的唯一序号（时间戳+随机数）
                    $valve_seq = time() . rand(100, 999);
                    
                    // 记录修改的序号信息
                    error_log("序号已存在，生成新序号: {$valve_seq}，原序号在第{$rowIndex}行");
                }
            } catch (Exception $e) {
                // 其他数据库错误，记录但继续处理
                error_log("检查阀门序号时出错: " . $e->getMessage());
                // 生成一个唯一的序号以确保导入成功
                $valve_seq = time() . rand(100, 999);
            }
            
            // 处理日期格式
            if (!empty($operation_date)) {
                if ($operation_date instanceof \PhpOffice\PhpSpreadsheet\Shared\Date) {
                    $operation_date = \PhpOffice\PhpSpreadsheet\Shared\Date::excelToDateTimeObject($operation_date)->format('Y-m-d');
                } else {
                    // 尝试转换字符串日期为Y-m-d格式
                    try {
                        // 处理类似"2012.05"格式的日期
                        if (preg_match('/^(\d{4})\.(\d{2})$/', $operation_date, $matches)) {
                            $operation_date = $matches[1] . '-' . $matches[2] . '-01'; // 使用月份的第1天
                        } else {
                            $tempDate = new DateTime($operation_date);
                            $operation_date = $tempDate->format('Y-m-d');
                        }
                    } catch (Exception $e) {
                        error_log("日期格式转换失败: " . $e->getMessage() . ", 原始值: " . $operation_date);
                        $operation_date = null;
                    }
                }
            } else {
                $operation_date = null;
            }
            
            // 插入或更新数据
            try {
                // 使用A列的序号作为serial_number
                $stmt = $pdo->prepare("INSERT INTO valve 
                    (platform_name, valve_name, serial_number, valve_code, location, function_desc, valve_type, 
                    flow_type, valve_size, flange_type, flange_holes, seal_type, gasket_type, 
                    operation_date, remark)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
                    
                $stmt->execute([
                    $platform_name, $valve_name, $valve_seq, $valve_code, $location, $function_desc, $valve_type,
                    $flow_type, $valve_size, $flange_type, $flange_holes, $seal_type, $gasket_type,
                    $operation_date, $remark
                ]);
                
                $importCount++;
            } catch (Exception $e) {
                error_log("插入/更新第{$rowIndex}行数据失败: " . $e->getMessage());
                continue;
            }
        }

        $pdo->commit();
        
        // 释放内存
        $spreadsheet->disconnectWorksheets();
        unset($spreadsheet);
        
        logAction('导入阀门数据', 'valve', ['count' => $importCount]);
        echo json_encode(['status' => 'success', 'message' => "成功导入{$importCount}条数据"]);
    } catch (Exception $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        error_log("导入阀门数据失败: " . $e->getMessage());
        http_response_code(500);
        echo json_encode(['status' => 'error', 'message' => $e->getMessage()]);
    }
    exit();
}

// 处理添加/编辑请求
if ($_SERVER['REQUEST_METHOD'] == 'POST' && !isset($_FILES['excel'])) {
    $input = $_POST;

    try {
        $required = ['platform_name', 'valve_name', 'serial_number'];
        foreach ($required as $field) {
            if (empty($input[$field])) throw new Exception($field . " 字段不能为空");
        }

        // 检查阀门编号唯一性
        $checkSql = "SELECT id FROM valve WHERE serial_number = ?";
        $params = [$input['serial_number']];
        if (!empty($input['id'])) {
            $checkSql .= " AND id != ?";
            $params[] = $input['id'];
        }
        $stmt = $pdo->prepare($checkSql);
        $stmt->execute($params);
        if ($stmt->fetch()) throw new Exception("阀门序号已存在，请使用其他序号");

        // 处理日期字段
        $operation_date = !empty($input['operation_date']) ? $input['operation_date'] : null;
        
        // 自动生成阀门编号（如果未提供）
        if (empty($input['valve_code'])) {
            // 按照公式: =A5&"#-"&E5&"-"&F5&"-"&G5&"-"&H5&"-"&I5&"("&LEFT(...)&")"
            
            // 构建前半部分：序号#-位置-功能-阀门类型-流程类型-阀门尺寸
            $firstPart = $input['serial_number'] . "#-";
            if (!empty($input['location'])) $firstPart .= $input['location'] . "-";
            if (!empty($input['function_desc'])) $firstPart .= $input['function_desc'] . "-";
            if (!empty($input['valve_type'])) $firstPart .= $input['valve_type'] . "-";
            if (!empty($input['flow_type'])) $firstPart .= $input['flow_type'] . "-";
            if (!empty($input['valve_size'])) $firstPart .= $input['valve_size'];
            
            // 移除末尾多余的连字符
            $firstPart = rtrim($firstPart, "-");
            
            // 处理括号部分
            $bracketParts = [];
            if (!empty($input['flange_type'])) $bracketParts[] = $input['flange_type'];
            if (!empty($input['flange_holes'])) $bracketParts[] = $input['flange_holes'];
            if (!empty($input['seal_type'])) $bracketParts[] = $input['seal_type'];
            if (!empty($input['gasket_type'])) $bracketParts[] = $input['gasket_type'];
            
            // 只有在有括号内容时才添加括号部分
            $bracketPart = "";
            if (!empty($bracketParts)) {
                $bracketPart = "(" . implode("-", $bracketParts) . ")";
            }
            
            // 组合成最终的阀门编号
            $input['valve_code'] = $firstPart . $bracketPart;
            
            // 如果编号只有序号加#-，或者为空，则使用阀门名称
            if ($input['valve_code'] == $input['serial_number'] . "#-" || empty($input['valve_code'])) {
                $input['valve_code'] = $input['valve_name'];
            }
            
            error_log("手动添加/编辑时生成阀门编号: " . $input['valve_code']);
        }

        // 构建数据数组
        $data = [
            'platform_name' => $input['platform_name'],
            'valve_name' => $input['valve_name'],
            'serial_number' => $input['serial_number'],
            'valve_code' => $input['valve_code'],
            'location' => $input['location'] ?? '',
            'function_desc' => $input['function_desc'] ?? '',
            'valve_type' => $input['valve_type'] ?? '',
            'flow_type' => $input['flow_type'] ?? '',
            'valve_size' => $input['valve_size'] ?? '',
            'flange_type' => $input['flange_type'] ?? '',
            'flange_holes' => $input['flange_holes'] ?? '',
            'seal_type' => $input['seal_type'] ?? '',
            'gasket_type' => $input['gasket_type'] ?? '',
            'operation_date' => $operation_date,
            'remark' => $input['remark'] ?? ''
        ];
        
        if (!empty($input['id'])) {
            // 更新阀门
            $sql = "UPDATE valve SET 
                platform_name=:platform_name, 
                valve_name=:valve_name, 
                serial_number=:serial_number, 
                valve_code=:valve_code,
                location=:location, 
                function_desc=:function_desc, 
                valve_type=:valve_type, 
                flow_type=:flow_type, 
                valve_size=:valve_size, 
                flange_type=:flange_type, 
                flange_holes=:flange_holes, 
                seal_type=:seal_type, 
                gasket_type=:gasket_type, 
                operation_date=:operation_date, 
                remark=:remark 
                WHERE id=:id";
            $data['id'] = $input['id'];
            $stmt = $pdo->prepare($sql);
            $stmt->execute($data);
            logAction('更新阀门', 'valve', ['id' => $input['id']]);
        } else {
            // 新增阀门
            $columns = implode(', ', array_keys($data));
            $placeholders = ':' . implode(', :', array_keys($data));
            $sql = "INSERT INTO valve ($columns) VALUES ($placeholders)";
            $stmt = $pdo->prepare($sql);
            $stmt->execute($data);
            logAction('添加阀门', 'valve', ['valve_code' => $input['serial_number']]);
        }
        
        echo json_encode(['status' => 'success']);
    } catch (Exception $e) {
        http_response_code(400);
        echo json_encode(['status' => 'error', 'message' => $e->getMessage()]);
    }
    exit();
}

// 处理删除请求
if(isset($_GET['delete'])) {
    try {
        $stmt = $pdo->prepare("DELETE FROM valve WHERE id = ?");
        $stmt->execute([$_GET['delete']]);
        logAction('删除阀门', 'valve', ['id' => $_GET['delete']]);
        echo json_encode(['status' => 'success']);
    } catch (Exception $e) {
        echo json_encode(['status' => 'error', 'message' => $e->getMessage()]);
    }
    exit();
}

// 处理全部删除请求
if(isset($_GET['deleteAll'])) {
    try {
        // 获取当前记录数量用于日志
        $countStmt = $pdo->query("SELECT COUNT(*) FROM valve");
        $deletedCount = $countStmt->fetchColumn();
        
        // 执行删除操作
        $stmt = $pdo->exec("TRUNCATE TABLE valve");
        
        // 记录操作日志
        logAction('删除所有阀门', 'valve', ['count' => $deletedCount]);
        
        echo json_encode(['status' => 'success', 'message' => "成功删除所有记录（共{$deletedCount}条）"]);
    } catch (Exception $e) {
        echo json_encode(['status' => 'error', 'message' => $e->getMessage()]);
    }
    exit();
}

// 分页处理
$page = isset($_GET['page']) ? intval($_GET['page']) : 1;
$limit = isset($_GET['limit']) ? intval($_GET['limit']) : 20; // 默认每页20条
$offset = ($page - 1) * $limit;

// 筛选参数
$filter_platform_name = isset($_GET['filter_platform_name']) ? $_GET['filter_platform_name'] : '';
$filter_location = isset($_GET['filter_location']) ? $_GET['filter_location'] : '';
$filter_valve_type = isset($_GET['filter_valve_type']) ? $_GET['filter_valve_type'] : '';
$filter_flow_type = isset($_GET['filter_flow_type']) ? $_GET['filter_flow_type'] : '';
$filter_valve_size = isset($_GET['filter_valve_size']) ? $_GET['filter_valve_size'] : '';
$filter_flange_type = isset($_GET['filter_flange_type']) ? $_GET['filter_flange_type'] : '';

// 排序处理
$sort = isset($_GET['sort']) ? $_GET['sort'] : 'serial_number';
$order = isset($_GET['order']) ? $_GET['order'] : 'asc';

// 保证排序字段安全
$allowedSortFields = ['id', 'serial_number', 'platform_name', 'location', 'valve_type', 'flow_type', 'valve_size', 'flange_type'];
if (!in_array($sort, $allowedSortFields)) {
    $sort = 'serial_number';
}

// 保证排序方向安全
$order = strtolower($order) === 'asc' ? 'ASC' : 'DESC';

// 搜索处理
$search = isset($_GET['search']) ? $_GET['search'] : '';
$conditions = [];
$params = [];

// 添加搜索条件
if ($search) {
    // 分词处理，按空格分割搜索词
    $keywords = preg_split('/\s+/', trim($search));
    
    foreach ($keywords as $keyword) {
        if (empty($keyword)) continue;
        
        // 只对阀门名称进行搜索
        $keywordCondition = "valve_name LIKE ?";
        $conditions[] = $keywordCondition;
        
        // 为阀门名称字段添加该关键词的参数
        $keywordParam = "%{$keyword}%";
        $params[] = $keywordParam;
    }
}

// 添加筛选条件
if (!empty($filter_platform_name)) {
    $conditions[] = "platform_name LIKE ?";
    $params[] = "%{$filter_platform_name}%";
}

if (!empty($filter_location)) {
    $conditions[] = "location LIKE ?";
    $params[] = "%{$filter_location}%";
}

if (!empty($filter_valve_type)) {
    $conditions[] = "valve_type LIKE ?";
    $params[] = "%{$filter_valve_type}%";
}

if (!empty($filter_flow_type)) {
    $conditions[] = "flow_type LIKE ?";
    $params[] = "%{$filter_flow_type}%";
}

if (!empty($filter_valve_size)) {
    $conditions[] = "valve_size LIKE ?";
    $params[] = "%{$filter_valve_size}%";
}

if (!empty($filter_flange_type)) {
    $conditions[] = "flange_type LIKE ?";
    $params[] = "%{$filter_flange_type}%";
}

// 构建WHERE子句
$whereClause = count($conditions) > 0 ? implode(' AND ', $conditions) : '1';

// 获取筛选选项的唯一值
try {
    $platformNames = $pdo->query("SELECT DISTINCT platform_name FROM valve WHERE platform_name != '' ORDER BY platform_name")->fetchAll(PDO::FETCH_COLUMN);
    $locations = $pdo->query("SELECT DISTINCT location FROM valve WHERE location != '' ORDER BY location")->fetchAll(PDO::FETCH_COLUMN);
    $valveTypes = $pdo->query("SELECT DISTINCT valve_type FROM valve WHERE valve_type != '' ORDER BY valve_type")->fetchAll(PDO::FETCH_COLUMN);
    $flowTypes = $pdo->query("SELECT DISTINCT flow_type FROM valve WHERE flow_type != '' ORDER BY flow_type")->fetchAll(PDO::FETCH_COLUMN);
    $valveSizes = $pdo->query("SELECT DISTINCT valve_size FROM valve WHERE valve_size != '' ORDER BY valve_size")->fetchAll(PDO::FETCH_COLUMN);
    $flangeTypes = $pdo->query("SELECT DISTINCT flange_type FROM valve WHERE flange_type != '' ORDER BY flange_type")->fetchAll(PDO::FETCH_COLUMN);
} catch (PDOException $e) {
    error_log("获取筛选选项失败: " . $e->getMessage());
    $platformNames = $locations = $valveTypes = $flowTypes = $valveSizes = $flangeTypes = [];
}

// 获取总记录数
$totalStmt = $pdo->prepare("SELECT COUNT(*) FROM valve WHERE " . $whereClause);
$totalStmt->execute($params);
$total = $totalStmt->fetchColumn();

// 查询阀门数据
$sql = "SELECT * FROM valve WHERE " . $whereClause;
    
// 如果是按序号排序，则使用数字排序而不是字符串排序
if ($sort === 'serial_number') {
    $sql .= " ORDER BY CAST(serial_number AS UNSIGNED) $order";
} else {
    $sql .= " ORDER BY $sort $order";
}

$sql .= " LIMIT ? OFFSET ?";
$stmt = $pdo->prepare($sql);
$stmt->execute(array_merge($params, [$limit, $offset]));
$valves = $stmt->fetchAll();

// 计算总页数
$totalPages = ceil($total / $limit);
?>
<!DOCTYPE html>
<html>
<head>
    <title>阀门管理</title>
    <link rel="stylesheet" href="../assets/css/bootstrap.min.css">
    <link href="../assets/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/apple-style.css" rel="stylesheet">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <style>
        /* 表格文字居中 */
        .table th, .table td {
            text-align: center;
            vertical-align: middle;
            padding: 0.5rem;
            font-size: 0.9rem;
        }
        
        /* 阀门管理页面特定样式 */
        
        /* 页面顶部区域优化 */
        .page-header {
            margin-bottom: 1.5rem !important;
        }

        /* 搜索和统计区域优化 */
        .row.align-items-center.mb-3 {
            margin-bottom: 1rem !important;
        }

        .text-primary {
            color: #007aff !important;
        }

        .input-group-sm .form-control {
            font-size: 0.875rem;
            border-radius: 6px;
        }

        .input-group-sm .btn {
            border-radius: 0 6px 6px 0;
        }

        /* 筛选区域优化 */
        .apple-card {
            border: none !important;
            box-shadow: 0 2px 8px rgba(0,0,0,0.06) !important;
        }

        .apple-card-header.bg-white {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%) !important;
            border-bottom: 1px solid #e9ecef !important;
        }

        .apple-card-title.text-primary {
            color: #007aff !important;
            font-weight: 600;
            font-size: 1rem;
        }

        /* 筛选表单标签优化 */
        .form-label-sm {
            font-size: 0.75rem;
            font-weight: 600;
            color: #495057;
            margin-bottom: 0.25rem;
            display: block;
        }

        .form-label-sm i {
            width: 12px;
            font-size: 0.7rem;
        }

        /* 筛选选择框优化 */
        .form-control-sm {
            border-radius: 6px;
            border: 1.5px solid #e9ecef;
            transition: all 0.2s ease;
        }

        .form-control-sm:focus {
            border-color: #007aff;
            box-shadow: 0 0 0 2px rgba(0, 122, 255, 0.1);
        }

        /* 筛选按钮优化 */
        .btn-sm {
            padding: 0.375rem 0.75rem;
            font-size: 0.875rem;
            border-radius: 6px;
        }

        /* 筛选面板间距优化 */
        .card-body.py-3 {
            padding-top: 1rem !important;
            padding-bottom: 1rem !important;
        }

        /* 筛选选项行间距 */
        .row .col-lg-2.mb-2 {
            margin-bottom: 0.75rem !important;
        }

        /* 容器间距优化 */
        .container-fluid.px-4 {
            padding-top: 1rem !important;
        }

        /* 响应式优化 */
        @media (max-width: 768px) {
            .form-label-sm {
                font-size: 0.8rem;
            }

            .row .col-lg-2 {
                margin-bottom: 1rem !important;
            }
        }

        /* 搜索框样式 */
        .search-box {
            margin-bottom: 15px;
        }
        
        /* 分页样式 */
        .pagination {
            margin-top: 15px;
        }
        
        /* 每页显示条数选择器 */
        .page-size-selector {
            margin-left: 10px;
        }
        
        /* 表格行交替颜色 */
        .table-striped tbody tr:nth-of-type(odd) {
            background-color: rgba(0, 0, 0, 0.03);
        }
        
        .table-hover tbody tr:hover {
            background-color: rgba(0, 0, 0, 0.075);
        }
        
        /* 行高亮样式 */
        .highlighted-row {
            background-color: #fffde7;
        }
        
        /* 模态框样式优化 */
        .modal-header {
            background: linear-gradient(135deg, #4b6cb7 0%, #182848 100%);
            color: white;
        }
        
        .modal-title {
            font-weight: 600;
        }
        
        .close {
            color: white;
        }
        
        /* 导入窗口样式 */
        #importForm .file-upload {
            border: 2px dashed #ccc;
            padding: 2rem;
            text-align: center;
            border-radius: 5px;
            background-color: #f8f9fa;
            transition: all 0.3s;
        }
        
        #importForm .file-upload:hover {
            border-color: #4b6cb7;
            background-color: #f1f5ff;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #4b6cb7 0%, #182848 100%);
            border: none;
        }
        
        .btn-primary:hover {
            background: linear-gradient(135deg, #5d7ec9 0%, #243a6a 100%);
        }
        
        /* 阀门编号列样式 */
        .valve-code-cell {
            max-width: 120px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            cursor: pointer;
        }
        
        /* 弹出显示全部内容的样式 */
        .popup-content {
            display: none;
            position: absolute;
            background-color: #fff;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
            z-index: 1000;
            max-width: 300px;
            word-break: break-all;
        }
    </style>
</head>
<body>
<div class="container-fluid px-4">
    <!-- 页面标题 -->
    <div class="page-header">
        <h3><i class="fas fa-tint mr-2"></i>阀门管理</h3>
        <div class="btn-group">
            <button class="btn btn-primary" id="addValveBtn">
                <i class="fas fa-plus mr-1"></i> 添加阀门
            </button>
            <button class="btn btn-warning" data-toggle="modal" data-target="#importModal">
                <i class="fas fa-file-import mr-1"></i> 导入数据
            </button>
            <button id="deleteAllBtn" class="btn btn-danger">
                <i class="fas fa-trash-alt mr-1"></i> 全部删除
            </button>
        </div>
    </div>

    <!-- 搜索和统计区域 -->
    <div class="row align-items-center mb-3">
        <div class="col-md-6">
            <span class="text-muted">
                共找到 <strong class="text-primary"><?= $total ?></strong> 条阀门记录
            </span>
        </div>
        <div class="col-md-6">
            <form class="form-inline justify-content-end" method="get">
                <div class="input-group input-group-sm">
                    <input type="text" class="form-control" name="search" placeholder="搜索阀门名称..." value="<?= htmlspecialchars($search) ?>">
                    <div class="input-group-append">
                        <button class="btn btn-outline-secondary" type="submit" title="搜索">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- 筛选区域 -->
    <div class="card apple-card mb-3 border-0 shadow-sm">
        <div class="card-header apple-card-header bg-white py-2">
            <div class="d-flex justify-content-between align-items-center">
                <h6 class="mb-0 apple-card-title text-primary">
                    <i class="fas fa-filter mr-2"></i> 筛选选项
                    <button class="btn btn-sm btn-link apple-btn-link ml-2 p-1" type="button" data-toggle="collapse" data-target="#filterPanel" aria-expanded="false">
                        <i class="fas fa-chevron-down"></i>
                    </button>
                </h6>
                <?php if (!empty($filter_platform_name) || !empty($filter_location) || !empty($filter_valve_type) || !empty($filter_flow_type) || !empty($filter_valve_size) || !empty($filter_flange_type)): ?>
                <div class="apple-badge">
                    <span class="badge badge-primary">已筛选</span>
                </div>
                <?php endif; ?>
            </div>
        </div>
        <div class="collapse <?= (!empty($filter_platform_name) || !empty($filter_location) || !empty($filter_valve_type) || !empty($filter_flow_type) || !empty($filter_valve_size) || !empty($filter_flange_type)) ? 'show' : '' ?>" id="filterPanel">
            <div class="card-body py-3">
                <form method="get" id="filterForm" class="mb-0">
                    <!-- 保留其他GET参数 -->
                    <?php if (!empty($search)): ?>
                    <input type="hidden" name="search" value="<?= htmlspecialchars($search) ?>">
                    <?php endif; ?>
                    <input type="hidden" name="sort" value="<?= htmlspecialchars($sort) ?>">
                    <input type="hidden" name="order" value="<?= htmlspecialchars($order) ?>">
                    <?php if (!empty($filter_platform_name)): ?>
                    <input type="hidden" name="filter_platform_name" value="<?= htmlspecialchars($filter_platform_name) ?>">
                    <?php endif; ?>
                    <?php if (!empty($filter_location)): ?>
                    <input type="hidden" name="filter_location" value="<?= htmlspecialchars($filter_location) ?>">
                    <?php endif; ?>
                    <?php if (!empty($filter_valve_type)): ?>
                    <input type="hidden" name="filter_valve_type" value="<?= htmlspecialchars($filter_valve_type) ?>">
                    <?php endif; ?>
                    <?php if (!empty($filter_flow_type)): ?>
                    <input type="hidden" name="filter_flow_type" value="<?= htmlspecialchars($filter_flow_type) ?>">
                    <?php endif; ?>
                    <?php if (!empty($filter_valve_size)): ?>
                    <input type="hidden" name="filter_valve_size" value="<?= htmlspecialchars($filter_valve_size) ?>">
                    <?php endif; ?>
                    <?php if (!empty($filter_flange_type)): ?>
                    <input type="hidden" name="filter_flange_type" value="<?= htmlspecialchars($filter_flange_type) ?>">
                    <?php endif; ?>
                    <div class="row">
                        <div class="col-lg-2 col-md-4 col-sm-6 mb-2">
                            <div class="form-group mb-0">
                                <label for="filter_platform_name" class="form-label-sm">
                                    <i class="fas fa-building mr-1 text-muted"></i>平台名称
                                </label>
                                <select name="filter_platform_name" id="filter_platform_name" class="form-control form-control-sm">
                                    <option value="">全部</option>
                                    <?php foreach($platformNames as $platformName): ?>
                                    <option value="<?= htmlspecialchars($platformName) ?>" <?= $filter_platform_name == $platformName ? 'selected' : '' ?>>
                                        <?= htmlspecialchars($platformName) ?>
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>

                        <div class="col-lg-2 col-md-4 col-sm-6 mb-2">
                            <div class="form-group mb-0">
                                <label for="filter_location" class="form-label-sm">
                                    <i class="fas fa-map-marker-alt mr-1 text-muted"></i>所在位置
                                </label>
                                <select name="filter_location" id="filter_location" class="form-control form-control-sm">
                                    <option value="">全部</option>
                                    <?php foreach($locations as $location): ?>
                                    <option value="<?= htmlspecialchars($location) ?>" <?= $filter_location == $location ? 'selected' : '' ?>>
                                        <?= htmlspecialchars($location) ?>
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>

                        <div class="col-lg-2 col-md-4 col-sm-6 mb-2">
                            <div class="form-group mb-0">
                                <label for="filter_valve_type" class="form-label-sm">
                                    <i class="fas fa-cog mr-1 text-muted"></i>阀门类型
                                </label>
                                <select name="filter_valve_type" id="filter_valve_type" class="form-control form-control-sm">
                                    <option value="">全部</option>
                                    <?php foreach($valveTypes as $valveType): ?>
                                    <option value="<?= htmlspecialchars($valveType) ?>" <?= $filter_valve_type == $valveType ? 'selected' : '' ?>>
                                        <?= htmlspecialchars($valveType) ?>
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>

                        <div class="col-lg-2 col-md-4 col-sm-6 mb-2">
                            <div class="form-group mb-0">
                                <label for="filter_flow_type" class="form-label-sm">
                                    <i class="fas fa-stream mr-1 text-muted"></i>流程类型
                                </label>
                                <select name="filter_flow_type" id="filter_flow_type" class="form-control form-control-sm">
                                    <option value="">全部</option>
                                    <?php foreach($flowTypes as $flowType): ?>
                                    <option value="<?= htmlspecialchars($flowType) ?>" <?= $filter_flow_type == $flowType ? 'selected' : '' ?>>
                                        <?= htmlspecialchars($flowType) ?>
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>

                        <div class="col-lg-2 col-md-4 col-sm-6 mb-2">
                            <div class="form-group mb-0">
                                <label for="filter_valve_size" class="form-label-sm">
                                    <i class="fas fa-ruler mr-1 text-muted"></i>阀门尺寸
                                </label>
                                <select name="filter_valve_size" id="filter_valve_size" class="form-control form-control-sm">
                                    <option value="">全部</option>
                                    <?php foreach($valveSizes as $valveSize): ?>
                                    <option value="<?= htmlspecialchars($valveSize) ?>" <?= $filter_valve_size == $valveSize ? 'selected' : '' ?>>
                                        <?= htmlspecialchars($valveSize) ?>
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>

                        <div class="col-lg-2 col-md-4 col-sm-6 mb-2">
                            <div class="form-group mb-0">
                                <label for="filter_flange_type" class="form-label-sm">
                                    <i class="fas fa-circle-notch mr-1 text-muted"></i>法兰形式
                                </label>
                                <select name="filter_flange_type" id="filter_flange_type" class="form-control form-control-sm">
                                    <option value="">全部</option>
                                    <?php foreach($flangeTypes as $flangeType): ?>
                                    <option value="<?= htmlspecialchars($flangeType) ?>" <?= $filter_flange_type == $flangeType ? 'selected' : '' ?>>
                                        <?= htmlspecialchars($flangeType) ?>
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>

                        <div class="col-12 mb-2">
                            <div class="d-flex justify-content-center">
                                <button type="submit" class="btn btn-primary btn-sm mr-2">
                                    <i class="fas fa-filter mr-1"></i> 应用筛选
                                </button>
                                <a href="valve.php" class="btn btn-outline-secondary btn-sm">
                                    <i class="fas fa-undo mr-1"></i> 重置筛选
                                </a>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            
            <?php if (!empty($filter_platform_name) || !empty($filter_location) || !empty($filter_valve_type) || !empty($filter_flow_type) || !empty($filter_valve_size) || !empty($filter_flange_type)): ?>
            <div class="card-footer bg-light py-2">
                <div class="d-flex flex-wrap gap-2">
                    <div class="me-2"><strong>当前筛选：</strong></div>
                    <?php if (!empty($filter_platform_name)): ?>
                        <span class="badge badge-primary me-1">平台名称: <?= htmlspecialchars($filter_platform_name) ?></span>
                    <?php endif; ?>
                    
                    <?php if (!empty($filter_location)): ?>
                        <span class="badge badge-primary me-1">所在位置: <?= htmlspecialchars($filter_location) ?></span>
                    <?php endif; ?>
                    
                    <?php if (!empty($filter_valve_type)): ?>
                        <span class="badge badge-primary me-1">阀门类型: <?= htmlspecialchars($filter_valve_type) ?></span>
                    <?php endif; ?>
                    
                    <?php if (!empty($filter_flow_type)): ?>
                        <span class="badge badge-primary me-1">流程类型: <?= htmlspecialchars($filter_flow_type) ?></span>
                    <?php endif; ?>
                    
                    <?php if (!empty($filter_valve_size)): ?>
                        <span class="badge badge-primary me-1">阀门尺寸: <?= htmlspecialchars($filter_valve_size) ?></span>
                    <?php endif; ?>
                    
                    <?php if (!empty($filter_flange_type)): ?>
                        <span class="badge badge-primary me-1">法兰形式: <?= htmlspecialchars($filter_flange_type) ?></span>
                    <?php endif; ?>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>

    <div class="table-responsive">
        <div class="text-muted small mb-2">
            <i class="fas fa-info-circle"></i> 提示：点击表头可按该列进行排序
        </div>
        <table class="table table-bordered table-hover table-striped">
            <thead class="thead-light">
                <tr>
                    <th>
                        <a href="?sort=serial_number&order=<?= $sort === 'serial_number' && $order === 'ASC' ? 'desc' : 'asc' ?><?= 
                            (!empty($search) ? '&search='.urlencode($search) : '') .
                            (!empty($filter_platform_name) ? '&filter_platform_name='.urlencode($filter_platform_name) : '') .
                            (!empty($filter_location) ? '&filter_location='.urlencode($filter_location) : '') .
                            (!empty($filter_valve_type) ? '&filter_valve_type='.urlencode($filter_valve_type) : '') .
                            (!empty($filter_flow_type) ? '&filter_flow_type='.urlencode($filter_flow_type) : '') .
                            (!empty($filter_valve_size) ? '&filter_valve_size='.urlencode($filter_valve_size) : '') .
                            (!empty($filter_flange_type) ? '&filter_flange_type='.urlencode($filter_flange_type) : '')
                        ?>">
                            序号
                            <?php if ($sort === 'serial_number'): ?>
                                <i class="fas fa-sort-<?= $order === 'ASC' ? 'up' : 'down' ?>"></i>
                            <?php else: ?>
                                <i class="fas fa-sort text-muted"></i>
                            <?php endif; ?>
                        </a>
                    </th>
                    <th>
                        <a href="?sort=platform_name&order=<?= $sort === 'platform_name' && $order === 'ASC' ? 'desc' : 'asc' ?><?= 
                            (!empty($search) ? '&search='.urlencode($search) : '') .
                            (!empty($filter_platform_name) ? '&filter_platform_name='.urlencode($filter_platform_name) : '') .
                            (!empty($filter_location) ? '&filter_location='.urlencode($filter_location) : '') .
                            (!empty($filter_valve_type) ? '&filter_valve_type='.urlencode($filter_valve_type) : '') .
                            (!empty($filter_flow_type) ? '&filter_flow_type='.urlencode($filter_flow_type) : '') .
                            (!empty($filter_valve_size) ? '&filter_valve_size='.urlencode($filter_valve_size) : '') .
                            (!empty($filter_flange_type) ? '&filter_flange_type='.urlencode($filter_flange_type) : '')
                        ?>">
                            平台名称
                            <?php if ($sort === 'platform_name'): ?>
                                <i class="fas fa-sort-<?= $order === 'ASC' ? 'up' : 'down' ?>"></i>
                            <?php else: ?>
                                <i class="fas fa-sort text-muted"></i>
                            <?php endif; ?>
                        </a>
                    </th>
                    <th>阀门名称</th>
                    <th>阀门编号</th>
                    <th>
                        <a href="?sort=location&order=<?= $sort === 'location' && $order === 'ASC' ? 'desc' : 'asc' ?><?= 
                            (!empty($search) ? '&search='.urlencode($search) : '') .
                            (!empty($filter_platform_name) ? '&filter_platform_name='.urlencode($filter_platform_name) : '') .
                            (!empty($filter_location) ? '&filter_location='.urlencode($filter_location) : '') .
                            (!empty($filter_valve_type) ? '&filter_valve_type='.urlencode($filter_valve_type) : '') .
                            (!empty($filter_flow_type) ? '&filter_flow_type='.urlencode($filter_flow_type) : '') .
                            (!empty($filter_valve_size) ? '&filter_valve_size='.urlencode($filter_valve_size) : '') .
                            (!empty($filter_flange_type) ? '&filter_flange_type='.urlencode($filter_flange_type) : '')
                        ?>">
                            所在位置
                            <?php if ($sort === 'location'): ?>
                                <i class="fas fa-sort-<?= $order === 'ASC' ? 'up' : 'down' ?>"></i>
                            <?php else: ?>
                                <i class="fas fa-sort text-muted"></i>
                            <?php endif; ?>
                        </a>
                    </th>
                    <th>阀门功能</th>
                    <th>
                        <a href="?sort=valve_type&order=<?= $sort === 'valve_type' && $order === 'ASC' ? 'desc' : 'asc' ?><?= 
                            (!empty($search) ? '&search='.urlencode($search) : '') .
                            (!empty($filter_platform_name) ? '&filter_platform_name='.urlencode($filter_platform_name) : '') .
                            (!empty($filter_location) ? '&filter_location='.urlencode($filter_location) : '') .
                            (!empty($filter_valve_type) ? '&filter_valve_type='.urlencode($filter_valve_type) : '') .
                            (!empty($filter_flow_type) ? '&filter_flow_type='.urlencode($filter_flow_type) : '') .
                            (!empty($filter_valve_size) ? '&filter_valve_size='.urlencode($filter_valve_size) : '') .
                            (!empty($filter_flange_type) ? '&filter_flange_type='.urlencode($filter_flange_type) : '')
                        ?>">
                            阀门类型
                            <?php if ($sort === 'valve_type'): ?>
                                <i class="fas fa-sort-<?= $order === 'ASC' ? 'up' : 'down' ?>"></i>
                            <?php else: ?>
                                <i class="fas fa-sort text-muted"></i>
                            <?php endif; ?>
                        </a>
                    </th>
                    <th>
                        <a href="?sort=flow_type&order=<?= $sort === 'flow_type' && $order === 'ASC' ? 'desc' : 'asc' ?><?= 
                            (!empty($search) ? '&search='.urlencode($search) : '') .
                            (!empty($filter_platform_name) ? '&filter_platform_name='.urlencode($filter_platform_name) : '') .
                            (!empty($filter_location) ? '&filter_location='.urlencode($filter_location) : '') .
                            (!empty($filter_valve_type) ? '&filter_valve_type='.urlencode($filter_valve_type) : '') .
                            (!empty($filter_flow_type) ? '&filter_flow_type='.urlencode($filter_flow_type) : '') .
                            (!empty($filter_valve_size) ? '&filter_valve_size='.urlencode($filter_valve_size) : '') .
                            (!empty($filter_flange_type) ? '&filter_flange_type='.urlencode($filter_flange_type) : '')
                        ?>">
                            流程类型
                            <?php if ($sort === 'flow_type'): ?>
                                <i class="fas fa-sort-<?= $order === 'ASC' ? 'up' : 'down' ?>"></i>
                            <?php else: ?>
                                <i class="fas fa-sort text-muted"></i>
                            <?php endif; ?>
                        </a>
                    </th>
                    <th>
                        <a href="?sort=valve_size&order=<?= $sort === 'valve_size' && $order === 'ASC' ? 'desc' : 'asc' ?><?= 
                            (!empty($search) ? '&search='.urlencode($search) : '') .
                            (!empty($filter_platform_name) ? '&filter_platform_name='.urlencode($filter_platform_name) : '') .
                            (!empty($filter_location) ? '&filter_location='.urlencode($filter_location) : '') .
                            (!empty($filter_valve_type) ? '&filter_valve_type='.urlencode($filter_valve_type) : '') .
                            (!empty($filter_flow_type) ? '&filter_flow_type='.urlencode($filter_flow_type) : '') .
                            (!empty($filter_valve_size) ? '&filter_valve_size='.urlencode($filter_valve_size) : '') .
                            (!empty($filter_flange_type) ? '&filter_flange_type='.urlencode($filter_flange_type) : '')
                        ?>">
                            阀门尺寸
                            <?php if ($sort === 'valve_size'): ?>
                                <i class="fas fa-sort-<?= $order === 'ASC' ? 'up' : 'down' ?>"></i>
                            <?php else: ?>
                                <i class="fas fa-sort text-muted"></i>
                            <?php endif; ?>
                        </a>
                    </th>
                    <th>
                        <a href="?sort=flange_type&order=<?= $sort === 'flange_type' && $order === 'ASC' ? 'desc' : 'asc' ?><?= 
                            (!empty($search) ? '&search='.urlencode($search) : '') .
                            (!empty($filter_platform_name) ? '&filter_platform_name='.urlencode($filter_platform_name) : '') .
                            (!empty($filter_location) ? '&filter_location='.urlencode($filter_location) : '') .
                            (!empty($filter_valve_type) ? '&filter_valve_type='.urlencode($filter_valve_type) : '') .
                            (!empty($filter_flow_type) ? '&filter_flow_type='.urlencode($filter_flow_type) : '') .
                            (!empty($filter_valve_size) ? '&filter_valve_size='.urlencode($filter_valve_size) : '') .
                            (!empty($filter_flange_type) ? '&filter_flange_type='.urlencode($filter_flange_type) : '')
                        ?>">
                            法兰形式
                            <?php if ($sort === 'flange_type'): ?>
                                <i class="fas fa-sort-<?= $order === 'ASC' ? 'up' : 'down' ?>"></i>
                            <?php else: ?>
                                <i class="fas fa-sort text-muted"></i>
                            <?php endif; ?>
                        </a>
                    </th>
                    <th>法兰孔数</th>
                    <th>密封面形式</th>
                    <th>垫子类型</th>
                    <th>投产时间</th>
                    <th>备注</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
                <?php 
                $index = ($page - 1) * $limit + 1;
                
                if(count($valves) > 0): 
                    foreach($valves as $valve): 
                ?>
                <tr>
                    <td><?= htmlspecialchars($valve['serial_number'] ?? '') ?></td>
                    <td><?= htmlspecialchars($valve['platform_name'] ?? '') ?></td>
                    <td><?= htmlspecialchars($valve['valve_name'] ?? '') ?></td>
                    <td class="valve-code-cell" title="点击查看完整内容" data-valve-code="<?= htmlspecialchars($valve['valve_code'] ?? '') ?>"><?= htmlspecialchars($valve['valve_code'] ?? '') ?></td>
                    <td><?= htmlspecialchars($valve['location'] ?? '') ?></td>
                    <td><?= htmlspecialchars($valve['function_desc'] ?? '') ?></td>
                    <td><?= htmlspecialchars($valve['valve_type'] ?? '') ?></td>
                    <td><?= htmlspecialchars($valve['flow_type'] ?? '') ?></td>
                    <td><?= htmlspecialchars($valve['valve_size'] ?? '') ?></td>
                    <td><?= htmlspecialchars($valve['flange_type'] ?? '') ?></td>
                    <td><?= htmlspecialchars($valve['flange_holes'] ?? '') ?></td>
                    <td><?= htmlspecialchars($valve['seal_type'] ?? '') ?></td>
                    <td><?= htmlspecialchars($valve['gasket_type'] ?? '') ?></td>
                    <td><?= $valve['operation_date'] ? date('Y-m-d', strtotime($valve['operation_date'])) : '' ?></td>
                    <td><?= htmlspecialchars($valve['remark'] ?? '') ?></td>
                    <td>
                        <button class="btn btn-sm btn-warning btn-edit"
                                data-id="<?= $valve['id'] ?>"
                                data-platform-name="<?= htmlspecialchars($valve['platform_name'] ?? '') ?>"
                                data-valve-name="<?= htmlspecialchars($valve['valve_name'] ?? '') ?>"
                                data-serial-number="<?= htmlspecialchars($valve['serial_number'] ?? '') ?>"
                                data-valve-code="<?= htmlspecialchars($valve['valve_code'] ?? '') ?>"
                                data-location="<?= htmlspecialchars($valve['location'] ?? '') ?>"
                                data-function-desc="<?= htmlspecialchars($valve['function_desc'] ?? '') ?>"
                                data-valve-type="<?= htmlspecialchars($valve['valve_type'] ?? '') ?>"
                                data-flow-type="<?= htmlspecialchars($valve['flow_type'] ?? '') ?>"
                                data-valve-size="<?= htmlspecialchars($valve['valve_size'] ?? '') ?>"
                                data-flange-type="<?= htmlspecialchars($valve['flange_type'] ?? '') ?>"
                                data-flange-holes="<?= htmlspecialchars($valve['flange_holes'] ?? '') ?>"
                                data-seal-type="<?= htmlspecialchars($valve['seal_type'] ?? '') ?>"
                                data-gasket-type="<?= htmlspecialchars($valve['gasket_type'] ?? '') ?>"
                                data-operation-date="<?= $valve['operation_date'] ? date('Y-m-d', strtotime($valve['operation_date'])) : '' ?>"
                                data-remark="<?= htmlspecialchars($valve['remark'] ?? '') ?>">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-danger btn-delete" data-id="<?= $valve['id'] ?>">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                </tr>
                <?php 
                    endforeach; 
                else: 
                ?>
                <tr>
                    <td colspan="15" class="text-center">没有找到匹配的记录</td>
                </tr>
                <?php endif; ?>
            </tbody>
        </table>
    </div>

    <!-- 优化的分页 -->
            <div class="d-flex justify-content-between align-items-center">
        <div>
            <span>共 <?= $total ?> 条记录，当前 <?= $page ?>/<?= $totalPages ?> 页</span>
        </div>
        
        <div>
            <nav aria-label="Page navigation">
                <ul class="pagination justify-content-center mb-0">
                    <?php if ($page > 1): ?>
                    <li class="page-item">
                        <a class="page-link" href="?page=<?= $page - 1 ?>&limit=<?= $limit ?><?= 
                            (!empty($search) ? '&search='.urlencode($search) : '') .
                            '&sort='.$sort.'&order='.$order .
                            (!empty($filter_platform_name) ? '&filter_platform_name='.urlencode($filter_platform_name) : '') .
                            (!empty($filter_location) ? '&filter_location='.urlencode($filter_location) : '') .
                            (!empty($filter_valve_type) ? '&filter_valve_type='.urlencode($filter_valve_type) : '') .
                            (!empty($filter_flow_type) ? '&filter_flow_type='.urlencode($filter_flow_type) : '') .
                            (!empty($filter_valve_size) ? '&filter_valve_size='.urlencode($filter_valve_size) : '') .
                            (!empty($filter_flange_type) ? '&filter_flange_type='.urlencode($filter_flange_type) : '')
                        ?>">
                            上一页
                        </a>
                    </li>
                    <?php endif; ?>
                    
                    <?php
                    // 计算需要显示的页码范围
                    $startPage = max(1, $page - 2);
                    $endPage = min($totalPages, $page + 2);
                    
                    // 显示第一页
                    if ($startPage > 1) {
                        echo '<li class="page-item"><a class="page-link" href="?page=1&limit='.$limit.
                            (!empty($search) ? '&search='.urlencode($search) : '') .
                            '&sort='.$sort.'&order='.$order .
                            (!empty($filter_platform_name) ? '&filter_platform_name='.urlencode($filter_platform_name) : '') .
                            (!empty($filter_location) ? '&filter_location='.urlencode($filter_location) : '') .
                            (!empty($filter_valve_type) ? '&filter_valve_type='.urlencode($filter_valve_type) : '') .
                            (!empty($filter_flow_type) ? '&filter_flow_type='.urlencode($filter_flow_type) : '') .
                            (!empty($filter_valve_size) ? '&filter_valve_size='.urlencode($filter_valve_size) : '') .
                            (!empty($filter_flange_type) ? '&filter_flange_type='.urlencode($filter_flange_type) : '') .
                            '">1</a></li>';
                        if ($startPage > 2) {
                            echo '<li class="page-item disabled"><a class="page-link" href="#">...</a></li>';
                        }
                    }
                    
                    // 显示中间的页码
                    for ($i = $startPage; $i <= $endPage; $i++) {
                        echo '<li class="page-item '.($i == $page ? 'active' : '').'">
                            <a class="page-link" href="?page='.$i.'&limit='.$limit.
                            (!empty($search) ? '&search='.urlencode($search) : '') .
                            '&sort='.$sort.'&order='.$order .
                            (!empty($filter_platform_name) ? '&filter_platform_name='.urlencode($filter_platform_name) : '') .
                            (!empty($filter_location) ? '&filter_location='.urlencode($filter_location) : '') .
                            (!empty($filter_valve_type) ? '&filter_valve_type='.urlencode($filter_valve_type) : '') .
                            (!empty($filter_flow_type) ? '&filter_flow_type='.urlencode($filter_flow_type) : '') .
                            (!empty($filter_valve_size) ? '&filter_valve_size='.urlencode($filter_valve_size) : '') .
                            (!empty($filter_flange_type) ? '&filter_flange_type='.urlencode($filter_flange_type) : '') .
                            '">'.$i.'</a>
                        </li>';
                    }
                    
                    // 显示最后一页
                    if ($endPage < $totalPages) {
                        if ($endPage < $totalPages - 1) {
                            echo '<li class="page-item disabled"><a class="page-link" href="#">...</a></li>';
                        }
                        echo '<li class="page-item"><a class="page-link" href="?page='.$totalPages.'&limit='.$limit.
                            (!empty($search) ? '&search='.urlencode($search) : '') .
                            '&sort='.$sort.'&order='.$order .
                            (!empty($filter_platform_name) ? '&filter_platform_name='.urlencode($filter_platform_name) : '') .
                            (!empty($filter_location) ? '&filter_location='.urlencode($filter_location) : '') .
                            (!empty($filter_valve_type) ? '&filter_valve_type='.urlencode($filter_valve_type) : '') .
                            (!empty($filter_flow_type) ? '&filter_flow_type='.urlencode($filter_flow_type) : '') .
                            (!empty($filter_valve_size) ? '&filter_valve_size='.urlencode($filter_valve_size) : '') .
                            (!empty($filter_flange_type) ? '&filter_flange_type='.urlencode($filter_flange_type) : '') .
                            '">'.$totalPages.'</a></li>';
                    }
                    ?>
                    
                    <?php if ($page < $totalPages): ?>
                    <li class="page-item">
                        <a class="page-link" href="?page=<?= $page + 1 ?>&limit=<?= $limit ?><?= 
                            (!empty($search) ? '&search='.urlencode($search) : '') .
                            '&sort='.$sort.'&order='.$order .
                            (!empty($filter_platform_name) ? '&filter_platform_name='.urlencode($filter_platform_name) : '') .
                            (!empty($filter_location) ? '&filter_location='.urlencode($filter_location) : '') .
                            (!empty($filter_valve_type) ? '&filter_valve_type='.urlencode($filter_valve_type) : '') .
                            (!empty($filter_flow_type) ? '&filter_flow_type='.urlencode($filter_flow_type) : '') .
                            (!empty($filter_valve_size) ? '&filter_valve_size='.urlencode($filter_valve_size) : '') .
                            (!empty($filter_flange_type) ? '&filter_flange_type='.urlencode($filter_flange_type) : '')
                        ?>">
                            下一页
                        </a>
                    </li>
                    <?php endif; ?>
                </ul>
            </nav>
        </div>
        
        <div>
            <form class="form-inline" method="get">
                <input type="hidden" name="search" value="<?= htmlspecialchars($search) ?>">
                <input type="hidden" name="sort" value="<?= htmlspecialchars($sort) ?>">
                <input type="hidden" name="order" value="<?= htmlspecialchars($order) ?>">
                <?php if (!empty($filter_platform_name)): ?>
                <input type="hidden" name="filter_platform_name" value="<?= htmlspecialchars($filter_platform_name) ?>">
                <?php endif; ?>
                <?php if (!empty($filter_location)): ?>
                <input type="hidden" name="filter_location" value="<?= htmlspecialchars($filter_location) ?>">
                <?php endif; ?>
                <?php if (!empty($filter_valve_type)): ?>
                <input type="hidden" name="filter_valve_type" value="<?= htmlspecialchars($filter_valve_type) ?>">
                <?php endif; ?>
                <?php if (!empty($filter_flow_type)): ?>
                <input type="hidden" name="filter_flow_type" value="<?= htmlspecialchars($filter_flow_type) ?>">
                <?php endif; ?>
                <?php if (!empty($filter_valve_size)): ?>
                <input type="hidden" name="filter_valve_size" value="<?= htmlspecialchars($filter_valve_size) ?>">
                <?php endif; ?>
                <?php if (!empty($filter_flange_type)): ?>
                <input type="hidden" name="filter_flange_type" value="<?= htmlspecialchars($filter_flange_type) ?>">
                <?php endif; ?>
                <div class="d-flex align-items-center">
                    <div class="form-group mb-0 mr-3">
                        <div class="d-flex align-items-center">
                            <label class="mr-2 mb-0 text-nowrap">每页显示</label>
                            <select name="limit" class="form-control form-control-sm" onchange="this.form.submit()" style="width: 80px;">
                                <option value="10" <?= $limit == 10 ? 'selected' : '' ?>>10</option>
                                <option value="20" <?= $limit == 20 ? 'selected' : '' ?>>20</option>
                                <option value="50" <?= $limit == 50 ? 'selected' : '' ?>>50</option>
                                <option value="100" <?= $limit == 100 ? 'selected' : '' ?>>100</option>
                            </select>
                            <span class="ml-1 text-nowrap">条</span>
                        </div>
                    </div>
                    <div class="form-group mb-0">
                        <div class="d-flex align-items-center">
                            <label class="mr-2 mb-0 text-nowrap">跳转到</label>
                            <input type="number" name="page" class="form-control form-control-sm" min="1" max="<?= $totalPages ?>" value="<?= $page ?>" style="width: 70px;">
                            <button type="submit" class="btn btn-sm btn-primary apple-btn ml-2">GO</button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- 添加/编辑模态框 -->
    <div class="modal fade" id="addModal">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <form id="dataForm">
                    <input type="hidden" name="id">
                    <div class="modal-header">
                        <h5 class="modal-title"><i class="fas fa-plus-circle mr-2"></i>添加阀门</h5>
                        <button type="button" class="close" data-dismiss="modal">&times;</button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>阀门序号 <span class="text-danger">*</span></label>
                                    <input type="text" name="serial_number" class="form-control" required>
                                    <small class="form-text text-muted">请输入唯一的阀门序号（Excel中A列）</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>平台名称 <span class="text-danger">*</span></label>
                                    <input type="text" name="platform_name" class="form-control" required>
                                    <small class="form-text text-muted">平台名称（Excel中B列）</small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>阀门名称 <span class="text-danger">*</span></label>
                                    <input type="text" name="valve_name" class="form-control valve-field" required>
                                    <small class="form-text text-muted">阀门名称（Excel中C列）</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>阀门编号 <span class="text-danger">*</span></label>
                                    <input type="text" name="valve_code" class="form-control" readonly>
                                    <small class="form-text text-muted">阀门编号将根据其他字段自动生成，无需手动填写</small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>所在位置</label>
                                    <input type="text" name="location" class="form-control valve-field">
                                    <small class="form-text text-muted">用于自动生成阀门编号</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>阀门类型</label>
                                    <input type="text" name="valve_type" class="form-control valve-field">
                                    <small class="form-text text-muted">用于自动生成阀门编号</small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>流程类型</label>
                                    <input type="text" name="flow_type" class="form-control valve-field">
                                    <small class="form-text text-muted">用于自动生成阀门编号</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>阀门尺寸</label>
                                    <input type="text" name="valve_size" class="form-control valve-field">
                                    <small class="form-text text-muted">用于自动生成阀门编号</small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>法兰形式</label>
                                    <input type="text" name="flange_type" class="form-control valve-field">
                                    <small class="form-text text-muted">用于自动生成阀门编号括号部分</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>法兰孔数</label>
                                    <input type="text" name="flange_holes" class="form-control valve-field">
                                    <small class="form-text text-muted">用于自动生成阀门编号括号部分</small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>密封面形式</label>
                                    <input type="text" name="seal_type" class="form-control valve-field">
                                    <small class="form-text text-muted">用于自动生成阀门编号括号部分</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>垫子类型</label>
                                    <input type="text" name="gasket_type" class="form-control valve-field">
                                    <small class="form-text text-muted">用于自动生成阀门编号括号部分</small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label>阀门功能</label>
                            <textarea name="function_desc" class="form-control valve-field" rows="2"></textarea>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>投产时间</label>
                                    <input type="date" name="operation_date" class="form-control">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>备注</label>
                                    <textarea name="remark" class="form-control" rows="2"></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                        <button type="submit" class="btn btn-primary">保存</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 导入模态框 -->
    <div class="modal fade" id="importModal">
        <div class="modal-dialog">
            <div class="modal-content">
                <form id="importForm" enctype="multipart/form-data">
                    <div class="modal-header">
                        <h5 class="modal-title"><i class="fas fa-file-import mr-2"></i>导入阀门数据</h5>
                        <button type="button" class="close" data-dismiss="modal">&times;</button>
                    </div>
                    <div class="modal-body">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle mr-2"></i>请上传Excel文件，从第5行开始读取数据。
                            <br>表格列对应关系:
                            <ul class="mb-0 mt-1">
                                <li><strong>A列</strong>: 序号 <span class="text-danger">(必填)</span>，作为阀门唯一标识，会显示在表格序号列中</li>
                                <li><strong>B列</strong>: 平台名称 <span class="text-danger">(必填)</span></li>
                                <li><strong>C列</strong>: 阀门名称 <span class="text-danger">(必填)</span></li>
                                <li><strong>D列</strong>: 阀门编号 <span class="text-success">(忽略此列)</span>，系统会根据E-M列自动生成阀门编号</li>
                                <li><strong>E列</strong>: 所在位置（用于自动生成阀门编号）</li>
                                <li><strong>F列</strong>: 阀门功能</li>
                                <li><strong>G列</strong>: 阀门类型（用于自动生成阀门编号）</li>
                                <li><strong>H列</strong>: 流程类型（用于自动生成阀门编号）</li>
                                <li><strong>I列</strong>: 阀门尺寸（用于自动生成阀门编号）</li>
                                <li><strong>J列</strong>: 法兰形式（用于自动生成阀门编号括号部分）</li>
                                <li><strong>K列</strong>: 法兰孔数（用于自动生成阀门编号括号部分）</li>
                                <li><strong>L列</strong>: 密封面形式（用于自动生成阀门编号括号部分）</li>
                                <li><strong>M列</strong>: 垫子类型（用于自动生成阀门编号括号部分）</li>
                                <li><strong>N列</strong>: 投产时间</li>
                                <li><strong>O列</strong>: 备注</li>
                            </ul>
                            <p class="mt-2 mb-0 text-danger"><small>注意：A、B、C列为必填项。D列将被忽略，系统会根据G-M列自动生成阀门编号。A列序号会显示在表格的序号列中，系统将按照A列序号检查重复，如有重复会自动生成新序号。</small></p>
                        </div>
                        <div class="form-group file-upload">
                            <label for="excelFile"><i class="fas fa-file-excel fa-2x mb-2 text-success"></i></label>
                            <input type="file" id="excelFile" name="excel" class="form-control-file" required accept=".xlsx,.xls">
                            <p class="mt-2 text-muted">点击或拖拽Excel文件至此处</p>
                        </div>
                        <div class="progress d-none">
                            <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 100%"></div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-upload mr-1"></i> 开始导入
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script src="../assets/js/jquery.min.js"></script>
<script src="../assets/js/bootstrap.bundle.min.js"></script>
<script src="../assets/js/fix_frontend_errors.js"></script>
<script>
$(document).ready(function() {
    // 初始化页面
    if (window.parent && window.parent.document) {
        // 更新localStorage中保存的当前页面
        window.parent.localStorage.setItem('currentPage', 'pages/valve.php');
        
        // 更新父窗口中的菜单高亮状态
        const menuItems = window.parent.document.querySelectorAll('a[target="contentFrame"]');
        menuItems.forEach(item => {
            item.classList.remove('active-menu-item');
        });
        const valveLink = window.parent.document.querySelector('a[data-page="pages/valve.php"]');
        if (valveLink) {
            valveLink.classList.add('active-menu-item');
        }
    }
    
    // 处理筛选功能
    $('#filterForm select').change(function() {
        $('#filterForm').submit();
    });
    
    // 筛选区域的折叠/展开
    $('#filterPanel').on('shown.bs.collapse', function () {
        $('#filterPanel').find('.btn-sm .fas').removeClass('fa-chevron-down').addClass('fa-chevron-up');
    });
    
    $('#filterPanel').on('hidden.bs.collapse', function () {
        $('#filterPanel').find('.btn-sm .fas').removeClass('fa-chevron-up').addClass('fa-chevron-down');
    });
    
    // 处理每页显示数量的变化
    $('.page-size-selector').change(function() {
        window.location.href = 'valve.php?page=1&limit=' + $(this).val() + 
        <?= "'" . 
            (!empty($search) ? '&search=' . urlencode($search) : '') .
            '&sort=' . $sort . '&order=' . $order .
            (!empty($filter_platform_name) ? '&filter_platform_name=' . urlencode($filter_platform_name) : '') .
            (!empty($filter_location) ? '&filter_location=' . urlencode($filter_location) : '') .
            (!empty($filter_valve_type) ? '&filter_valve_type=' . urlencode($filter_valve_type) : '') .
            (!empty($filter_flow_type) ? '&filter_flow_type=' . urlencode($filter_flow_type) : '') .
            (!empty($filter_valve_size) ? '&filter_valve_size=' . urlencode($filter_valve_size) : '') .
            (!empty($filter_flange_type) ? '&filter_flange_type=' . urlencode($filter_flange_type) : '') .
        "'" ?>;
    });
    
    // 动态生成阀门编号
    function generateValveCode() {
        const form = $('#dataForm');
        
        // 收集表单字段值
        const serialNumber = form.find('input[name="serial_number"]').val();
        const location = form.find('input[name="location"]').val();
        const functionDesc = form.find('textarea[name="function_desc"]').val();
        const valveType = form.find('input[name="valve_type"]').val();
        const flowType = form.find('input[name="flow_type"]').val();
        const valveSize = form.find('input[name="valve_size"]').val();
        const flangeType = form.find('input[name="flange_type"]').val();
        const flangeHoles = form.find('input[name="flange_holes"]').val();
        const sealType = form.find('input[name="seal_type"]').val();
        const gasketType = form.find('input[name="gasket_type"]').val();
        const valveName = form.find('input[name="valve_name"]').val();
        
        // 按照公式: =A5&"#-"&E5&"-"&F5&"-"&G5&"-"&H5&"-"&I5&"("&LEFT(...)&")"
        
        // 构建前半部分：序号#-位置-功能-阀门类型-流程类型-阀门尺寸
        let firstPart = serialNumber + "#-";
        if (location) firstPart += location + "-";
        if (functionDesc) firstPart += functionDesc + "-";
        if (valveType) firstPart += valveType + "-";
        if (flowType) firstPart += flowType + "-";
        if (valveSize) firstPart += valveSize;
        
        // 移除末尾多余的连字符
        firstPart = firstPart.replace(/-+$/, "");
        
        // 处理括号部分
        const bracketParts = [];
        if (flangeType) bracketParts.push(flangeType);
        if (flangeHoles) bracketParts.push(flangeHoles);
        if (sealType) bracketParts.push(sealType);
        if (gasketType) bracketParts.push(gasketType);
        
        // 只有在有括号内容时才添加括号部分
        let bracketPart = "";
        if (bracketParts.length > 0) {
            bracketPart = "(" + bracketParts.join("-") + ")";
        }
        
        // 组合成最终的阀门编号
        let valveCode = firstPart + bracketPart;
        
        // 如果编号只有序号加#-，或者为空，则使用阀门名称
        if (valveCode === serialNumber + "#-" || !valveCode) {
            valveCode = valveName;
        }
        
        // 更新阀门编号字段
        form.find('input[name="valve_code"]').val(valveCode);
        
        console.log("生成的阀门编号:", valveCode);
    }
    
    // 监听表单字段变化，动态更新阀门编号
    $('#dataForm').on('input', '.valve-field', function() {
        generateValveCode();
    });
    
    // 添加阀门按钮点击事件 - 打开添加模态框
    $('#addValveBtn').click(function() {
        const form = document.getElementById('dataForm');
        form.reset();
        
        // 清空ID字段（确保是添加而非编辑）
        $(form).find('input[name="id"]').val('');
        
        // 更新模态框标题
        $('#addModal').find('.modal-title').html('<i class="fas fa-plus-circle mr-2"></i>添加阀门');
        
        // 自动生成阀门序号（当前时间戳+3位随机数）
        const timestamp = new Date().getTime();
        const randomNum = Math.floor(Math.random() * 900) + 100; // 100-999之间的随机数
        const serialNumber = timestamp.toString() + randomNum.toString();
        $(form).find('input[name="serial_number"]').val(serialNumber);
        
        // 生成阀门编号
        generateValveCode();
        
        // 显示模态框
        $('#addModal').modal('show');
    });
    
    // 移除旧的模态框事件处理
    $('#addModal').off('show.bs.modal');
    
    // 模态框显示后的事件 - 仅用于调整界面，不处理数据
    $('#addModal').on('shown.bs.modal', function() {
        // 确保打开模态框后表单元素能够获取焦点
        $(this).find('input:visible:first').focus();
    });
    
    // 显示状态弹窗
    function showStatusModal(title, message, type = 'success') {
        const modal = $('<div class="modal fade" tabindex="-1" role="dialog">');
        const modalDialog = $('<div class="modal-dialog modal-lg" role="document">');
        const modalContent = $('<div class="modal-content">');
        
        // 设置标题颜色
        const headerClass = type === 'success' ? 'bg-success' : 'bg-danger';
        const header = $(`<div class="modal-header ${headerClass} text-white">`);
        header.append(`<h5 class="modal-title">${title}</h5>`);
        header.append('<button type="button" class="close text-white" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>');
        
        const body = $('<div class="modal-body">');
        
        // 错误消息处理，添加换行和格式化
        if (type === 'error' && message.length > 100) {
            const preElement = $('<pre class="error-message p-3 bg-light">').text(message);
            body.append(preElement);
        } else {
            body.append(`<p>${message}</p>`);
        }
        
        if (type === 'error') {
            body.append('<div class="alert alert-warning mt-3"><strong>导入提示：</strong><ol>' +
                '<li>确保Excel文件格式正确，且A、B、C、D列已填写必要信息</li>' +
                '<li>检查从第5行开始是否有有效数据</li>' +
                '<li>确保Excel文件未被其他程序占用</li>' +
                '<li>如果文件较大，可能需要增加服务器超时时间</li>' +
                '<li>可查看浏览器控制台(F12)了解更多错误详情</li>' +
                '</ol></div>');
        }
        
        const footer = $('<div class="modal-footer">');
        footer.append('<button type="button" class="btn btn-secondary" data-dismiss="modal">关闭</button>');
        
        modalContent.append(header);
        modalContent.append(body);
        modalContent.append(footer);
        modalDialog.append(modalContent);
        modal.append(modalDialog);
        
        $('body').append(modal);
        modal.modal('show');
        
        // 在关闭时移除模态框
        modal.on('hidden.bs.modal', function() {
            $(this).remove();
            if (type === 'success') {
                window.location.href = window.location.href.split('?')[0] + '?t=' + new Date().getTime();
            }
        });
    }
    
    // 编辑按钮点击事件 - 完全重写版本
    $(document).on('click', '.btn-edit', function() {
        // 获取所有数据属性
        const $btn = $(this);
        const id = $btn.attr('data-id');
        const platformName = $btn.attr('data-platform-name');
        const valveName = $btn.attr('data-valve-name');
        const serialNumber = $btn.attr('data-serial-number');
        const valveCode = $btn.attr('data-valve-code');
        const location = $btn.attr('data-location');
        const functionDesc = $btn.attr('data-function-desc');
        const valveType = $btn.attr('data-valve-type');
        const flowType = $btn.attr('data-flow-type');
        const valveSize = $btn.attr('data-valve-size');
        const flangeType = $btn.attr('data-flange-type');
        const flangeHoles = $btn.attr('data-flange-holes');
        const sealType = $btn.attr('data-seal-type');
        const gasketType = $btn.attr('data-gasket-type');
        const operationDate = $btn.attr('data-operation-date');
        const remark = $btn.attr('data-remark');

        // 获取模态框
        const modal = $('#addModal');
        
        // 更新模态框标题
        modal.find('.modal-title').html('<i class="fas fa-edit mr-2"></i>编辑阀门');
        
        // 设置表单值
        modal.find('input[name="id"]').val(id);
        modal.find('input[name="platform_name"]').val(platformName);
        modal.find('input[name="valve_name"]').val(valveName);
        modal.find('input[name="serial_number"]').val(serialNumber);
        modal.find('input[name="valve_code"]').val(valveCode);
        modal.find('input[name="location"]').val(location);
        modal.find('textarea[name="function_desc"]').val(functionDesc);
        modal.find('input[name="valve_type"]').val(valveType);
        modal.find('input[name="flow_type"]').val(flowType);
        modal.find('input[name="valve_size"]').val(valveSize);
        modal.find('input[name="flange_type"]').val(flangeType);
        modal.find('input[name="flange_holes"]').val(flangeHoles);
        modal.find('input[name="seal_type"]').val(sealType);
        modal.find('input[name="gasket_type"]').val(gasketType);
        modal.find('input[name="operation_date"]').val(operationDate);
        modal.find('textarea[name="remark"]').val(remark);
        
        // 显示模态框
        modal.modal('show');
    });
    
    // 表单提交
    $('#dataForm').submit(function(e) {
        e.preventDefault();
        const formData = $(this).serialize();
        
        $.ajax({
            url: 'valve.php',
            method: 'POST',
            data: formData,
            dataType: 'json',
            success: function(response) {
                if (response.status === 'success') {
                    // 显示成功提示，并刷新页面
                    showStatusModal('操作成功', response.message || '操作成功！');
                } else {
                    // 显示错误提示
                    showStatusModal('操作失败', response.message || '未知错误', 'error');
                }
            },
            error: function(xhr) {
                let errorMsg = '操作失败：';
                try {
                    const response = JSON.parse(xhr.responseText);
                    errorMsg += response.message || xhr.statusText;
                } catch (e) {
                    errorMsg += xhr.statusText;
                }
                showStatusModal('操作失败', errorMsg, 'error');
            }
        });
    });
    
    // 导入表单提交
    $('#importForm').submit(function(e) {
        e.preventDefault();
        
        // 验证文件类型
        const fileInput = document.getElementById('excelFile');
        if (fileInput.files.length === 0) {
            showStatusModal('上传错误', '请选择要导入的Excel文件', 'error');
            return;
        }
        
        const fileName = fileInput.files[0].name;
        const fileExt = fileName.substring(fileName.lastIndexOf('.')).toLowerCase();
        if (fileExt !== '.xlsx' && fileExt !== '.xls') {
            showStatusModal('上传错误', '请上传Excel文件(.xlsx或.xls)', 'error');
            return;
        }
        
        // 显示正在导入提示
        const submitBtn = $(this).find('button[type="submit"]');
        const originalText = submitBtn.html();
        submitBtn.html('<i class="fas fa-spinner fa-spin mr-1"></i> 导入中...').prop('disabled', true);
        
        // 显示进度条
        const progressBar = $(this).find('.progress');
        progressBar.removeClass('d-none');
        
        // 提交表单
        const formData = new FormData(this);
        
        $.ajax({
            url: 'valve.php',
            method: 'POST',
            data: formData,
            contentType: false,
            processData: false,
            dataType: 'json',
            timeout: 300000, // 5分钟超时
            success: function(response) {
                if (response.status === 'success') {
                    // 显示成功提示，并刷新页面
                    showStatusModal('导入成功', response.message || '导入成功！');
                    // 模态框关闭后会自动刷新页面
                } else {
                    // 显示错误提示
                    showStatusModal('导入失败', response.message || '未知错误', 'error');
                    // 隐藏进度条
                    progressBar.addClass('d-none');
                }
            },
            error: function(xhr, status, error) {
                let errorMsg = '';
                
                if (status === 'timeout') {
                    errorMsg = '请求超时，文件可能太大或服务器处理时间过长';
                } else {
                    try {
                        const response = JSON.parse(xhr.responseText);
                        errorMsg = response.message || xhr.statusText;
                    } catch (e) {
                        if (xhr.status === 500) {
                            errorMsg = '服务器内部错误。可能是内存不足或文件格式有误。';
                        } else {
                            errorMsg = xhr.statusText || '未知错误';
                        }
                    }
                }
                
                showStatusModal('导入失败', errorMsg, 'error');
                console.error('导入错误:', xhr);
                
                // 隐藏进度条
                progressBar.addClass('d-none');
            },
            complete: function() {
                // 恢复按钮状态
                submitBtn.html(originalText).prop('disabled', false);
            }
        });
    });
    
    // 删除操作 - 使用事件委托
    $(document).on('click', '.btn-delete', function() {
        const id = $(this).data('id');
        
        if (confirm('确定要删除此阀门记录吗？此操作不可恢复！')) {
            $.ajax({
                url: 'valve.php?delete=' + id,
                method: 'GET',
                dataType: 'json',
                success: function(response) {
                    if (response.status === 'success') {
                        showStatusModal('删除成功', '阀门记录已成功删除');
                    } else {
                        showStatusModal('删除失败', response.message || '未知错误', 'error');
                    }
                },
                error: function() {
                    showStatusModal('删除失败', '请稍后再试', 'error');
                }
            });
        }
    });
    
    // 美化文件上传
    $('#excelFile').on('change', function() {
        const fileName = this.files[0]?.name;
        if (fileName) {
            $(this).next('p').text(fileName);
        } else {
            $(this).next('p').text('点击或拖拽Excel文件至此处');
        }
    });
    
    // 全部删除按钮事件
    $('#deleteAllBtn').click(function() {
        if (confirm('您确定要删除所有阀门记录吗？此操作不可恢复！')) {
            // 二次确认
            if (confirm('再次确认：删除后所有数据将无法恢复，确定要继续吗？')) {
                $.ajax({
                    url: 'valve.php?deleteAll=1',
                    method: 'GET',
                    dataType: 'json',
                    success: function(response) {
                        if (response.status === 'success') {
                            showStatusModal('删除成功', '所有阀门记录已成功删除');
                        } else {
                            showStatusModal('删除失败', response.message || '未知错误', 'error');
                        }
                    },
                    error: function(xhr) {
                        let errorMsg = '删除失败：';
                        try {
                            const response = JSON.parse(xhr.responseText);
                            errorMsg += response.message || xhr.statusText;
                        } catch (e) {
                            errorMsg += xhr.statusText || '未知错误';
                        }
                        showStatusModal('删除失败', errorMsg, 'error');
                    }
                });
            }
        }
    });

    // 阀门编号点击显示完整内容
    let popup = $('<div class="popup-content"></div>').appendTo('body');
    
    $(document).on('click', '.valve-code-cell', function(e) {
        const code = $(this).attr('data-valve-code');
        popup.text(code);
        
        // 计算弹出窗口位置
        const rect = this.getBoundingClientRect();
        const scrollTop = $(window).scrollTop();
        const scrollLeft = $(window).scrollLeft();
        
        popup.css({
            top: rect.bottom + scrollTop + 5 + 'px',
            left: rect.left + scrollLeft + 'px',
            display: 'block'
        });
        
        e.stopPropagation();
    });
    
    // 点击其他区域关闭弹窗
    $(document).on('click', function() {
        popup.hide();
    });
});
</script>
</body>
</html> 