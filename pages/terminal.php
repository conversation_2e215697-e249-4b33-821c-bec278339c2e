<?php
require '../includes/config.php';
requireRole(['admin','manager']);

// 正确引入 PhpSpreadsheet
require_once __DIR__.'/../../vendor/autoload.php';
use PhpOffice\PhpSpreadsheet\IOFactory;

// 处理Excel导入
if(isset($_POST['import_type']) && isset($_FILES['excel'])){
    try {
        $pdo->beginTransaction();
        $spreadsheet = IOFactory::load($_FILES['excel']['tmp_name']);
        $sheet = $spreadsheet->getActiveSheet();
        $importCount = 0;
        $importType = $_POST['import_type'];

        foreach ($sheet->getRowIterator(2) as $row) {
            $rowIndex = $row->getRowIndex();
            
            $data = [
                'parent' => trim($sheet->getCell('B'.$rowIndex)->getValue()),
                'code'   => trim($sheet->getCell('C'.$rowIndex)->getValue()),
                'cable'  => trim($sheet->getCell('D'.$rowIndex)->getValue()),
                'remark' => trim($sheet->getCell('E'.$rowIndex)->getValue())
            ];

            if (empty($data['parent']) || empty($data['code'])) {
                throw new Exception("第{$rowIndex}行数据不完整");
            }

            if ($importType == 'rcp') {
                // RCP柜处理逻辑
                $stmt = $pdo->prepare("SELECT id FROM rcp_cabinet WHERE name = ?");
                $stmt->execute([$data['parent']]);
                if (!$parentId = $stmt->fetchColumn()) {
                    throw new Exception("第{$rowIndex}行RCP柜不存在");
                }
                
                $stmt = $pdo->prepare("INSERT INTO terminal 
                    (rcp_id, code, cable_name, remark)
                    VALUES (?, ?, ?, ?)
                    ON DUPLICATE KEY UPDATE 
                        cable_name=VALUES(cable_name),
                        remark=VALUES(remark)");
                $stmt->execute([$parentId, $data['code'], $data['cable'], $data['remark']]);
            } else {
                // 分线箱处理逻辑
                $stmt = $pdo->prepare("SELECT id, rcp_id FROM junction_box WHERE name = ?");
                $stmt->execute([$data['parent']]);
                if (!$junctionBox = $stmt->fetch()) {
                    throw new Exception("第{$rowIndex}行分线箱不存在");
                }
                
                if (empty($junctionBox['rcp_id'])) {
                    throw new Exception("第{$rowIndex}行分线箱未关联RCP柜");
                }

                // 关键修复：同时写入 junction_id 和 rcp_id
                $stmt = $pdo->prepare("INSERT INTO terminal 
                    (junction_id, rcp_id, code, cable_name, remark)
                    VALUES (?, ?, ?, ?, ?)
                    ON DUPLICATE KEY UPDATE 
                        cable_name=VALUES(cable_name),
                        remark=VALUES(remark)");
                $stmt->execute([
                    $junctionBox['id'],    // junction_id
                    $junctionBox['rcp_id'],// rcp_id
                    $data['code'],
                    $data['cable'],
                    $data['remark']
                ]);
            }
            $importCount++;
        }

        $pdo->commit();
        echo json_encode(['status' => 'success', 'message' => "成功导入{$importCount}条数据"]);
    } catch (Exception $e) {
        $pdo->rollBack();
        http_response_code(500);
        echo json_encode(['status' => 'error', 'message' => $e->getMessage()]);
    }
    exit();
}

// 处理添加/编辑请求
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $input = $_POST;

    try {
        $required = ['code', 'parent_id', 'type'];
        foreach ($required as $field) {
            if (empty($input[$field])) throw new Exception("必填字段缺失");
        }

        $checkSql = "SELECT id FROM terminal WHERE code = ? AND ".($input['type'] == 'rcp' ? 'rcp_id' : 'junction_id')." = ?";
        $params = [$input['code'], $input['parent_id']];
        if (!empty($input['id'])) {
            $checkSql .= " AND id != ?";
            $params[] = $input['id'];
        }
        $stmt = $pdo->prepare($checkSql);
        $stmt->execute($params);
        if ($stmt->fetch()) throw new Exception("位号已存在");

        $data = [
            'rcp_id' => ($input['type'] == 'rcp') ? $input['parent_id'] : null,
            'junction_id' => ($input['type'] == 'junction') ? $input['parent_id'] : null,
            'code' => $input['code'],
            'cable_name' => $input['cable_name'] ?? '',
            'remark' => $input['remark'] ?? ''
        ];

        if (!empty($input['id'])) {
            $stmt = $pdo->prepare("UPDATE terminal SET 
                rcp_id=?, junction_id=?, code=?, cable_name=?, remark=?
                WHERE id=?");
            $data[] = $input['id'];
        } else {
            $stmt = $pdo->prepare("INSERT INTO terminal 
                (rcp_id, junction_id, code, cable_name, remark)
                VALUES (?, ?, ?, ?, ?)");
        }

        $stmt->execute(array_values($data));
        echo json_encode(['status' => 'success']);
    } catch (Exception $e) {
        http_response_code(400);
        echo json_encode(['status' => 'error', 'message' => $e->getMessage()]);
    }
    exit();
}

// 处理删除请求
if(isset($_GET['delete'])) {
    try {
        $stmt = $pdo->prepare("DELETE FROM terminal WHERE id = ?");
        $stmt->execute([$_GET['delete']]);
        echo json_encode(['status' => 'success']);
    } catch (Exception $e) {
        echo json_encode(['status' => 'error', 'message' => $e->getMessage()]);
    }
    exit();
}

// 分页处理 - 优化分页
$page = isset($_GET['page']) ? intval($_GET['page']) : 1;
$limit = isset($_GET['limit']) ? intval($_GET['limit']) : 20; // 默认每页20条
$offset = ($page - 1) * $limit;

// 搜索处理
$search = isset($_GET['search']) ? $_GET['search'] : '';
$searchConditions = [];
$params = [];

if ($search) {
    // 分词处理，按空格分割搜索词
    $keywords = preg_split('/\s+/', trim($search));
    
    foreach ($keywords as $keyword) {
        if (empty($keyword)) continue;
        
        // 为每个关键词创建一个条件组
        $keywordCondition = "(t.code LIKE ? OR t.cable_name LIKE ? OR t.remark LIKE ? OR r.name LIKE ? OR j.name LIKE ? OR p.name LIKE ?)";
        $searchConditions[] = $keywordCondition;
        
        // 为每个字段添加该关键词的参数
        $keywordParam = "%{$keyword}%";
        $params = array_merge($params, array_fill(0, 6, $keywordParam));
    }
}

// 如果有搜索条件，将它们用AND连接
$searchWhere = count($searchConditions) > 0 ? '(' . implode(' AND ', $searchConditions) . ')' : '1';

// 获取总记录数
$totalStmt = $pdo->prepare("
    SELECT COUNT(*) 
    FROM terminal t
    LEFT JOIN rcp_cabinet r ON t.rcp_id = r.id 
    LEFT JOIN junction_box j ON t.junction_id = j.id 
    LEFT JOIN power_cabinet p ON r.power_id = p.id 
    WHERE " . $searchWhere
);
$totalStmt->execute($params);
$total = $totalStmt->fetchColumn();

// 修改查询以按RCP柜ID和分线箱ID排序
$sql = "
    SELECT t.*, 
    r.name AS rcp_name, r.id AS rcp_id,
    j.name AS junction_name, j.id AS junction_id,
    p.name AS power_name, p.id AS power_id
    FROM terminal t
    LEFT JOIN rcp_cabinet r ON t.rcp_id = r.id 
    LEFT JOIN junction_box j ON t.junction_id = j.id 
    LEFT JOIN power_cabinet p ON r.power_id = p.id 
    WHERE " . $searchWhere . "
    ORDER BY r.id ASC, CASE WHEN j.id IS NULL THEN 1 ELSE 0 END, j.id ASC, t.id ASC
    LIMIT ? OFFSET ?
";

$stmt = $pdo->prepare($sql);
$stmt->execute(array_merge($params, [$limit, $offset]));
$terminals = $stmt->fetchAll();

$rcpCabinets = $pdo->query("SELECT * FROM rcp_cabinet")->fetchAll();
$junctions = $pdo->query("SELECT * FROM junction_box")->fetchAll();

// 计算总页数
$totalPages = ceil($total / $limit);
?>

<!DOCTYPE html>
<html>
<head>
    <title>位号管理</title>
    <link rel="stylesheet" href="../assets/css/bootstrap.min.css">
    <link href="../assets/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/apple-style.css" rel="stylesheet">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <style>
        /* 表格文字居中 */
        .table th, .table td {
            text-align: center;
            vertical-align: middle;
            padding: 0.5rem;
            font-size: 0.9rem;
        }
        
        /* 搜索框样式 */
        .search-box {
            margin-bottom: 15px;
        }
        
        /* 分页样式 */
        .pagination {
            margin-top: 15px;
        }
        
        /* 每页显示条数选择器 */
        .page-size-selector {
            margin-left: 10px;
        }
        
        /* RCP柜颜色区分 */
        .rcp-group-even {
            background-color: rgba(240, 248, 255, 0.5); /* 浅蓝色 */
        }
        
        .rcp-group-odd {
            background-color: rgba(255, 248, 240, 0.5); /* 浅橙色 */
        }
    </style>
</head>
<body>
<div class="container-fluid px-4">
    <!-- 页面标题 -->
    <div class="page-header">
        <h3><i class="fas fa-list-ol mr-2"></i>位号管理</h3>
        <div class="btn-group">
            <button class="btn btn-primary" data-type="rcp" data-toggle="modal" data-target="#addModal">
                <i class="fas fa-plus mr-1"></i> 添加RCP柜位号
            </button>
            <button class="btn btn-info" data-type="junction" data-toggle="modal" data-target="#addModal">
                <i class="fas fa-plus mr-1"></i> 添加分线箱位号
            </button>
            <div class="btn-group">
                <button type="button" class="btn btn-warning dropdown-toggle" data-toggle="dropdown">
                    <i class="fas fa-file-import mr-1"></i> 导入数据
                </button>
                <div class="dropdown-menu">
                    <a class="dropdown-item" href="#" data-type="rcp" data-toggle="modal" data-target="#importModal">
                        <i class="fas fa-server mr-1"></i> RCP柜位号导入
                    </a>
                    <a class="dropdown-item" href="#" data-type="junction" data-toggle="modal" data-target="#importModal">
                        <i class="fas fa-box mr-1"></i> 分线箱位号导入
                    </a>
                </div>
            </div>
            <div class="btn-group">
                <button type="button" class="btn btn-success dropdown-toggle" data-toggle="dropdown">
                    <i class="fas fa-download mr-1"></i> 下载模板
                </button>
                <div class="dropdown-menu">
                    <a class="dropdown-item" href="../assets/templates/terminal_rcp_template.xlsx">
                        <i class="fas fa-server mr-1"></i> RCP柜模板
                    </a>
                    <a class="dropdown-item" href="../assets/templates/terminal_junction_template.xlsx">
                        <i class="fas fa-box mr-1"></i> 分线箱模板
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-3">
        <div class="col-md-6">
        </div>
        <div class="col-md-6">
            <form class="form-inline justify-content-end" method="get">
                <small class="form-text text-muted mr-2">提示：可使用空格分隔多个关键词进行组合搜索，如"高频 压力"</small>
                <div class="input-group">
                    <input type="text" class="form-control" name="search" placeholder="搜索位号、RCP柜、分线箱、线缆名称..." value="<?= htmlspecialchars($search) ?>">
                    <div class="input-group-append">
                        <button class="btn btn-outline-secondary" type="submit">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <table class="table table-bordered table-hover">
        <thead class="thead-light">
            <tr>
                <th>序号</th>
                <th>所属电源柜</th>
                <th>所属RCP柜</th>
                <th>所属分线箱</th>
                <th>位号</th>
                <th>线缆名称</th>
                <th>备注</th>
                <th>操作</th>
            </tr>
        </thead>
        <tbody>
            <?php 
            $index = ($page - 1) * $limit + 1;
            $currentPowerId = null;
            $currentRcpId = null;
            $currentJunctionId = null;
            $powerRowspan = 0;
            $rcpRowspan = 0;
            $junctionRowspan = 0;
            $rcpGroupCount = 0; // 用于跟踪RCP柜组的计数
            
            // 预处理计算每个电源柜和RCP柜的行数
            $powerCounts = [];
            $rcpCounts = [];
            $junctionCounts = [];
            
            foreach($terminals as $term) {
                // 计算电源柜行数
                if (!isset($powerCounts[$term['power_id']])) {
                    $powerCounts[$term['power_id']] = 0;
                }
                $powerCounts[$term['power_id']]++;
                
                // 计算RCP柜行数
                if (!isset($rcpCounts[$term['rcp_id']])) {
                    $rcpCounts[$term['rcp_id']] = 0;
                }
                $rcpCounts[$term['rcp_id']]++;
                
                // 计算分线箱行数
                if ($term['junction_id'] && !isset($junctionCounts[$term['junction_id']])) {
                    $junctionCounts[$term['junction_id']] = 0;
                }
                if ($term['junction_id']) {
                    $junctionCounts[$term['junction_id']]++;
                }
            }
            
            foreach($terminals as $term): 
                $showPower = false;
                $showRcp = false;
                $showJunction = false;
                
                // 检查是否需要显示电源柜
                if ($currentPowerId !== $term['power_id']) {
                    $currentPowerId = $term['power_id'];
                    $powerRowspan = $powerCounts[$currentPowerId];
                    $showPower = true;
                }
                
                // 检查是否需要显示RCP柜
                if ($currentRcpId !== $term['rcp_id']) {
                    $currentRcpId = $term['rcp_id'];
                    $rcpRowspan = $rcpCounts[$currentRcpId];
                    $showRcp = true;
                    $rcpGroupCount++; // 每次RCP柜变化时增加计数
                }
                
                // 检查是否需要显示分线箱
                if ($term['junction_id'] && $currentJunctionId !== $term['junction_id']) {
                    $currentJunctionId = $term['junction_id'];
                    $junctionRowspan = $junctionCounts[$currentJunctionId];
                    $showJunction = true;
                } else if (!$term['junction_id']) {
                    $currentJunctionId = null;
                    $showJunction = true;
                }
                
                // 确定行的CSS类，基于RCP柜组计数
                $rowClass = ($rcpGroupCount % 2 == 0) ? 'rcp-group-even' : 'rcp-group-odd';
            ?>
            <tr data-id="<?= $term['id'] ?>" class="<?= $rowClass ?>">
                <td><?= $index++ ?></td>
                
                <?php if ($showPower): ?>
                <td rowspan="<?= $powerRowspan ?>"><?= htmlspecialchars($term['power_name'] ?? '') ?></td>
                <?php endif; ?>
                
                <?php if ($showRcp): ?>
                <td rowspan="<?= $rcpRowspan ?>"><?= htmlspecialchars($term['rcp_name'] ?? '') ?></td>
                <?php endif; ?>
                
                <?php if ($showJunction): ?>
                <td <?= $term['junction_id'] ? "rowspan=\"{$junctionRowspan}\"" : "" ?>><?= htmlspecialchars($term['junction_name'] ?? '') ?></td>
                <?php endif; ?>
                
                <td><?= htmlspecialchars($term['code'] ?? '') ?></td>
                <td><?= htmlspecialchars($term['cable_name'] ?? '') ?></td>
                <td><?= htmlspecialchars($term['remark'] ?? '') ?></td>
                <td>
                    <button class="btn btn-sm btn-warning btn-edit"
                            data-id="<?= $term['id'] ?>"
                            data-code="<?= htmlspecialchars($term['code'] ?? '') ?>"
                            data-cable-name="<?= htmlspecialchars($term['cable_name'] ?? '') ?>"
                            data-remark="<?= htmlspecialchars($term['remark'] ?? '') ?>"
                            data-type="<?= $term['junction_id'] ? 'junction' : 'rcp' ?>"
                            data-parent-id="<?= $term['junction_id'] ? $term['junction_id'] : $term['rcp_id'] ?>">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-sm btn-danger btn-delete" data-id="<?= $term['id'] ?>">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            </tr>
            <?php endforeach; ?>
            
            <?php if(count($terminals) == 0): ?>
            <tr>
                <td colspan="8" class="text-center">没有找到匹配的记录</td>
            </tr>
            <?php endif; ?>
        </tbody>
    </table>

    <!-- 优化的分页 -->
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <span>共 <?= $total ?> 条记录，当前 <?= $page ?>/<?= $totalPages ?> 页</span>
        </div>
        
        <div>
            <nav aria-label="Page navigation">
                <ul class="pagination justify-content-center mb-0">
                    <li class="page-item <?= ($page <= 1) ? 'disabled' : '' ?>">
                        <a class="page-link" href="?page=1&limit=<?= $limit ?>&search=<?= urlencode($search) ?>">首页</a>
                    </li>
                    <li class="page-item <?= ($page <= 1) ? 'disabled' : '' ?>">
                        <a class="page-link" href="?page=<?= $page-1 ?>&limit=<?= $limit ?>&search=<?= urlencode($search) ?>">上一页</a>
                    </li>
                    
                    <?php 
                    // 显示附近的页码
                    $startPage = max(1, $page - 2);
                    $endPage = min($totalPages, $page + 2);
                    
                    for ($i = $startPage; $i <= $endPage; $i++): 
                    ?>
                    <li class="page-item <?= ($page == $i) ? 'active' : '' ?>">
                        <a class="page-link" href="?page=<?= $i ?>&limit=<?= $limit ?>&search=<?= urlencode($search) ?>"><?= $i ?></a>
                    </li>
                    <?php endfor; ?>
                    
                    <li class="page-item <?= ($page >= $totalPages) ? 'disabled' : '' ?>">
                        <a class="page-link" href="?page=<?= $page+1 ?>&limit=<?= $limit ?>&search=<?= urlencode($search) ?>">下一页</a>
                    </li>
                    <li class="page-item <?= ($page >= $totalPages) ? 'disabled' : '' ?>">
                        <a class="page-link" href="?page=<?= $totalPages ?>&limit=<?= $limit ?>&search=<?= urlencode($search) ?>">末页</a>
                    </li>
                </ul>
            </nav>
        </div>
        
        <div>
            <form class="form-inline" method="get">
                <input type="hidden" name="search" value="<?= htmlspecialchars($search) ?>">
                <div class="form-group mb-0">
                    <label class="mr-2">每页显示</label>
                    <select name="limit" class="form-control form-control-sm" onchange="this.form.submit()">
                        <option value="10" <?= $limit == 10 ? 'selected' : '' ?>>10</option>
                        <option value="20" <?= $limit == 20 ? 'selected' : '' ?>>20</option>
                        <option value="50" <?= $limit == 50 ? 'selected' : '' ?>>50</option>
                        <option value="100" <?= $limit == 100 ? 'selected' : '' ?>>100</option>
                    </select>
                    <span class="ml-1">条</span>
                </div>
                <div class="form-group ml-2 mb-0">
                    <label class="mr-2">跳转到</label>
                    <input type="number" name="page" class="form-control form-control-sm" min="1" max="<?= $totalPages ?>" value="<?= $page ?>" style="width: 60px;">
                    <button type="submit" class="btn btn-sm btn-outline-secondary ml-1">GO</button>
                </div>
            </form>
        </div>
    </div>

    <!-- 添加/编辑模态框 -->
    <div class="modal fade" id="addModal">
        <div class="modal-dialog">
            <div class="modal-content">
                <form id="dataForm">
                    <input type="hidden" name="type" id="inputType">
                    <input type="hidden" name="id">
                    <div class="modal-header">
                        <h5 class="modal-title"></h5>
                        <button type="button" class="close" data-dismiss="modal">&times;</button>
                    </div>
                    <div class="modal-body">
                        <div class="form-group">
                            <label id="parentLabel"></label>
                            <select name="parent_id" class="form-control" required></select>
                        </div>
                        <div class="form-group">
                            <label>位号</label>
                            <input type="text" name="code" class="form-control" required>
                        </div>
                        <div class="form-group">
                            <label>线缆名称</label>
                            <input type="text" name="cable_name" class="form-control">
                        </div>
                        <div class="form-group">
                            <label>备注</label>
                            <textarea name="remark" class="form-control" rows="3"></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                        <button type="submit" class="btn btn-primary">保存</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 导入模态框 -->
    <div class="modal fade" id="importModal">
        <div class="modal-dialog">
            <div class="modal-content">
                <form id="importForm" enctype="multipart/form-data">
                    <input type="hidden" name="import_type">
                    <div class="modal-header">
                        <h5 class="modal-title"></h5>
                        <button type="button" class="close" data-dismiss="modal">&times;</button>
                    </div>
                    <div class="modal-body">
                        <div class="form-group">
                            <label>选择Excel文件</label>
                            <input type="file" name="excel" class="form-control-file" required>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                        <button type="submit" class="btn btn-primary">开始导入</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script src="../assets/js/jquery.min.js"></script>
<script src="../assets/js/bootstrap.bundle.min.js"></script>
<script>
// 添加模态框初始化
$('#addModal').on('show.bs.modal', function(e) {
    const type = $(e.relatedTarget).data('type'); 
    const title = type === 'rcp' ? '添加RCP柜位号' : '添加分线箱位号';
    const options = type === 'rcp' ? <?= json_encode($rcpCabinets) ?> : <?= json_encode($junctions) ?>;
    
    $(this).find('.modal-title').text(title);
    $('#inputType').val(type);
    $('#parentLabel').text(type === 'rcp' ? '选择RCP柜' : '选择分线箱');
    
    const select = $(this).find('select[name="parent_id"]').empty();
    options.forEach(item => {
        select.append(new Option(item.name, item.id)); 
    });
    
    // 清空表单
    $(this).find('input[name="id"]').val('');
    $(this).find('input[name="code"]').val('');
    $(this).find('input[name="cable_name"]').val('');
    $(this).find('textarea[name="remark"]').val('');
});

// 进一步优化编辑按钮的点击事件处理函数
$('.btn-edit').click(function() {
    const id = $(this).data('id');
    const code = $(this).data('code');
    const cableName = $(this).data('cable-name');
    const remark = $(this).data('remark');
    const type = $(this).data('type');
    const parentId = $(this).data('parent-id');
    
    // 直接打开模态框，而不是通过点击其他按钮
    const modal = $('#addModal');
    
    // 设置模态框标题和类型
    modal.find('.modal-title').text(type === 'rcp' ? '编辑RCP柜位号' : '编辑分线箱位号');
    $('#inputType').val(type);
    $('#parentLabel').text(type === 'rcp' ? '选择RCP柜' : '选择分线箱');
    
    // 加载对应的选项列表
    const options = type === 'rcp' ? <?= json_encode($rcpCabinets) ?> : <?= json_encode($junctions) ?>;
    const select = modal.find('select[name="parent_id"]').empty();
    options.forEach(item => {
        select.append(new Option(item.name, item.id)); 
    });
    
    // 先显示模态框，确保DOM已经准备好
    modal.modal('show');
    
    // 在模态框显示后设置表单值
    modal.on('shown.bs.modal', function() {
        $(this).find('input[name="id"]').val(id);
        $(this).find('input[name="code"]').val(code);
        $(this).find('input[name="cable_name"]').val(cableName);
        $(this).find('textarea[name="remark"]').val(remark);
        $(this).find('select[name="parent_id"]').val(parentId);
        
        // 解绑事件，防止多次绑定
        $(this).off('shown.bs.modal');
    });
});

// 表单提交
$('#dataForm').submit(function(e) {
    e.preventDefault(); 
    const formData = $(this).serialize();
    
    $.ajax({
        url: 'terminal.php', 
        method: 'POST',
        data: formData,
        success: function() {
            window.location.reload(); 
        },
        error: function(xhr) {
            alert('操作失败: ' + (xhr.responseJSON?.message || xhr.statusText)); 
        }
    });
});

// 导入处理
$('#importModal').on('show.bs.modal', function(e) {
    const type = $(e.relatedTarget).data('type');
    $(this).find('.modal-title').text(type === 'rcp' ? '导入RCP柜位号' : '导入分线箱位号');
    $(this).find('[name="import_type"]').val(type);
});

$('#importForm').submit(function(e) {
    e.preventDefault(); 
    const formData = new FormData(this);
    
    $.ajax({
        url: 'terminal.php', 
        method: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        dataType: 'json',
        success: function(res) {
            alert(res.message); 
            if(res.status === 'success') window.location.reload(); 
        },
        error: function(xhr) {
            let errorMsg = '导入失败: ';
            try {
                errorMsg += xhr.responseJSON.message || xhr.statusText;
            } catch(e) {
                errorMsg += xhr.statusText;
            }
            alert(errorMsg);
        }
    });
});

// 删除操作
$('.btn-delete').click(function() {
    if (!confirm('确定删除？')) return;
    
    const id = $(this).data('id');
    $.get('terminal.php?delete=' + id, function() {
        window.location.reload(); 
    });
});
</script>
</body>
</html>