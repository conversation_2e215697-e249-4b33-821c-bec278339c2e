<?php
require_once '../includes/config.php';

// 确保$pdo变量可用
if (!isset($pdo) || !$pdo) {
    die('数据库连接失败：PDO对象未初始化');
}

// 检查是否登录
if(!isset($_SESSION['user'])) {
    header("Location: login.php");
    exit;
}

// 只允许管理员访问
if($_SESSION['user']['role'] != 'admin') {
    header("Location: dashboard.php");
    exit;
}

// 通用JSON响应函数
function sendJsonResponse($data) {
    ob_clean();
    header('Content-Type: application/json; charset=utf-8');
    header('Cache-Control: no-cache, must-revalidate');
    echo json_encode($data, JSON_UNESCAPED_UNICODE);
    exit;
}

// 处理AJAX请求保存参数
if(isset($_POST['save_params'])) {
    $_SESSION['jintie_params'] = [
        'workers_count' => intval($_POST['workers_count'] ?? 15),
        'jintie_total' => $_SESSION['jintie_params']['jintie_total'] ?? 10000,
        'distribution' => intval($_POST['distribution'] ?? 0)
    ];

    if(!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
        sendJsonResponse(['success' => true]);
    }
}

// 始终保持编辑模式
$isSavedMode = false;

// 检查津贴人员表是否存在，如果不存在则创建
try {
    $tableCheck = $pdo->query("SHOW TABLES LIKE 'jintie_personnel'");
    if ($tableCheck->rowCount() == 0) {
        // 创建津贴人员表
        $pdo->exec("CREATE TABLE jintie_personnel (
            id INT AUTO_INCREMENT PRIMARY KEY,
            dept VARCHAR(50) NOT NULL DEFAULT '生产',
            name VARCHAR(100) NOT NULL,
            fixed_amount DECIMAL(10,2) NOT NULL DEFAULT 0,
            floating_amount DECIMAL(10,2) NOT NULL DEFAULT 0,
            is_fixed_bonus TINYINT(1) NOT NULL DEFAULT 0,
            sort_order INT NOT NULL DEFAULT 0,
            is_visible TINYINT(1) NOT NULL DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4");
    } else {
        // 检查is_visible字段是否存在
        $columnCheck = $pdo->query("SHOW COLUMNS FROM jintie_personnel LIKE 'is_visible'");
        if ($columnCheck->rowCount() == 0) {
            // 添加is_visible字段
            $pdo->exec("ALTER TABLE jintie_personnel ADD COLUMN is_visible TINYINT(1) NOT NULL DEFAULT 1");
        }

        // 检查is_fixed_bonus字段是否存在
        $columnCheck = $pdo->query("SHOW COLUMNS FROM jintie_personnel LIKE 'is_fixed_bonus'");
        if ($columnCheck->rowCount() == 0) {
            // 添加is_fixed_bonus字段
            $pdo->exec("ALTER TABLE jintie_personnel ADD COLUMN is_fixed_bonus TINYINT(1) NOT NULL DEFAULT 0");
        }

        // 检查floating_ratio字段是否存在
        $columnCheck = $pdo->query("SHOW COLUMNS FROM jintie_personnel LIKE 'floating_ratio'");
        if ($columnCheck->rowCount() == 0) {
            // 添加floating_ratio字段，固定人员默认为0，非固定人员默认为1
            $pdo->exec("ALTER TABLE jintie_personnel ADD COLUMN floating_ratio DECIMAL(3,2) NOT NULL DEFAULT 1.0");
            // 更新现有固定人员的浮动比例为0
            $pdo->exec("UPDATE jintie_personnel SET floating_ratio = 0 WHERE is_fixed_bonus = 1");
        }
    }
} catch (PDOException $e) {
    // 创建表出错
}

// 检查岗位金额设置表是否存在，如果不存在则创建
try {
    $tableCheck = $pdo->query("SHOW TABLES LIKE 'jintie_dept_settings'");
    if ($tableCheck->rowCount() == 0) {
        // 创建岗位金额设置表
        $pdo->exec("CREATE TABLE jintie_dept_settings (
            id INT AUTO_INCREMENT PRIMARY KEY,
            jintie_total DECIMAL(10,2) NOT NULL DEFAULT 10000 COMMENT '津贴总额',
            monitoring_base_amount DECIMAL(10,2) NOT NULL DEFAULT 2500 COMMENT '监控岗位基础金额',
            cb26_base_amount DECIMAL(10,2) NOT NULL DEFAULT 400 COMMENT 'CB26岗位基础金额',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4");

        // 插入默认设置
        $pdo->exec("INSERT INTO jintie_dept_settings (jintie_total, monitoring_base_amount, cb26_base_amount) VALUES (10000, 2500, 400)");
    }
} catch (PDOException $e) {
    // 创建表出错
}

// 从SESSION获取参数，如果不存在则使用默认值
if(!isset($_SESSION['jintie_params'])) {
    $_SESSION['jintie_params'] = [
        'workers_count' => 15,
        'jintie_total' => 10000,
        'distribution' => 0,
        'record_date' => date('Y-m-d'),
        'record_month' => date('Y-m')
    ];
}



// 处理保存数据
if(isset($_POST['save_data'])) {
    // 从POST获取数据
    $depts = $_POST['dept'] ?? [];
    $names = $_POST['name'] ?? [];
    $fixeds = $_POST['fixed'] ?? [];
    $floatings = $_POST['floating'] ?? [];
    $days = $_POST['days'] ?? [];
    $fixedActuals = $_POST['fixed_actual'] ?? [];
    $floatingRatios = $_POST['floating_ratio'] ?? [];
    $deductions = $_POST['deduction'] ?? [];
    $personalBonuses = $_POST['personal_bonus'] ?? [];
    $generalBonuses = $_POST['general_bonus'] ?? [];
    $isFixedBonuses = $_POST['is_fixed_bonus'] ?? [];
    $floatingActuals = $_POST['floating_actual'] ?? []; // 应得浮动部分数据
    $totals = $_POST['total'] ?? []; // 总金额数据
    $remarks = $_POST['remark'] ?? []; // 备注数据
    
    // 处理JSON数据
    $jsonData = null;
    if(isset($_POST['employee_data_json'])) {
        $jsonData = json_decode($_POST['employee_data_json'], true);
    }
    
    // 获取基础参数
    $workDays = intval($_POST['workers_count'] ?? 15);
    $jintieTotal = intval($_POST['jintie_total'] ?? 10000);
    $distribution = intval($_POST['distribution'] ?? 0);
    $recordDate = $_POST['record_date'] ?? date('Y-m-d');
    $recordMonth = $_POST['record_month'] ?? date('Y-m');

    // 更新SESSION中的参数
    $_SESSION['jintie_params'] = [
        'workers_count' => $workDays,
        'jintie_total' => $jintieTotal,
        'distribution' => $distribution,
        'record_date' => $recordDate,
        'record_month' => $recordMonth
    ];
    
    // 处理员工数据，优先使用JSON数据中的值
    $employees = [];

    // 如果有JSON数据，优先使用JSON数据
    if ($jsonData && is_array($jsonData)) {
        foreach ($jsonData as $i => $employee) {
            if (!empty($employee['name'])) {
                $employees[] = [
                    'id' => $i + 1,
                    'dept' => $employee['dept'] ?? '生产',
                    'name' => $employee['name'],
                    'fixed' => floatval($employee['fixed'] ?? 0),
                    'floating' => floatval($employee['floating'] ?? 0),
                    'days' => intval($employee['days'] ?? 0),
                    'fixed_actual' => floatval($employee['fixed_actual'] ?? 0),
                    'floating_ratio' => floatval($employee['floating_ratio'] ?? 1),
                    'deduction' => intval($employee['deduction'] ?? 0),
                    'bonus' => intval(($employee['personal_bonus'] ?? 0) + ($employee['general_bonus'] ?? 0)),
                    'personal_bonus' => intval($employee['personal_bonus'] ?? 0),
                    'general_bonus' => intval($employee['general_bonus'] ?? 0),
                    'floating_actual' => floatval($employee['floating_actual'] ?? 0),
                    'total' => floatval($employee['total'] ?? 0),
                    'is_fixed_bonus' => ($employee['is_fixed_bonus'] ?? false) ? true : false,
                    'remark' => $employee['remark'] ?? ''
                ];
            }
        }
    } else {
        // 否则使用表单数组数据
        foreach($names as $i => $name) {
            if(!empty($name)) {
                $isFixedBonus = isset($isFixedBonuses[$i]) && $isFixedBonuses[$i] == "1";

                // 获取表单数据
                $dept = $depts[$i];
                $fixed = floatval($fixeds[$i] ?? 0);
                $floating = floatval($floatings[$i] ?? 0);
                $day = intval($days[$i] ?? 0);
                $fixedActual = floatval($fixedActuals[$i] ?? 0);
                $floatingActual = floatval($floatingActuals[$i] ?? 0);
                $total = floatval($totals[$i] ?? 0);
                $deduction = intval($deductions[$i] ?? 0);
                $personalBonus = intval($personalBonuses[$i] ?? 0);
                $generalBonus = intval($generalBonuses[$i] ?? 0);
                $bonus = $personalBonus + $generalBonus;
                $floatingRatio = $isFixedBonus ? 0 : (floatval($floatingRatios[$i] ?? 1));
                $remark = $remarks[$i] ?? ''; // 备注信息

                // 添加员工数据到数组
                $employees[] = [
                    'id' => $i + 1,
                    'dept' => $dept,
                    'name' => $name,
                    'fixed' => $fixed,
                    'floating' => $floating,
                    'days' => $day,
                    'fixed_actual' => $fixedActual,
                    'floating_ratio' => $floatingRatio,
                    'deduction' => $deduction,
                    'bonus' => $bonus,
                    'personal_bonus' => $personalBonus,
                    'general_bonus' => $generalBonus,
                    'floating_actual' => $floatingActual,
                    'total' => $total,
                    'is_fixed_bonus' => $isFixedBonus,
                    'remark' => $remark
                ];
            }
        }
    }
    
    // 保存员工数据到SESSION
    $_SESSION['jintie_employees'] = $employees;

    // 直接保存到数据库（不通过HTTP API调用）
    try {
        // 检查数据库连接
        if (!$pdo) {
            throw new Exception('数据库连接失败');
        }

        // 检查数据库表是否存在
        $checkTables = $pdo->query("SHOW TABLES LIKE 'jintie_records'");
        if ($checkTables->rowCount() == 0) {
            throw new Exception('数据库表 jintie_records 不存在，请先运行 init_database.php');
        }

        $checkParams = $pdo->query("SHOW TABLES LIKE 'jintie_params'");
        if ($checkParams->rowCount() == 0) {
            throw new Exception('数据库表 jintie_params 不存在，请先运行 init_database.php');
        }

        // 检查表结构是否包含remark字段
        $checkColumns = $pdo->query("SHOW COLUMNS FROM jintie_records LIKE 'remark'");

        $pdo->beginTransaction();

        // 计算月份
        $monthYear = date('Y-m', strtotime($recordDate));

        // 保存或更新参数
        $paramSql = "INSERT INTO jintie_params (record_date, workers_count, jintie_total, distribution)
                     VALUES (?, ?, ?, ?)
                     ON DUPLICATE KEY UPDATE
                     workers_count = VALUES(workers_count),
                     jintie_total = VALUES(jintie_total),
                     distribution = VALUES(distribution),
                     updated_at = CURRENT_TIMESTAMP";

        $paramStmt = $pdo->prepare($paramSql);
        $paramStmt->execute([$recordDate, $workDays, $jintieTotal, $distribution]);

        // 删除该日期的旧记录
        $deleteSql = "DELETE FROM jintie_records WHERE record_date = ?";
        $deleteStmt = $pdo->prepare($deleteSql);
        $deleteStmt->execute([$recordDate]);

        // 准备插入员工数据的SQL
        $insertSql = "INSERT INTO jintie_records (
            record_date, month_year, name, dept, fixed_amount, floating_amount,
            fixed_actual, floating_actual, days, deduction, personal_bonus,
            general_bonus, total_amount, is_fixed_bonus, floating_ratio, remark
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

        $insertStmt = $pdo->prepare($insertSql);

        $savedCount = 0;
        foreach ($employees as $employee) {
            if (!empty($employee['name'])) {
                try {
                    $insertStmt->execute([
                        $recordDate,
                        $monthYear,
                        $employee['name'],
                        $employee['dept'],
                        $employee['fixed'],
                        $employee['floating'],
                        $employee['fixed_actual'],
                        $employee['floating_actual'],
                        $employee['days'],
                        $employee['deduction'],
                        $employee['personal_bonus'],
                        $employee['general_bonus'],
                        $employee['total'],
                        $employee['is_fixed_bonus'] ? 1 : 0,
                        $employee['floating_ratio'],
                        $employee['remark'] ?? ''
                    ]);
                    $savedCount++;
                } catch (Exception $e) {
                    throw new Exception("保存员工 " . $employee['name'] . " 的数据失败: " . $e->getMessage());
                }
            }
        }

        $pdo->commit();
        $_SESSION['message'] = "数据已成功保存到数据库！共保存 {$savedCount} 条员工记录。";

    } catch (Exception $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        $_SESSION['error'] = "保存到数据库失败：" . $e->getMessage();
    }

    // 保存操作完成，不重定向，继续显示当前页面
}

// 处理人员管理固定奖金状态同步
if(isset($_POST['action']) && $_POST['action'] == 'update_personnel_fixed_bonus') {
    // 强力清理输出缓冲区，防止BOM等字符影响JSON
    while (ob_get_level()) {
        ob_end_clean();
    }
    ob_start();
    header('Content-Type: application/json; charset=utf-8');

    try {
        $name = $_POST['name'];
        $isFixedBonus = intval($_POST['is_fixed_bonus']);
        $floatingRatio = $isFixedBonus ? 0 : 1.0;

        // 更新人员表中的固定奖金状态和浮动比例
        $stmt = $pdo->prepare("UPDATE jintie_personnel SET is_fixed_bonus = ?, floating_ratio = ? WHERE name = ?");
        $result = $stmt->execute([$isFixedBonus, $floatingRatio, $name]);

        // 检查影响的行数
        $affectedRows = $stmt->rowCount();

        if ($result && $affectedRows > 0) {
            $response = json_encode(['success' => true, 'message' => 'success', 'affected_rows' => $affectedRows]);
        } else {
            $response = json_encode(['success' => false, 'message' => 'not found or update failed']);
        }
    } catch (Exception $e) {
        $response = json_encode(['success' => false, 'message' => 'exception occurred']);
    }

    // 清理响应中的BOM字符
    $response = str_replace("\xEF\xBB\xBF", '', $response);
    echo $response;
    exit;
}

// 处理数据清除
if(isset($_POST['clear_data'])) {
    try {
        // 1. 清除SESSION中的数据
        $_SESSION['jintie_employees'] = [];

        // 2. 重置参数为默认值
        $_SESSION['jintie_params'] = [
            'workers_count' => 15,
            'jintie_total' => 10000,
            'distribution' => 0,
            'record_date' => date('Y-m-d'),
            'record_month' => date('Y-m')
        ];

        // 3. 设置标记，跳过当天数据库记录的读取
        $_SESSION['skip_today_records'] = true;

    } catch (Exception $e) {
        // 清除数据异常处理
    }

    // 4. 重定向页面，让页面重新加载并从人员表重新初始化
    header("Location: jintie.php");
    exit;
}

// 处理删除员工
if(isset($_POST['delete_id'])) {
    $deleteId = intval($_POST['delete_id']);
    $employees = $_SESSION['jintie_employees'] ?? [];
    
    // 过滤掉要删除的员工
    $employees = array_filter($employees, function($employee) use ($deleteId) {
        return $employee['id'] != $deleteId;
    });
    
    // 重新编号
    $newEmployees = [];
    foreach($employees as $index => $employee) {
        $employee['id'] = $index + 1;
        $newEmployees[] = $employee;
    }
    
    $_SESSION['jintie_employees'] = $newEmployees;
    $employees = $newEmployees;
} else {
    // 从SESSION获取员工数据
    $employees = $_SESSION['jintie_employees'] ?? [];
}

// 从SESSION获取员工数据，确保不出现交叉问题
$employees = $_SESSION['jintie_employees'] ?? [];

// 从数据库加载人员基础数据
try {
    // 使用sort_order字段排序，确保与人员管理页面顺序一致，只获取可见的人员
    $stmt = $pdo->query("SELECT * FROM jintie_personnel WHERE is_visible = 1 ORDER BY sort_order ASC");
    $personnel = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // 先检查 SESSION 中是否有员工数据
    $employees = $_SESSION['jintie_employees'] ?? [];

    // 如果 SESSION 中没有数据，则从数据库初始化
    if(empty($employees)) {
        $employees = [];

        // 检查是否需要跳过当天记录（清除数据后）
        $skipTodayRecords = isset($_SESSION['skip_today_records']) && $_SESSION['skip_today_records'];
        if ($skipTodayRecords) {
            unset($_SESSION['skip_today_records']); // 清除标记，只使用一次
        }

        // 首先检查当天是否有已保存的津贴记录（除非被标记跳过）
        $savedRecords = [];
        if (!$skipTodayRecords) {
            $currentDate = date('Y-m-d');
            $recordStmt = $pdo->prepare("SELECT * FROM jintie_records WHERE record_date = ? ORDER BY id");
            $recordStmt->execute([$currentDate]);
            $savedRecords = $recordStmt->fetchAll(PDO::FETCH_ASSOC);
        }

        if (!empty($savedRecords)) {
            // 如果有当天的记录，从记录中恢复数据
            foreach($savedRecords as $index => $record) {
                $employees[] = [
                    'id' => $index + 1,
                    'dept' => $record['dept'],
                    'name' => $record['name'],
                    'fixed' => floatval($record['fixed_amount']),
                    'floating' => floatval($record['floating_amount']),
                    'days' => intval($record['days']),
                    'fixed_actual' => floatval($record['fixed_actual']),
                    'floating_ratio' => floatval($record['floating_ratio']), // 直接使用保存的浮动比例
                    'deduction' => intval($record['deduction']),
                    'bonus' => intval($record['personal_bonus']) + intval($record['general_bonus']),
                    'personal_bonus' => intval($record['personal_bonus']),
                    'general_bonus' => intval($record['general_bonus']),
                    'floating_actual' => floatval($record['floating_actual']),
                    'total' => floatval($record['total_amount']),
                    'is_fixed_bonus' => !empty($record['is_fixed_bonus']),
                    'remark' => $record['remark'] ?? ''
                ];
            }
        } else {
            // 如果没有当天的记录，从人员表初始化
            foreach($personnel as $index => $person) {
                $isFixedBonus = !empty($person['is_fixed_bonus']) && $person['is_fixed_bonus'] == 1;
                $dept = $person['dept'];
                $fixedAmount = floatval($person['fixed_amount']);
                $floatingAmount = floatval($person['floating_amount']);
                // 使用数据库中的floating_ratio，如果不存在则根据固定奖金状态设置
                $floatingRatio = isset($person['floating_ratio']) ? floatval($person['floating_ratio']) : ($isFixedBonus ? 0 : 1.0);

                // 创建新员工记录
                $employees[] = [
                    'id' => $index + 1,
                    'dept' => $dept,
                    'name' => $person['name'],
                    'fixed' => $fixedAmount,
                    'floating' => $floatingAmount,
                    'days' => 0,
                    'fixed_actual' => $fixedAmount,
                    'floating_ratio' => $floatingRatio,
                    'deduction' => 0,
                    'bonus' => 0,
                    'personal_bonus' => 0,
                    'general_bonus' => 0,
                    'floating_actual' => $floatingAmount,
                    'total' => $fixedAmount + $floatingAmount,
                    'is_fixed_bonus' => $isFixedBonus,
                    'remark' => $person['remark'] ?? ''
                ];
            }
        }
        
        // 将从数据库初始化的数据保存到 SESSION
        $_SESSION['jintie_employees'] = $employees;
    }
} catch (PDOException $e) {
    // 数据库查询出错
    // 如果SESSION中没有数据，初始化空数组
    if(empty($employees)) {
        $employees = [];
        $_SESSION['jintie_employees'] = $employees;
    }
}

$params = $_SESSION['jintie_params'];

// 处理获取原始金额的请求
if(isset($_POST['get_original_amounts'])) {
    try {
        $stmt = $pdo->query("SELECT name, dept, fixed_amount, floating_amount, is_fixed_bonus FROM jintie_personnel WHERE is_visible = 1 ORDER BY sort_order ASC");
        $personnel = $stmt->fetchAll(PDO::FETCH_ASSOC);

        sendJsonResponse([
            'success' => true,
            'message' => '成功获取原始金额数据',
            'personnel' => $personnel
        ]);
    } catch (PDOException $e) {
        sendJsonResponse([
            'success' => false,
            'message' => '获取原始金额数据失败: ' . $e->getMessage()
        ]);
    }
}

// 处理获取岗位金额设置的请求
if(isset($_POST['get_dept_settings'])) {
    try {
        $stmt = $pdo->query("SELECT * FROM jintie_dept_settings ORDER BY id DESC LIMIT 1");
        $settings = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$settings) {
            $settings = [
                'jintie_total' => 10000,
                'monitoring_base_amount' => 2500,
                'cb26_base_amount' => 400
            ];
        }

        sendJsonResponse([
            'success' => true,
            'settings' => $settings
        ]);
    } catch (PDOException $e) {
        sendJsonResponse([
            'success' => false,
            'message' => '获取岗位金额设置失败: ' . $e->getMessage()
        ]);
    }
}

// 处理保存岗位金额设置的请求
if(isset($_POST['save_dept_settings'])) {
    try {
        $jintieTotal = floatval($_POST['jintie_total'] ?? 10000);
        $monitoringBaseAmount = floatval($_POST['monitoring_base_amount'] ?? 2500);
        $cb26BaseAmount = floatval($_POST['cb26_base_amount'] ?? 400);

        $checkStmt = $pdo->query("SELECT COUNT(*) FROM jintie_dept_settings");
        $hasSettings = $checkStmt->fetchColumn() > 0;

        if ($hasSettings) {
            $pdo->exec("DELETE FROM jintie_dept_settings");
        }

        $stmt = $pdo->prepare("INSERT INTO jintie_dept_settings (jintie_total, monitoring_base_amount, cb26_base_amount) VALUES (?, ?, ?)");
        $stmt->execute([$jintieTotal, $monitoringBaseAmount, $cb26BaseAmount]);

        $_SESSION['jintie_params']['jintie_total'] = $jintieTotal;

        sendJsonResponse([
            'success' => true,
            'message' => '岗位金额设置已保存'
        ]);
    } catch (PDOException $e) {
        sendJsonResponse([
            'success' => false,
            'message' => '保存岗位金额设置失败: ' . $e->getMessage()
        ]);
    }
}

// 处理保存基础参数的异步请求
// ... existing code ...
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>工艺业务流艰苦津贴计算</title>

    <!-- 预加载字体文件 -->
    <link rel="preload" href="../assets/webfonts/fa-solid-900.woff2" as="font" type="font/woff2" crossorigin="anonymous">

    <link rel="stylesheet" href="../assets/css/bootstrap.min.css">
    <link href="../assets/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/apple-style.css" rel="stylesheet">
    <script src="../assets/js/jquery.min.js"></script>
    <script src="../assets/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/fix_frontend_errors.js"></script>
    <!-- Excel导出现在使用PHP后端处理，不再需要XLSX库 -->
    <style>
        /* 表格样式增强 */
        .table {
            border: 1px solid #dee2e6;
        }
        
        /* 移除表头样式冲突 */
        
        .table td {
            border: 1px solid #dee2e6;
        }
        
        /* 页面标题样式 */
        .header {
            background-color: #4267b2;
            color: white;
            padding: 10px 15px;
            margin-bottom: 20px;
            border-radius: 4px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        /* 参数区域样式 */
        .params-area {
            background-color: #fff;
            border-radius: 4px;
            padding: 8px;
            margin-bottom: 4px;
            border: 1px solid #e9ecef;
            box-shadow: 0 1px 3px rgba(0,0,0,0.05);
        }
        
        /* 参数标题样式 */
        .param-title {
            text-align: center;
            margin-bottom: 8px;
            font-weight: bold;
            font-size: 16px;
            color: #333;
        }
        
        /* 参数行样式 */
        .params-row {
            display: flex;
            flex-wrap: wrap;
            margin: 0 -5px;
        }
        
        /* 参数项样式 */
        .param-item {
            flex: 1;
            padding: 0 5px;
            min-width: 200px;
        }
        
        /* 参数标签和输入框一行显示 */
        .param-group {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }
        
        /* 参数输入框标签样式 */
        .param-group label {
            font-weight: normal;
            color: #666;
            margin-bottom: 0;
            margin-right: 10px;
            width: 80px;
            flex-shrink: 0;
        }
        
        /* 参数输入框样式 */
        .params-area .form-control {
            border: 1px solid #ced4da;
            padding: 4px 8px;
            height: 32px;
            font-size: 14px;
            text-align: center;
            flex-grow: 1;
        }
        
        /* 只读参数字段样式 */
        .params-area .form-control[readonly] {
            background-color: #f8f9fa;
            cursor: not-allowed;
        }
        
        /* 统计区域样式 */
        .stats-area {
            display: flex;
            flex-wrap: wrap;
            margin: 0 -5px 2px;
        }
        
        /* 统计卡片样式 */
        .stat-card {
            flex: 1;
            padding: 8px;
            border-radius: 4px;
            margin: 3px;
            text-align: center;
            min-width: 150px;
            box-shadow: 0 1px 2px rgba(0,0,0,0.05);
        }
        
        /* 统计卡片标题样式 */
        .stat-card h6 {
            color: #555;
            font-size: 13px;
            margin-bottom: 3px;
            font-weight: 600;
        }
        
        /* 统计卡片数值样式 */
        .stat-card h4 {
            color: #333;
            font-size: 18px;
            font-weight: bold;
            margin: 0;
        }
        
        /* 生产总额卡片样式 */
        .production-card {
            background-color: rgba(240, 255, 240, 0.6);
            border: 1px solid #d0e0d0;
            border-left: 3px solid #28a745;
        }
        
        /* 监控总额卡片样式 */
        .monitoring-card {
            background-color: rgba(240, 248, 255, 0.6);
            border: 1px solid #d0d8e0;
            border-left: 3px solid #17a2b8;
        }
        
        /* CB26总额卡片样式 */
        .cb26-card {
            background-color: rgba(255, 250, 240, 0.6);
            border: 1px solid #e0d8d0;
            border-left: 3px solid #fd7e14;
        }
        
        /* 奖励总额卡片样式 */
        .bonus-card {
            background-color: rgba(255, 243, 224, 0.6);
            border: 1px solid #e0d0c0;
            border-left: 3px solid #ff9800;
        }
        
        /* 显示岗位选择样式 */
        .dept-selector {
            margin-bottom: 15px;
        }
        
        /* 编辑表格样式 */
        .table input, .table select {
            width: 100%;
            height: 28px;
            border: 1px solid #ced4da;
            padding: 2px 4px;
            border-radius: 3px;
            text-align: center;
            background-color: #fff;
        }
        
        /* 只读字段样式 */
        .readonly-field {
            background-color: #e9ecef !important;
            pointer-events: none; /* 阻止所有鼠标交互 */
        }
        
        /* 删除按钮样式 */
        .delete-btn {
            color: #dc3545;
            cursor: pointer;
            font-size: 16px;
        }
        
        /* 表格行样式 */
        .table-row-green {
            background-color: rgba(240, 255, 240, 0.5);
        }
        
        /* 确保下拉菜单在编辑时显示完整 */
        .edit-control.form-control-sm {
            height: 28px;
            padding: 2px 5px;
        }
        
        /* 岗位下拉框样式 */
        select.edit-control {
            width: 60px !important;
            text-align: center !important;
            text-align-last: center !important;
            padding: 0 16px 0 5px !important;
            appearance: none;
            background-position: right 2px center;
            margin: 0 auto;
            display: block;
        }
        
        /* 确保数字输入框按钮增减是整数 */
        input[type="number"] {
            width: 100%;
            text-align: center;
            -moz-appearance: textfield;
        }
        
        /* 默认隐藏上下箭头 */
        input[type="number"]::-webkit-inner-spin-button, 
        input[type="number"]::-webkit-outer-spin-button { 
            -webkit-appearance: none;
            margin: 0;
        }
        
        /* 编辑模式下完全隐藏上下箭头 */
        .edit-control[type="number"]::-webkit-inner-spin-button, 
        .edit-control[type="number"]::-webkit-outer-spin-button { 
            -webkit-appearance: none;
            margin: 0;
            display: none;
        }
        
        /* 只读输入框完全隐藏上下箭头 */
        input[readonly]::-webkit-inner-spin-button, 
        input[readonly]::-webkit-outer-spin-button,
        input.readonly-field::-webkit-inner-spin-button, 
        input.readonly-field::-webkit-outer-spin-button { 
            -webkit-appearance: none;
            opacity: 0;
            display: none;
        }
        
        /* 奖励悬停提示样式 */
        .bonus-tooltip {
            position: relative;
            display: inline-block;
            width: 100%;
            height: 100%;
        }
        
        .bonus-tooltip .bonus-detail {
            visibility: hidden;
            background-color: #555;
            color: #fff;
            text-align: center;
            border-radius: 4px;
            padding: 5px;
            position: absolute;
            z-index: 1;
            bottom: 125%;
            left: 50%;
            transform: translateX(-50%);
            opacity: 0;
            transition: opacity 0.3s;
            white-space: nowrap;
        }
        
        .bonus-tooltip:hover .bonus-detail {
            visibility: visible;
            opacity: 1;
        }
        
        /* 岗位背景颜色 */
        .row-production {
            background-color: rgba(240, 255, 240, 0.5) !important;
            border-left: 3px solid #28a745 !important;
        }
        
        .row-monitoring {
            background-color: rgba(240, 248, 255, 0.5) !important;
            border-left: 3px solid #17a2b8 !important;
        }
        
        .row-cb26 {
            background-color: rgba(255, 250, 240, 0.5) !important;
            border-left: 3px solid #fd7e14 !important;
        }
        
        /* 表格行悬停样式 */
        .row-production:hover {
            background-color: rgba(220, 240, 220, 0.8) !important;
            cursor: pointer;
            transition: background-color 0.2s ease;
        }

        .row-monitoring:hover {
            background-color: rgba(220, 235, 250, 0.8) !important;
            cursor: pointer;
            transition: background-color 0.2s ease;
        }

        .row-cb26:hover {
            background-color: rgba(255, 240, 220, 0.8) !important;
            cursor: pointer;
            transition: background-color 0.2s ease;
        }

        /* 表格行点击/激活样式 */
        .row-production:active,
        .row-production.active {
            background-color: rgba(200, 230, 200, 0.9) !important;
            transform: scale(0.998);
        }

        .row-monitoring:active,
        .row-monitoring.active {
            background-color: rgba(200, 225, 240, 0.9) !important;
            transform: scale(0.998);
        }

        .row-cb26:active,
        .row-cb26.active {
            background-color: rgba(245, 230, 200, 0.9) !important;
            transform: scale(0.998);
        }
        
        /* 表格条纹样式覆盖 */
        .table-striped .row-production,
        .table-striped .row-monitoring,
        .table-striped .row-cb26,
        .table-hover .row-production,
        .table-hover .row-monitoring,
        .table-hover .row-cb26 {
            --bs-table-accent-bg: transparent !important;
        }
        
        /* 表格基本样式 - 减小行高 */
        .table th, .table td {
            text-align: center;
            vertical-align: middle;
            padding: 2px 1px;
            font-size: 13px;
            line-height: 1.2;
        }
        
        /* 表格单元格输入框样式 - 更紧凑 */
        .table input.form-control-sm, 
        .table select.form-control-sm {
            height: 24px;
            padding: 1px 2px;
            font-size: 12px;
        }
        
        /* 岗位标题 */
        .table th {
            padding: 5px 2px;
            white-space: nowrap;
        }
        
        /* 奖励输入框样式 - 更紧凑横排版 */
        .bonus-input-container {
            position: relative;
            width: 100%;
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: space-between;
            gap: 2px;
        }
        
        /* 奖励输入框标签样式 */
        .bonus-input-label {
            font-size: 9px;
            color: #666;
            text-align: center;
            margin: 0;
            padding: 0;
            width: auto;
            line-height: 1;
        }
        
        /* 奖励输入框组合 */
        .bonus-input-group {
            display: flex;
            align-items: center;
            width: 48%;
        }
        
        .personal-bonus-input,
        .general-bonus-input {
            width: 70%;
            font-size: 11px;
            padding: 0 2px;
            text-align: center;
            height: 20px;
            min-height: 20px;
        }
        
        /* 奖励表头样式 */
        .bonus-header {
            font-size: 13px;
            white-space: nowrap;
        }
        
        /* 编辑模式下岗位选择 */
        td.dept-cell select {
            margin: 0 auto;
        }
        
        /* 按钮容器样式 */
        .buttons-container {
            display: flex;
            align-items: center;
            flex-wrap: wrap;
            gap: 8px;
            margin: 0;
            padding: 0;
        }

        /* 操作按钮之间的间距 */
        .buttons-container .btn {
            margin: 0;
        }
        
        /* 津贴管理页面特定样式 */

        /* 强制应用Apple风格表头 */
        .table thead th,
        .table thead.thead-dark th,
        .table thead.thead-light th,
        #dataTable thead th,
        #historyModal .table thead th {
            background: #f9f9f9 !important;
            color: #1d1d1f !important;
            border-color: rgba(0, 0, 0, 0.04) !important;
            font-weight: 600 !important;
            font-size: 15px !important;
            padding: 16px 20px !important;
            text-align: center !important;
            vertical-align: middle !important;
            letter-spacing: -0.022em !important;
        }
        
        /* 下拉框通用样式 */
        select.form-control, select.form-control-sm, select.edit-control {
            text-align: center !important;
            text-align-last: center !important;
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' fill='%23333' viewBox='0 0 16 16'%3E%3Cpath d='M7.247 11.14 2.451 5.658C1.885 5.013 2.345 4 3.204 4h9.592a1 1 0 0 1 .753 1.659l-4.796 5.48a1 1 0 0 1-1.506 0z'/%3E%3C/svg%3E");
            background-repeat: no-repeat;
            background-position: right 4px center;
            padding: 0 16px 0 5px !important;
            min-width: 60px !important;
        }
        
        /* 岗位选择框样式 */
        #deptSelector {
            width: 60px !important;
        }
        
        /* 表格中的下拉框样式 */
        select.edit-control {
            width: 60px !important;
        }
        
        /* 确保下拉框选项也居中对齐 */
        select.form-control option, 
        select.form-control-sm option, 
        select.edit-control option {
            text-align: center !important;
            text-align-last: center !important;
            padding: 0 4px !important;
        }
        
        /* 确保下拉框内容区域居中对齐 */
        .dept-cell select,
        .dept-cell span {
            width: 60px !important;
            display: block;
            text-align: center !important;
        }
        
        /* 浮动提示样式 */
        .floating-alert {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            min-width: 250px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
            border-left: 4px solid;
            animation: slideIn 0.5s ease forwards;
        }
        
        .floating-alert.alert-success {
            border-left-color: #28a745;
        }
        
        .floating-alert.alert-danger {
            border-left-color: #dc3545;
        }
        
        @keyframes slideIn {
            0% { transform: translateX(100%); opacity: 0; }
            100% { transform: translateX(0); opacity: 1; }
        }
        
        @keyframes slideOut {
            0% { transform: translateX(0); opacity: 1; }
            100% { transform: translateX(100%); opacity: 0; }
        }
        
        .floating-alert.hiding {
            animation: slideOut 0.5s ease forwards;
        }
        
        /* 表格列宽度调整 - 仅在编辑模式应用 */
        .edit-mode .table th:first-child, 
        .edit-mode .table td:first-child {
            width: 40px; /* 序号列更窄 */
        }
        
        .edit-mode .table th:last-child, 
        .edit-mode .table td:last-child {
            width: 50px; /* 操作列更窄 */
        }
        
        .edit-mode .table th:nth-child(6), 
        .edit-mode .table td:nth-child(6) {
            width: 60px; /* 请假天数列变窄 */
        }
        
        .edit-mode .table th:nth-child(8), 
        .edit-mode .table td:nth-child(8) {
            width: 60px; /* 浮动比例列变窄 */
        }
        
        .edit-mode .table th:nth-child(10), 
        .edit-mode .table td:nth-child(10) {
            width: 120px; /* 奖励列变宽 */
        }
        
        /* 岗位列居中显示 - 彻底修复 */
        td.dept-cell {
            text-align: center !important;
        }

        td.dept-cell .display-value {
            text-align: center !important;
            margin: 0 auto !important;
        }

        td.dept-cell select {
            margin: 0 auto !important;
            text-align: center !important;
            text-align-last: center !important;
        }
        
        /* 确保表格单元格内容居中 */
        .table td {
            text-align: center !important;
        }

        /* 重新分配表格列宽 - 合理布局 */
        /* 序号列 */
        .table th:nth-child(1),
        .table td:nth-child(1) {
            width: 40px !important;
            max-width: 40px !important;
            min-width: 40px !important;
        }

        /* 岗位列 */
        .table th:nth-child(2),
        .table td:nth-child(2) {
            width: 60px !important;
            max-width: 60px !important;
            min-width: 60px !important;
        }

        /* 姓名列 */
        .table th:nth-child(3),
        .table td:nth-child(3) {
            width: 60px !important;
            max-width: 60px !important;
            min-width: 60px !important;
        }

        /* 固定部分列 */
        .table th:nth-child(4),
        .table td:nth-child(4) {
            width: 60px !important;
            max-width: 60px !important;
            min-width: 60px !important;
        }

        /* 浮动部分列 */
        .table th:nth-child(5),
        .table td:nth-child(5) {
            width: 60px !important;
            max-width: 60px !important;
            min-width: 60px !important;
        }

        /* 请假天数列 */
        .table th:nth-child(6),
        .table td:nth-child(6) {
            width: 50px !important;
            max-width: 50px !important;
            min-width: 50px !important;
        }

        /* 应得固定列 */
        .table th:nth-child(7),
        .table td:nth-child(7) {
            width: 60px !important;
            max-width: 60px !important;
            min-width: 60px !important;
        }

        /* 浮动比例列 */
        .table th:nth-child(8),
        .table td:nth-child(8) {
            width: 50px !important;
            max-width: 50px !important;
            min-width: 50px !important;
        }

        /* 扣罚列 */
        .table th:nth-child(9),
        .table td:nth-child(9) {
            width: 50px !important;
            max-width: 50px !important;
            min-width: 50px !important;
        }

        /* 奖励列 */
        .table th:nth-child(10),
        .table td:nth-child(10) {
            width: 80px !important;
            max-width: 80px !important;
            min-width: 80px !important;
        }

        /* 应得浮动列 */
        .table th:nth-child(11),
        .table td:nth-child(11) {
            width: 60px !important;
            max-width: 60px !important;
            min-width: 60px !important;
        }

        /* 总金额列 */
        .table th:nth-child(12),
        .table td:nth-child(12) {
            width: 60px !important;
            max-width: 60px !important;
            min-width: 60px !important;
        }

        /* 固定列（复选框） */
        .table th:nth-child(13),
        .table td:nth-child(13) {
            width: 35px !important;
            max-width: 35px !important;
            min-width: 35px !important;
        }

        /* 备注列 */
        .table th:nth-child(14),
        .table td:nth-child(14) {
            width: 100px !important;
            max-width: 100px !important;
            min-width: 100px !important;
        }

        /* 固定奖金区域样式 */
        .fixed-bonus-container {
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .fixed-bonus-label {
            display: flex;
            align-items: center;
            margin: 0;
            font-size: 12px;
            white-space: nowrap;
        }

        .fixed-bonus-checkbox {
            margin-right: 3px;
            width: 14px;
            height: 14px;
        }

        /* 禁用状态下的输入框样式 */
        input[disabled].form-control-sm,
        input[disabled].edit-control {
            background-color: #e9ecef !important;
            cursor: not-allowed;
            opacity: 0.7;
        }

        /* 添加disabled-field类的样式（如果尚未存在） */
        .disabled-field, input[disabled].edit-control {
            background-color: #e9ecef !important;
        }

        /* 预览模态框日期信息样式 */
        #previewDateInfo {
            display: block;
            margin-top: 5px;
            font-size: 13px;
            color: #6c757d;
        }

        #previewMonth, #previewDate {
            font-weight: 500;
            color: #495057;
        }

        /* 历史记录模态框样式 */
        #historyModal .table-responsive {
            max-height: 400px;
        }

        #historyModal .table th {
            position: sticky;
            top: 0;
            z-index: 10;
            font-size: 14px;
            padding: 8px 4px;
        }

        #historyModal .table td {
            font-size: 13px;
            padding: 6px 4px;
            vertical-align: middle;
        }

        /* 历史记录表格列宽优化 */
        #historyModal .table th:nth-child(1),
        #historyModal .table td:nth-child(1) {
            width: 50px; /* 序号列 */
            text-align: center;
        }

        #historyModal .table th:nth-child(2),
        #historyModal .table td:nth-child(2) {
            width: 100px; /* 年月列 */
            text-align: center;
        }

        #historyModal .table th:nth-child(3),
        #historyModal .table td:nth-child(3) {
            width: 120px; /* 日期列 */
            text-align: center;
        }

        #historyModal .table th:nth-child(4),
        #historyModal .table td:nth-child(4) {
            width: 80px; /* 记录数列 */
            text-align: center;
        }

        #historyModal .table th:nth-child(5),
        #historyModal .table td:nth-child(5) {
            width: 100px; /* 津贴总额列 */
            text-align: center;
        }

        #historyModal .table th:nth-child(6),
        #historyModal .table td:nth-child(6) {
            width: 140px; /* 操作列 - 增加宽度以容纳两个按钮 */
            text-align: center;
        }

        .load-history-record, .delete-history-record {
            font-size: 11px;
            padding: 3px 6px;
            white-space: nowrap;
        }

        .delete-history-record {
            margin-left: 4px;
        }

        #historyLoading, #historyEmpty {
            padding: 20px;
            font-size: 14px;
        }

        #historyYear {
            max-width: 150px;
        }

        /* 补齐差额模态框样式 */
        #balanceModal .modal-dialog {
            max-width: 400px;
        }

        #balanceModal .modal-body {
            padding: 20px;
        }

        #balanceModal #balanceEmployee {
            max-width: 200px;
            margin: 0 auto;
            text-align: center;
            text-align-last: center;
        }

        #balanceModal #balanceDifference {
            font-size: 28px;
            color: #dc3545 !important;
            font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
        }

        #balanceModal .text-muted {
            font-size: 14px;
            color: #6c757d !important;
        }

        #balanceModal .form-label {
            font-weight: 600;
            color: #495057;
            font-size: 14px;
        }

        #balanceModal .modal-header {
            border-bottom: 1px solid #e9ecef;
            background-color: #f8f9fa;
        }

        #balanceModal .modal-footer {
            border-top: 1px solid #e9ecef;
            background-color: #f8f9fa;
            justify-content: center;
        }

        #balanceModal .btn {
            min-width: 80px;
        }
    </style>
</head>
<body>
<div class="container-fluid px-4">
    <!-- 页面标题 -->
    <div class="page-header">
        <h3><i class="fas fa-money-bill-wave mr-2"></i>工艺业务流艰苦津贴计算</h3>
        <div class="btn-group">
            <a href="personnel_manage.php" class="btn btn-info">
                <i class="fas fa-users mr-1"></i> 人员管理
            </a>
            <button type="button" class="btn btn-warning" id="deptAmountSettingsBtn">
                <i class="fas fa-cog mr-1"></i> 岗位金额设置
            </button>
        </div>
    </div>

    <!-- 基础参数区域 -->
    <div class="container-fluid" style="padding: 0 15px; margin-bottom: 4px;">
        <div class="params-area" id="paramsArea">
            <h5 class="param-title">津贴计算基础参数</h5>

            <div class="params-row">
                <div class="param-item">
                    <div class="param-group">
                        <label for="recordMonth">月度</label>
                        <input type="month" class="form-control" id="recordMonth" name="record_month" value="<?= $params['record_month'] ?? date('Y-m') ?>">
                    </div>
                </div>
                <div class="param-item">
                    <div class="param-group">
                        <label for="recordDate">日期</label>
                        <input type="date" class="form-control" id="recordDate" name="record_date" value="<?= $params['record_date'] ?? date('Y-m-d') ?>">
                    </div>
                </div>
                <div class="param-item">
                    <div class="param-group">
                        <label for="workersCount">应出勤天数</label>
                        <input type="number" class="form-control" id="workersCount" name="workers_count" value="<?= htmlspecialchars((string)($params['workers_count'] ?? 15)) ?>">
                    </div>
                </div>
                <div class="param-item">
                    <div class="param-group">
                        <label for="jintieTotal">津贴总额</label>
                        <input type="number" class="form-control" id="jintieTotal" name="jintie_total" value="<?= htmlspecialchars((string)($params['jintie_total'] ?? 10000)) ?>" readonly>
                    </div>
                </div>
                <div class="param-item">
                    <div class="param-group">
                        <label for="distribution">分配总额</label>
                        <input type="number" class="form-control" id="distribution" name="distribution" value="<?= htmlspecialchars((string)($params['distribution'] ?? 0)) ?>" readonly>
                    </div>
                </div>
            </div>

            <!-- 统计区域 -->
            <div class="stats-area">
                <div class="stat-card production-card">
                    <h6>生产岗位总额</h6>
                    <h4 id="productionTotal">0</h4>
                </div>
                <div class="stat-card monitoring-card">
                    <h6>监控岗位总额</h6>
                    <h4 id="monitoringTotal">0</h4>
                </div>
                <div class="stat-card cb26-card">
                    <h6>CB26岗位总额</h6>
                    <h4 id="cb26Total">0</h4>
                </div>
                <div class="stat-card bonus-card">
                    <h6>总金额中奖励总额</h6>
                    <h4 id="bonusTotal">0</h4>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 操作按钮和岗位选择 -->
    <div class="container-fluid" style="padding: 20 15px; margin-bottom: 2px;">
        <div class="row">
            <div class="col-md-12">
                <div class="d-flex align-items-center flex-wrap" style="gap: 12px;">
                    <div class="d-flex align-items-center">
                        <label for="deptSelector" class="mb-0 mr-2">显示岗位：</label>
                        <select class="form-control-sm" id="deptSelector" style="width: 100px;">
                            <option value="all">全部</option>
                            <option value="production">生产</option>
                            <option value="monitoring">监控</option>
                            <option value="cb26">CB26</option>
                        </select>
                    </div>
                    <div class="buttons-container">
                        <!-- 按照新的顺序排列按钮 -->
                        <button type="button" id="loadHistoryBtn" class="btn btn-warning btn-sm">
                            <i class="fas fa-history"></i> 提取历史
                        </button>
                        <button type="button" class="btn btn-danger btn-sm" id="clearDataBtn">
                            <i class="fas fa-trash-alt"></i> 清除数据
                        </button>
                        <button type="button" id="calculateBtn" class="btn btn-success btn-sm">
                            <i class="fas fa-calculator"></i> 计算津贴
                        </button>
                        <button type="button" id="balanceBtn" class="btn btn-success btn-sm">
                            <i class="fas fa-balance-scale"></i> 补齐差额
                        </button>
                        <!-- 数据操作按钮 -->
                        <button type="button" class="btn btn-info btn-sm" id="copyDataBtn">
                            <i class="fas fa-copy"></i> 一键复制
                        </button>
                        <button type="button" class="btn btn-success btn-sm" id="exportExcelBtn">
                            <i class="fas fa-file-excel"></i> 导出EXCEL
                        </button>
                        <button type="button" class="btn btn-primary btn-sm" id="saveToDbBtn">
                            <i class="fas fa-database"></i> 保存到数据库
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 数据表格 -->
    <?php /* HTML表单生成 */ ?>
    <div class="container-fluid" style="padding: 0 15px;">
        <!-- 消息提示 -->
        <?php if(isset($_SESSION['message'])): ?>
        <div class="alert alert-success alert-dismissible fade show floating-alert">
            <?= $_SESSION['message'] ?>
            <button type="button" class="close" data-dismiss="alert">&times;</button>
        </div>
        <?php unset($_SESSION['message']); endif; ?>
        
        <?php if(isset($_SESSION['error'])): ?>
        <div class="alert alert-danger alert-dismissible fade show floating-alert">
            <?= $_SESSION['error'] ?>
            <button type="button" class="close" data-dismiss="alert">&times;</button>
        </div>
        <?php unset($_SESSION['error']); endif; ?>
        
        <form id="dataForm" method="post">
            <input type="hidden" name="workers_count" id="hiddenWorkersCount" value="<?= htmlspecialchars((string)($params['workers_count'] ?? 15)) ?>">
            <input type="hidden" name="jintie_total" id="hiddenJintieTotal" value="<?= htmlspecialchars((string)($params['jintie_total'] ?? 10000)) ?>">
            <input type="hidden" name="distribution" id="hiddenDistribution" value="<?= htmlspecialchars((string)($params['distribution'] ?? 0)) ?>">
            <!-- 添加是否保存模式的标志 -->
            <input type="hidden" id="isSavedMode" value="<?= $isSavedMode ? '1' : '0' ?>">
            
            <div class="table-responsive <?= $isSavedMode ? '' : 'edit-mode' ?>">
                <table class="table table-striped" id="dataTable">
                    <thead class="thead-light apple-thead">
                        <tr>
                            <th style="background: #f9f9f9 !important; color: #1d1d1f !important; font-weight: 600 !important; font-size: 15px !important; padding: 16px 20px !important; text-align: center !important; border-color: rgba(0, 0, 0, 0.04) !important;">序号</th>
                            <th style="background: #f9f9f9 !important; color: #1d1d1f !important; font-weight: 600 !important; font-size: 15px !important; padding: 16px 20px !important; text-align: center !important; border-color: rgba(0, 0, 0, 0.04) !important;">岗位</th>
                            <th style="background: #f9f9f9 !important; color: #1d1d1f !important; font-weight: 600 !important; font-size: 15px !important; padding: 16px 20px !important; text-align: center !important; border-color: rgba(0, 0, 0, 0.04) !important;">姓名</th>
                            <th style="background: #f9f9f9 !important; color: #1d1d1f !important; font-weight: 600 !important; font-size: 15px !important; padding: 16px 20px !important; text-align: center !important; border-color: rgba(0, 0, 0, 0.04) !important;">固定部分</th>
                            <th style="background: #f9f9f9 !important; color: #1d1d1f !important; font-weight: 600 !important; font-size: 15px !important; padding: 16px 20px !important; text-align: center !important; border-color: rgba(0, 0, 0, 0.04) !important;">浮动部分</th>
                            <th style="background: #f9f9f9 !important; color: #1d1d1f !important; font-weight: 600 !important; font-size: 15px !important; padding: 16px 20px !important; text-align: center !important; border-color: rgba(0, 0, 0, 0.04) !important;">请假天数</th>
                            <th style="background: #f9f9f9 !important; color: #1d1d1f !important; font-weight: 600 !important; font-size: 15px !important; padding: 16px 20px !important; text-align: center !important; border-color: rgba(0, 0, 0, 0.04) !important;">应得固定</th>
                            <th style="background: #f9f9f9 !important; color: #1d1d1f !important; font-weight: 600 !important; font-size: 15px !important; padding: 16px 20px !important; text-align: center !important; border-color: rgba(0, 0, 0, 0.04) !important;">浮动比例</th>
                            <th style="background: #f9f9f9 !important; color: #1d1d1f !important; font-weight: 600 !important; font-size: 15px !important; padding: 16px 8px !important; text-align: center !important; border-color: rgba(0, 0, 0, 0.04) !important; width: 50px !important; max-width: 50px !important; min-width: 50px !important;">扣罚</th>
                            <th style="background: #f9f9f9 !important; color: #1d1d1f !important; font-weight: 600 !important; font-size: 15px !important; padding: 16px 20px !important; text-align: center !important; border-color: rgba(0, 0, 0, 0.04) !important;">奖励</th>
                            <th style="background: #f9f9f9 !important; color: #1d1d1f !important; font-weight: 600 !important; font-size: 15px !important; padding: 16px 20px !important; text-align: center !important; border-color: rgba(0, 0, 0, 0.04) !important;">应得浮动</th>
                            <th style="background: #f9f9f9 !important; color: #1d1d1f !important; font-weight: 600 !important; font-size: 15px !important; padding: 16px 20px !important; text-align: center !important; border-color: rgba(0, 0, 0, 0.04) !important;">总金额</th>
                            <th style="background: #f9f9f9 !important; color: #1d1d1f !important; font-weight: 600 !important; font-size: 15px !important; padding: 16px 4px !important; text-align: center !important; border-color: rgba(0, 0, 0, 0.04) !important; width: 35px !important; max-width: 35px !important; min-width: 35px !important;">固定</th>
                            <th width="80" style="background: #f9f9f9 !important; color: #1d1d1f !important; font-weight: 600 !important; font-size: 15px !important; padding: 16px 20px !important; text-align: center !important; border-color: rgba(0, 0, 0, 0.04) !important;">备注</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach($employees as $index => $employee): 
                            $isFixedBonus = isset($employee['is_fixed_bonus']) && $employee['is_fixed_bonus']; 

                            $rowId = "row_" . ($index + 1);
                        ?>
                        <tr class="data-row <?= $employee['dept'] == '生产' ? 'row-production' : ($employee['dept'] == '监控' ? 'row-monitoring' : 'row-cb26') ?>" id="<?= $rowId ?>">
                            <td><?= $employee['id'] ?></td>
                            <td class="dept-cell" style="text-align:center !important;">
                                <span class="display-value" style="<?= $isSavedMode ? '' : 'display:none;' ?>"><?= $employee['dept'] ?></span>
                                <select name="dept[]" id="<?= $rowId ?>_dept" class="form-control-sm edit-control" style="<?= $isSavedMode ? 'display:none;' : '' ?> margin:0 auto !important; text-align:center !important; text-align-last:center !important;">
                                    <option value="生产" <?= $employee['dept'] == '生产' ? 'selected' : '' ?>>生产</option>
                                    <option value="监控" <?= $employee['dept'] == '监控' ? 'selected' : '' ?>>监控</option>
                                    <option value="CB26" <?= $employee['dept'] == 'CB26' ? 'selected' : '' ?>>CB26</option>
                                </select>
                            </td>
                            <td class="name-cell">
                                <span class="display-value" style="<?= $isSavedMode ? '' : 'display:none;' ?>"><?= $employee['name'] ?></span>
                                <input type="text" name="name[]" id="<?= $rowId ?>_name" value="<?= $employee['name'] ?>" class="form-control-sm edit-control" style="<?= $isSavedMode ? 'display:none;' : '' ?>">
                            </td>
                            <td>
                                <span class="display-value" style="<?= $isSavedMode ? '' : 'display:none;' ?>"><?= number_format($employee['fixed']) ?></span>
                                <input type="number" step="1" name="fixed[]" id="<?= $rowId ?>_fixed" value="<?= $employee['fixed'] ?>" class="form-control-sm calc-input edit-control" style="<?= $isSavedMode ? 'display:none;' : '' ?>">
                            </td>
                            <td>
                                <span class="display-value" style="<?= $isSavedMode ? '' : 'display:none;' ?>"><?= number_format($employee['floating']) ?></span>
                                <input type="number" step="1" name="floating[]" id="<?= $rowId ?>_floating" value="<?= $employee['floating'] ?>" class="form-control-sm calc-input edit-control" style="<?= $isSavedMode ? 'display:none;' : '' ?>" <?= isset($employee['is_fixed_bonus']) && $employee['is_fixed_bonus'] ? 'disabled class="disabled-field"' : '' ?>>
                            </td>
                            <td>
                                <span class="display-value" style="<?= $isSavedMode ? '' : 'display:none;' ?>"><?= $employee['days'] ?></span>
                                <input type="number" name="days[]" id="<?= $rowId ?>_days" value="<?= $employee['days'] ?>" class="form-control-sm calc-input edit-control" style="<?= $isSavedMode ? 'display:none;' : '' ?>">
                            </td>
                            <td>
                                <span class="display-value" style="<?= $isSavedMode ? '' : 'display:none;' ?>"><?= number_format($employee['fixed_actual']) ?></span>
                                <input type="number" step="1" name="fixed_actual[]" id="<?= $rowId ?>_fixed_actual" value="<?= $employee['fixed_actual'] ?>" class="form-control-sm readonly-field edit-control" style="<?= $isSavedMode ? 'display:none;' : '' ?>" readonly>
                            </td>
                            <td>
                                <span class="display-value" style="<?= $isSavedMode ? '' : 'display:none;' ?>"><?= $employee['floating_ratio'] ?></span>
                                <input type="number" step="0.1" name="floating_ratio[]" id="<?= $rowId ?>_floating_ratio" value="<?= $employee['floating_ratio'] ?>" class="form-control-sm calc-input edit-control floating-ratio-input" style="<?= $isSavedMode ? 'display:none;' : '' ?>" <?= isset($employee['is_fixed_bonus']) && $employee['is_fixed_bonus'] ? 'disabled class="disabled-field"' : '' ?>>
                                <input type="hidden" name="needs_calculation" id="<?= $rowId ?>_needs_calculation" value="0">
                            </td>
                            <td>
                                <span class="display-value" style="<?= $isSavedMode ? '' : 'display:none;' ?>"><?= number_format($employee['deduction']) ?></span>
                                <input type="number" name="deduction[]" id="<?= $rowId ?>_deduction" value="<?= $employee['deduction'] ?>" class="form-control-sm calc-input edit-control" style="<?= $isSavedMode ? 'display:none;' : '' ?>">
                            </td>
                            <td>
                                <span class="display-value" style="<?= $isSavedMode ? '' : 'display:none;' ?>">
                                    <div class="bonus-tooltip">
                                        <?= number_format($employee['bonus']) ?>
                                        <span class="bonus-detail">
                                            个人: <?= number_format($employee['personal_bonus'] ?? 0) ?> + 
                                            总额: <?= number_format($employee['general_bonus'] ?? ($employee['bonus'] - ($employee['personal_bonus'] ?? 0))) ?>
                                        </span>
                                    </div>
                                </span>
                                <div class="bonus-input-container edit-control" style="<?= $isSavedMode ? 'display:none;' : '' ?>">
                                    <div class="bonus-input-group">
                                        <input type="number" name="personal_bonus[]" id="<?= $rowId ?>_personal_bonus" value="<?= $employee['personal_bonus'] ?? 0 ?>" 
                                               class="form-control-sm calc-input personal-bonus-input">
                                        <span class="bonus-input-label">个</span>
                                    </div>
                                    <div class="bonus-input-group">
                                        <input type="number" name="general_bonus[]" id="<?= $rowId ?>_general_bonus" value="<?= $employee['general_bonus'] ?? ($employee['bonus'] - ($employee['personal_bonus'] ?? 0)) ?>" 
                                               class="form-control-sm calc-input general-bonus-input">
                                        <span class="bonus-input-label">总</span>
                                    </div>
                                    <input type="hidden" name="bonus[]" id="<?= $rowId ?>_bonus" value="<?= $employee['bonus'] ?>" 
                                           class="total-bonus-input">
                                </div>
                            </td>
                            <td>
                                <span class="display-value" style="<?= $isSavedMode ? '' : 'display:none;' ?>"><?= number_format($employee['floating_actual']) ?></span>
                                <input type="number" step="1" name="floating_actual[]" id="<?= $rowId ?>_floating_actual" value="<?= $employee['floating_actual'] ?>" class="form-control-sm readonly-field edit-control" style="<?= $isSavedMode ? 'display:none;' : '' ?>" readonly>
                            </td>
                            <td>
                                <span class="display-value" style="<?= $isSavedMode ? '' : 'display:none;' ?>"><?= number_format($employee['total']) ?></span>
                                <input type="number" step="1" name="total[]" id="<?= $rowId ?>_total" value="<?= $employee['total'] ?>" class="form-control-sm readonly-field edit-control" style="<?= $isSavedMode ? 'display:none;' : '' ?>" readonly>
                            </td>
                            <td style="text-align: center; padding: 8px;">
                                <span class="display-value" style="<?= $isSavedMode ? '' : 'display:none;' ?>"><?= isset($employee['is_fixed_bonus']) && $employee['is_fixed_bonus'] ? '✓' : '' ?></span>
                                <div class="edit-control" style="<?= $isSavedMode ? 'display:none;' : '' ?>">
                                    <input type="checkbox" id="<?= $rowId ?>_is_fixed_bonus_checkbox" <?= isset($employee['is_fixed_bonus']) && $employee['is_fixed_bonus'] ? 'checked' : '' ?>
                                           style="width: 16px; height: 16px; cursor: pointer; margin: 0;">
                                    <input type="hidden" name="is_fixed_bonus[]" id="<?= $rowId ?>_is_fixed_bonus" value="<?= isset($employee['is_fixed_bonus']) && $employee['is_fixed_bonus'] ? '1' : '0' ?>">
                                </div>
                            </td>
                            <td>
                                <!-- 备注栏 -->
                                <span class="display-value" style="<?= $isSavedMode ? '' : 'display:none;' ?>"><?= htmlspecialchars($employee['remark'] ?? '') ?></span>
                                <input type="text" name="remark[]" id="<?= $rowId ?>_remark" value="<?= htmlspecialchars($employee['remark'] ?? '') ?>" class="form-control-sm edit-control" style="<?= $isSavedMode ? 'display:none;' : '' ?>" placeholder="备注">
                                <input type="hidden" name="is_fixed_bonus_display[]" id="<?= $rowId ?>_is_fixed_bonus_display" value="<?= $isFixedBonus ? '1' : '0' ?>">
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </form>
    </div>
    
    <script>
        $(document).ready(function() {
            // 添加全局初始化标志，防止重复计算
            window.isInitializing = true;

            // 通用员工数据收集函数
            function collectEmployeeData() {
                const employeeData = [];
                $(".data-row").each(function() {
                    const rowId = $(this).attr('id');
                    const name = $('#' + rowId + '_name').val();

                    if (name && name.trim() !== '') {
                        const isFixedBonus = $('#' + rowId + '_is_fixed_bonus').val() === "1";
                        // 修复：固定奖金员工的浮动比例强制为0，非固定员工从DOM读取
                        const floatingRatio = isFixedBonus ? 0 : (parseFloat($('#' + rowId + '_floating_ratio').val()) || 1.0);

                        employeeData.push({
                            name: name,
                            dept: $('#' + rowId + '_dept').val(),
                            fixed: parseFloat($('#' + rowId + '_fixed').val()) || 0,
                            floating: parseFloat($('#' + rowId + '_floating').val()) || 0,
                            fixed_actual: parseFloat($('#' + rowId + '_fixed_actual').val()) || 0,
                            floating_actual: parseFloat($('#' + rowId + '_floating_actual').val()) || 0,
                            days: parseInt($('#' + rowId + '_days').val()) || 0,
                            deduction: parseInt($('#' + rowId + '_deduction').val()) || 0,
                            personal_bonus: parseInt($('#' + rowId + '_personal_bonus').val()) || 0,
                            general_bonus: parseInt($('#' + rowId + '_general_bonus').val()) || 0,
                            total: parseInt($('#' + rowId + '_total').val()) || 0,
                            remark: $('#' + rowId + '_remark').val() || '',
                            is_fixed_bonus: isFixedBonus,
                            floating_ratio: floatingRatio
                        });
                    }
                });
                return employeeData;
            }

            // 通用数据行遍历函数
            function forEachDataRow(callback) {
                $(".data-row").each(function() {
                    const rowId = $(this).attr('id');
                    if (rowId) {
                        callback($(this), rowId);
                    }
                });
            }

            // 通用数据行清空函数
            function clearAllDataRows() {
                forEachDataRow(function(row, rowId) {
                    $('#' + rowId + '_name').val('');
                    $('#' + rowId + '_dept').val('生产');
                    $('#' + rowId + '_fixed').val(0);
                    $('#' + rowId + '_floating').val(0);
                    $('#' + rowId + '_fixed_actual').val(0);
                    $('#' + rowId + '_floating_actual').val(0);
                    $('#' + rowId + '_days').val(0);
                    $('#' + rowId + '_deduction').val(0);
                    $('#' + rowId + '_personal_bonus').val(0);
                    $('#' + rowId + '_general_bonus').val(0);
                    $('#' + rowId + '_total').val(0);
                    $('#' + rowId + '_remark').val('');
                    $('#' + rowId + '_is_fixed_bonus').val('0');
                    $('#' + rowId + '_floating_ratio').val(1);
                });
            }

            // 通用AJAX错误处理函数
            function handleAjaxError(xhr, status, error, context = '') {
                console.error(`AJAX错误 ${context}:`, error);
                alert(`操作失败${context ? ' (' + context + ')' : ''}：${error}`);
            }

            // 通用AJAX成功处理函数
            function handleAjaxSuccess(response, successCallback, errorCallback) {
                if (response.success) {
                    if (typeof successCallback === 'function') {
                        successCallback(response);
                    }
                } else {
                    const message = response.message || '操作失败';
                    if (typeof errorCallback === 'function') {
                        errorCallback(message);
                    } else {
                        alert(message);
                    }
                }
            }

            // 通用统计计算函数
            function calculateDepartmentStats() {
                const stats = {
                    production: { total: 0, count: 0, bonus: 0 },
                    monitoring: { total: 0, count: 0, bonus: 0 },
                    cb26: { total: 0, count: 0, bonus: 0 },
                    overall: { total: 0, count: 0, bonus: 0 }
                };

                forEachDataRow(function(row, rowId) {
                    const name = $('#' + rowId + '_name').val();
                    if (name && name.trim() !== '') {
                        const dept = $('#' + rowId + '_dept').val();
                        const total = parseInt($('#' + rowId + '_total').val()) || 0;
                        const generalBonus = parseInt($('#' + rowId + '_general_bonus').val()) || 0;

                        stats.overall.total += total;
                        stats.overall.count++;
                        stats.overall.bonus += generalBonus;

                        if (dept === "生产") {
                            stats.production.total += total;
                            stats.production.count++;
                            stats.production.bonus += generalBonus;
                        } else if (dept === "监控") {
                            stats.monitoring.total += total;
                            stats.monitoring.count++;
                            stats.monitoring.bonus += generalBonus;
                        } else if (dept === "CB26") {
                            stats.cb26.total += total;
                            stats.cb26.count++;
                            stats.cb26.bonus += generalBonus;
                        }
                    }
                });

                return stats;
            }

            // 始终保持编辑模式
            const isSavedMode = false;

            // 始终设置为编辑模式
            $(".table-responsive").addClass("edit-mode");
            $(".display-value").hide();
            $(".edit-control").show();

            // 确保基础参数可编辑
            $("#workersCount").prop("readonly", false);
            $("#jintieTotal").prop("readonly", true);

            // 修改加载顺序：先从本地存储恢复数据，再初始化其他功能
            // 尝试恢复本地存储数据
            const loadDataPromise = new Promise((resolve) => {
                try {
                    const loaded = loadDataFromLocalStorage();
                    resolve(loaded);
                } catch (e) {
                    console.error('本地存储数据恢复失败:', e);
                    resolve(false);
                }
            });

            // 通用固定奖金状态设置函数
            function setFixedBonusStates() {
                forEachDataRow(function(row, rowId) {
                    const isFixedBonus = $('#' + rowId + '_is_fixed_bonus').val() === "1";
                    const name = $('#' + rowId + '_name').val();

                    // 对于固定奖金员工，强制设置浮动比例为0
                    if (isFixedBonus) {
                        $('#' + rowId + '_floating_ratio').val('0');
                    }

                    // 同步复选框状态
                    const checkbox = $('#' + rowId + '_is_fixed_bonus_checkbox');
                    checkbox.prop('checked', isFixedBonus);

                    if (isFixedBonus) {
                        $('#' + rowId + '_floating').prop("disabled", true).addClass("disabled-field");
                        $('#' + rowId + '_floating_ratio').prop("disabled", true).addClass("disabled-field");
                    } else {
                        $('#' + rowId + '_floating').prop("disabled", false).removeClass("disabled-field");
                        $('#' + rowId + '_floating_ratio').prop("disabled", false).removeClass("disabled-field");
                    }
                });
            }

            // 在数据加载完成后再初始化其他功能
            loadDataPromise.then(() => {
                setFixedBonusStates();
                loadDeptSettings(function() {
                    window.isInitializing = false;
                });

                // 初始化行点击效果
                initRowClickEffects();
            });

            // 初始设置固定奖金状态
            setFixedBonusStates();

            // 行点击效果函数
            function initRowClickEffects() {
                // 为所有数据行添加点击效果
                $(document).on('mousedown', '.data-row', function(e) {
                    // 避免在输入框、按钮等元素上触发
                    if ($(e.target).is('input, select, button, .btn, .delete-row')) {
                        return;
                    }

                    // 移除其他行的active类
                    $('.data-row').removeClass('active');

                    // 为当前行添加active类
                    $(this).addClass('active');

                    // 200ms后移除active类，创建点击效果
                    setTimeout(() => {
                        $(this).removeClass('active');
                    }, 200);
                });

                // 鼠标离开时确保移除active类
                $(document).on('mouseleave', '.data-row', function() {
                    $(this).removeClass('active');
                });
            }

            // 为所有现有行绑定事件
            $(".data-row").each(function() {
                const rowId = $(this).attr('id');
                if (rowId) {
                    bindRowEvents(rowId);
                }
            });



            // 监听表单提交
            $("#dataForm").on("submit", function(e) {
                // 表单提交处理
            });
            
            // 显示/隐藏参数区域
            $("#paramsBtn").click(function() {
                $("#paramsArea").slideToggle();
            });
            
            // 3秒后自动关闭提示消息
            setTimeout(function() {
                $('.floating-alert').addClass('hiding');
                setTimeout(function() {
                    $('.floating-alert').alert('close');
                }, 500);
            }, 3000);
            
            // 计算津贴按钮点击事件 - 这是唯一的计算触发点
            $("#calculateBtn").click(function() {

                // 确保所有禁用的输入框被临时启用以便计算
                $("input[disabled]").prop("disabled", false);

                // 标记所有行为编辑状态
                $(".data-row").addClass("editing");

                // 更新岗位总额
                updateDepartmentTotals();

                // 执行计算
                calculateAll();

                // 恢复固定奖金人员的禁用状态
                setFixedBonusStates();

                // 显示成功消息
                showTemporaryMessage("津贴计算完成！所有数据已重新计算。", "success");

                // 自动保存到本地存储
                saveDataToLocalStorage();
            });
            

            
            // 一键复制按钮
            $("#copyDataBtn").click(function() {
                copyTableData();
            });

            // 导出EXCEL按钮
            $("#exportExcelBtn").click(function() {
                exportToExcel();
            });

            // 保存到数据库按钮
            $("#saveToDbBtn").click(function() {
                saveToDatabase();
            });

            // 一键复制功能实现
            function copyTableData() {
                const employeeData = collectEmployeeData();
                if (employeeData.length === 0) {
                    alert('没有数据可复制');
                    return;
                }

                // 获取月份和日期信息
                const recordMonth = $("#recordMonth").val() || '';
                const recordDate = $("#recordDate").val() || '';

                // 格式化月份显示
                let monthText = '';
                if (recordMonth) {
                    const monthDate = new Date(recordMonth + '-01');
                    monthText = monthDate.getFullYear() + '年' + (monthDate.getMonth() + 1) + '月';
                }

                // 构建新格式的复制文本
                let copyText = `${monthText}津贴发放明细\n`;
                copyText += `日期：${recordDate}\n`;
                copyText += `------------------------\n`;

                // 保持页面显示顺序，不进行排序
                // 添加员工数据
                employeeData.forEach((emp) => {
                    copyText += `${emp.name}: ${emp.total}\n`;
                });

                // 复制到剪贴板
                navigator.clipboard.writeText(copyText).then(function() {
                    showTemporaryMessage("数据已复制到剪贴板", "success");
                }).catch(function(err) {
                    console.error('复制失败:', err);
                    // 降级方案：使用传统方法
                    const textArea = document.createElement("textarea");
                    textArea.value = copyText;
                    document.body.appendChild(textArea);
                    textArea.select();
                    document.execCommand('copy');
                    document.body.removeChild(textArea);
                    showTemporaryMessage("数据已复制到剪贴板", "success");
                });
            }

            // 导出EXCEL功能实现
            function exportToExcel() {
                const employeeData = collectEmployeeData();
                if (employeeData.length === 0) {
                    alert('没有数据可导出，请先添加员工数据');
                    return;
                }



                // 准备API请求数据
                const exportData = {
                    employees: employeeData.map(emp => ({
                        name: emp.name,
                        dept: emp.dept,
                        fixed: emp.fixed,
                        floating: emp.floating,
                        fixedActual: emp.fixed_actual,
                        floatingActual: emp.floating_actual,
                        days: emp.days,
                        deduction: emp.deduction,
                        personalBonus: emp.personal_bonus,
                        generalBonus: emp.general_bonus,
                        total: emp.total,
                        remark: emp.remark
                    })),
                    record_date: $("#recordDate").val() || '',
                    record_month: $("#recordMonth").val() || ''
                };



                // 显示加载状态
                const exportBtn = $("#exportExcelBtn");
                const originalText = exportBtn.html();
                exportBtn.html('<i class="fas fa-spinner fa-spin"></i> 导出中...').prop('disabled', true);

                // 使用fetch发送JSON请求到API
                fetch('../api/export_jintie_excel.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(exportData)
                })
                .then(response => {
                    if (!response.ok) {
                        return response.text().then(text => {
                            throw new Error(`导出失败 (${response.status}): ${text}`);
                        });
                    }
                    return response.blob();
                })
                .then(blob => {

                    // 创建下载链接
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.style.display = 'none';
                    a.href = url;
                    a.download = `津贴数据_${$("#recordMonth").val()}_${$("#recordDate").val()}.xlsx`;

                    document.body.appendChild(a);
                    a.click();
                    window.URL.revokeObjectURL(url);
                    document.body.removeChild(a);

                    showTemporaryMessage("EXCEL文件导出成功！", "success");
                })
                .catch(error => {
                    console.error('导出失败:', error);
                    alert('导出失败: ' + error.message);
                })
                .finally(() => {
                    // 恢复按钮状态
                    exportBtn.html(originalText).prop('disabled', false);
                });
            }

            // 保存到数据库功能实现
            function saveToDatabase() {
                const employeeData = collectEmployeeData();

                if (employeeData.length === 0) {
                    alert('没有数据可保存');
                    return;
                }

                // 确认保存
                if (!confirm('确定要保存当前数据到数据库吗？')) {
                    return;
                }

                // 创建表单提交保存
                const form = $("<form>")
                    .attr({
                        method: "post",
                        action: "jintie.php"
                    })
                    .append($("<input>").attr({
                        type: "hidden",
                        name: "save_data",
                        value: "1"
                    }))
                    .append($("<input>").attr({
                        type: "hidden",
                        name: "employee_data_json",
                        value: JSON.stringify(employeeData)
                    }))
                    .append($("<input>").attr({
                        type: "hidden",
                        name: "workers_count",
                        value: $("#workersCount").val()
                    }))
                    .append($("<input>").attr({
                        type: "hidden",
                        name: "jintie_total",
                        value: $("#jintieTotal").val()
                    }))
                    .append($("<input>").attr({
                        type: "hidden",
                        name: "distribution",
                        value: $("#distribution").val()
                    }))
                    .append($("<input>").attr({
                        type: "hidden",
                        name: "record_month",
                        value: $("#recordMonth").val()
                    }))
                    .append($("<input>").attr({
                        type: "hidden",
                        name: "record_date",
                        value: $("#recordDate").val()
                    }));

                // 提交表单
                $("body").append(form);
                form.submit();
            }
            
            // 应出勤天数变化时保存
            $("#workersCount").on("change", function() {
                $("#hiddenWorkersCount").val($("#workersCount").val());

                $.ajax({
                    url: "jintie.php",
                    type: "POST",
                    data: {
                        save_params: 1,
                        workers_count: $("#workersCount").val(),
                        distribution: $("#distribution").val()
                    },
                    dataType: "json"
                });

                saveDataToLocalStorage();
            });

            // 添加月度变化事件监听
            $("#recordMonth").on("change", function() {
                // 如果月度变化，自动更新日期的月份部分
                const selectedMonth = $(this).val();
                const currentDate = $("#recordDate").val();
                if (selectedMonth && currentDate) {
                    const day = currentDate.split('-')[2];
                    $("#recordDate").val(selectedMonth + '-' + day);
                }
            });

            // 添加日期变化事件监听
            $("#recordDate").on("change", function() {
                // 如果日期变化，自动更新月度
                const selectedDate = $(this).val();
                if (selectedDate) {
                    // 从日期中提取年月部分 (YYYY-MM-DD -> YYYY-MM)
                    const yearMonth = selectedDate.substring(0, 7);
                    $("#recordMonth").val(yearMonth);
                }
            });

            // 加载历史数据函数
            function loadHistoryData(recordDate) {
                // 设置数据恢复标记，禁用所有自动计算
                window.isRestoringData = true;

                $.ajax({
                    url: '../api/load_jintie_data.php',
                    method: 'GET',
                    data: { record_date: recordDate },
                    dataType: 'json',
                    success: function(response) {

                        if (response.success && response.data.employees && response.data.employees.length > 0) {
                            // 设置记录日期（优先使用API返回的日期）
                            const actualRecordDate = response.data.record_date || recordDate;
                            $("#recordDate").val(actualRecordDate);

                            // 设置月度字段（优先使用员工数据中的month_year）
                            let monthYear = '';
                            if (response.data.employees.length > 0 && response.data.employees[0].month_year) {
                                monthYear = response.data.employees[0].month_year;
                            } else {
                                // 如果没有month_year，从记录日期提取
                                const dateObj = new Date(actualRecordDate);
                                const year = dateObj.getFullYear();
                                const month = (dateObj.getMonth() + 1).toString().padStart(2, '0');
                                monthYear = `${year}-${month}`;
                            }
                            $("#recordMonth").val(monthYear);



                            // 加载参数 - 确保所有参数字段都使用历史数据
                            if (response.data.params) {
                                const workersCount = response.data.params.workers_count || 15;
                                const jintieTotal = response.data.params.jintie_total || 10000;
                                const distribution = response.data.params.distribution || 0;

                                // 更新显示字段
                                $("#workersCount").val(workersCount);
                                $("#jintieTotal").val(jintieTotal);
                                $("#distribution").val(distribution);

                                // 更新隐藏字段
                                $("#hiddenWorkersCount").val(workersCount);
                                $("#hiddenJintieTotal").val(jintieTotal);
                                $("#hiddenDistribution").val(distribution);


                            }

                            // 加载岗位设置信息
                            if (response.data.settings) {
                                // 更新全局变量
                                window.deptSettings = {
                                    jintie_total: parseFloat(response.data.settings.jintie_total || 10000),
                                    monitoring_base_amount: parseFloat(response.data.settings.monitoring_base_amount || 2500),
                                    cb26_base_amount: parseFloat(response.data.settings.cb26_base_amount || 400)
                                };

                                // 更新页面显示的津贴总额
                                $("#jintieTotal").val(Math.round(window.deptSettings.jintie_total));
                                $("#hiddenJintieTotal").val(Math.round(window.deptSettings.jintie_total));

                            } else {
                                // 如果没有设置数据，使用默认值
                                window.deptSettings = {
                                    jintie_total: 10000,
                                    monitoring_base_amount: 2500,
                                    cb26_base_amount: 400
                                };
                            }

                            // 先清空所有现有行的数据
                            clearAllDataRows();

                            // 加载员工数据到现有行
                            response.data.employees.forEach(function(employee, index) {
                                addEmployeeRow(employee, index + 1);
                            });

                            // 计算并更新统计数据
                            const stats = calculateDepartmentStats();
                            $("#distribution").val(stats.overall.total);
                            $("#productionTotal").text(stats.production.total);
                            $("#monitoringTotal").text(stats.monitoring.total);
                            $("#cb26Total").text(stats.cb26.total);
                            $("#bonusTotal").text(stats.overall.bonus);



                            // 重要：将历史数据强制保存到本地存储，覆盖原有数据
                            forceSaveToLocalStorage('历史数据加载');

                            // 清除数据恢复标记
                            window.isRestoringData = false;

                            // 历史数据加载完成后设置固定奖金状态
                            setFixedBonusStates();

                            alert('成功加载 ' + recordDate + ' 的数据，共 ' + response.data.employees.length + ' 条记录');
                        } else {
                            // 清除数据恢复标记
                            window.isRestoringData = false;
                            alert('该日期没有保存的数据');
                        }
                    },
                    error: function(xhr, status, error) {
                        // 清除数据恢复标记
                        window.isRestoringData = false;
                        alert('加载历史数据失败：' + error);
                    }
                });
            }

            // 添加员工行函数 - 修复以匹配当前表格结构
            function addEmployeeRow(employee, rowNumber) {
                // 注意：这个函数现在应该直接更新现有的表格行，而不是创建新行
                // 因为我们使用的是PHP生成的静态表格结构

                // 找到对应的行ID
                const existingRows = $(".data-row");
                if (rowNumber <= existingRows.length) {
                    const rowId = existingRows.eq(rowNumber - 1).attr('id');

                    // 更新现有行的数据 - 确保所有字段都使用历史数据
                    $('#' + rowId + '_name').val(employee.name || '');
                    $('#' + rowId + '_dept').val(employee.dept || '生产');
                    $('#' + rowId + '_fixed').val(parseFloat(employee.fixed) || 0);
                    $('#' + rowId + '_floating').val(parseFloat(employee.floating) || 0);
                    $('#' + rowId + '_fixed_actual').val(parseFloat(employee.fixed_actual) || 0);
                    $('#' + rowId + '_floating_actual').val(parseFloat(employee.floating_actual) || 0);
                    $('#' + rowId + '_days').val(parseInt(employee.days) || 0);
                    $('#' + rowId + '_deduction').val(parseFloat(employee.deduction) || 0);
                    $('#' + rowId + '_personal_bonus').val(parseFloat(employee.personal_bonus) || 0);
                    $('#' + rowId + '_general_bonus').val(parseFloat(employee.general_bonus) || 0);
                    $('#' + rowId + '_total').val(parseFloat(employee.total) || 0);
                    $('#' + rowId + '_remark').val(employee.remark || '');
                    $('#' + rowId + '_is_fixed_bonus').val(employee.is_fixed_bonus == '1' || employee.is_fixed_bonus === 1 ? '1' : '0');

                    // 直接使用历史数据中的浮动比例
                    $('#' + rowId + '_floating_ratio').val(parseFloat(employee.floating_ratio) || 1.0);

                    // 更新其他隐藏字段
                    $('#' + rowId + '_needs_calculation').val('0'); // 重置计算标记
                    $('#' + rowId + '_is_fixed_bonus_display').val(employee.is_fixed_bonus == '1' || employee.is_fixed_bonus === 1 ? '1' : '0');

                    // 计算并设置总奖励（个人奖励 + 总金额奖励）
                    const personalBonus = parseFloat(employee.personal_bonus) || 0;
                    const generalBonus = parseFloat(employee.general_bonus) || 0;
                    const totalBonus = personalBonus + generalBonus;
                    $('#' + rowId + '_bonus').val(totalBonus);



                    // 更新行的岗位样式
                    const row = $('#' + rowId);
                    row.removeClass("row-production row-monitoring row-cb26");
                    if(employee.dept === "生产") {
                        row.addClass("row-production");
                    } else if(employee.dept === "监控") {
                        row.addClass("row-monitoring");
                    } else if(employee.dept === "CB26") {
                        row.addClass("row-cb26");
                    }

                    // 根据固定奖金状态设置字段的禁用状态
                    const isFixedBonus = employee.is_fixed_bonus == '1' || employee.is_fixed_bonus === 1;
                    if (isFixedBonus) {
                        $('#' + rowId + '_floating').prop("disabled", true).addClass("disabled-field");
                        $('#' + rowId + '_floating_ratio').prop("disabled", true).addClass("disabled-field");
                    } else {
                        $('#' + rowId + '_floating').prop("disabled", false).removeClass("disabled-field");
                        $('#' + rowId + '_floating_ratio').prop("disabled", false).removeClass("disabled-field");
                    }
                }
            }

            function bindRowEvents(rowId) {
                $(`#${rowId} .delete-row`).on("click", function() {
                    if(confirm("确定要删除此记录吗？")) {
                        $(`#${rowId}`).remove();
                        saveDataToLocalStorage();
                    }
                });

                // 绑定固定奖金复选框事件
                $(`#${rowId}_is_fixed_bonus_checkbox`).on("change", function() {
                    const isChecked = $(this).is(':checked');
                    const name = $('#' + rowId + '_name').val();

                    // 更新隐藏字段
                    $(`#${rowId}_is_fixed_bonus`).val(isChecked ? '1' : '0');

                    // 根据固定奖金状态设置浮动比例和字段状态
                    if (isChecked) {
                        // 设置为固定奖金：浮动比例为0，禁用相关字段
                        $('#' + rowId + '_floating_ratio').val('0').prop("disabled", true).addClass("disabled-field");
                        $('#' + rowId + '_floating').prop("disabled", true).addClass("disabled-field");
                    } else {
                        // 取消固定奖金：浮动比例为1，启用相关字段
                        $('#' + rowId + '_floating_ratio').val('1').prop("disabled", false).removeClass("disabled-field");
                        $('#' + rowId + '_floating').prop("disabled", false).removeClass("disabled-field");
                    }

                    // 立即保存到本地存储
                    saveDataToLocalStorage();



                    // 同步更新到人员管理数据库
                    updatePersonnelFixedBonus(name, isChecked);
                });
            }

            // 奖励输入框变化时更新总奖励金额
            $(document).on("input", "[id$='_personal_bonus'], [id$='_general_bonus']", function() {
                const row = $(this).closest("tr");
                const rowId = row.attr('id');

                const personalBonus = parseInt($('#' + rowId + '_personal_bonus').val()) || 0;
                const generalBonus = parseInt($('#' + rowId + '_general_bonus').val()) || 0;
                const totalBonus = personalBonus + generalBonus;

                $('#' + rowId + '_bonus').val(totalBonus);
                saveDataToLocalStorage();
            });

            // 清除数据按钮
            $("#clearDataBtn").click(function() {
                if(confirm("确定要清除所有数据吗？")) {
                    // 强制清除本地存储
                    localStorage.removeItem('jintie_form_data');
                    localStorage.clear(); // 清除所有本地存储

                    // 禁用所有可能触发保存的事件
                    window.isClearing = true;

                    // 提交清除请求
                    $("<form>")
                        .attr({
                            method: "post",
                            action: "jintie.php"
                        })
                        .append($("<input>").attr({
                            type: "hidden",
                            name: "clear_data",
                            value: "1"
                        }))
                        .appendTo("body")
                        .submit();
                }
            });
            
            // 删除行
            $(".delete-row").on("click", function() {
                const id = $(this).data('id');
                if(confirm("确定要删除此记录吗？")) {
                    // 提交删除请求
                    $("<form>")
                        .attr({
                            method: "post",
                            action: "jintie.php"
                        })
                        .append($("<input>").attr({
                            type: "hidden",
                            name: "delete_id",
                            value: id
                        }))
                        .appendTo("body")
                        .submit();
                }
            });
            
            // 移除实时计算事件绑定
            $(".calc-input").off("input");
            $(document).off("input change", "[id$='_fixed'], [id$='_floating'], [id$='_days'], [id$='_floating_ratio'], [id$='_deduction'], [id$='_personal_bonus'], [id$='_general_bonus'], [id$='_dept'], [id$='_name'], [id$='_remark']");

            // 只保留自动保存功能
            $(document).on("input change", "[id$='_fixed'], [id$='_floating'], [id$='_days'], [id$='_floating_ratio'], [id$='_deduction'], [id$='_personal_bonus'], [id$='_general_bonus'], [id$='_dept'], [id$='_name'], [id$='_remark']", function() {
                $(this).closest("tr").addClass("value-changed");
                saveDataToLocalStorage();
            });
            
            // 岗位选择事件更新样式
            $(document).on("change", "[id$='_dept']", function() {
                const row = $(this).closest("tr");
                const rowId = row.attr('id');
                const dept = $(this).val();

                row.removeClass("row-production row-monitoring row-cb26");

                if(dept === "生产") {
                    row.addClass("row-production");
                } else if(dept === "监控") {
                    row.addClass("row-monitoring");
                } else if(dept === "CB26") {
                    row.addClass("row-cb26");
                }

                // 岗位变化后重新设置固定奖金状态
                setFixedBonusStates();

                saveDataToLocalStorage();
            });
            
            // 计算单行数据
            function calculateRow(row) {
                const rowId = row.attr('id'); // 获取行ID
                if (!rowId) {

                    return;
                }
                
                const name = $('#' + rowId + '_name').val();

                
                // 获取用户输入的值
                const fixed = parseFloat($('#' + rowId + '_fixed').val()) || 0;
                const floating = parseFloat($('#' + rowId + '_floating').val()) || 0; // 标准浮动部分值，不应被修改
                const days = parseInt($('#' + rowId + '_days').val()) || 0;
                
                // 浮动部分和固定奖金状态
                let floatingRatioVal = $('#' + rowId + '_floating_ratio').val();
                const floatingRatio = floatingRatioVal === "" ? 0 : (parseFloat(floatingRatioVal) || 0);
                const isFixedBonus = $('#' + rowId + '_is_fixed_bonus').val() === "1";
                // 检查是否处于编辑状态 - 避免无谓的再次调用
                const isEditing = row.hasClass("editing") && $('#' + rowId + '_needs_calculation').val() !== "1";
                
                // 获取扣罚和奖励
                const deduction = parseInt($('#' + rowId + '_deduction').val()) || 0;
                const personalBonus = parseInt($('#' + rowId + '_personal_bonus').val()) || 0;
                const generalBonus = parseInt($('#' + rowId + '_general_bonus').val()) || 0;
                const bonus = personalBonus + generalBonus;
                
                // 更新总奖励字段
                $('#' + rowId + '_bonus').val(bonus);
                
                // 计算应得固定部分（仅受请假天数影响）
                const workDays = parseInt($("#workersCount").val()) || 15;
                // 3、应得固定=固定部分-固定部分/应出勤天数*请假天数
                const fixedActual = days > 0 ? Math.floor(fixed - (fixed / workDays * days)) : fixed;
                
                // 获取当前部门
                const dept = $('#' + rowId + '_dept').val();
                
                // 应得浮动部分计算逻辑
                let floatingActual = 0;
                
                // 检查是否已经存在计算好的值，避免重复计算
                const existingFloatingActual = parseFloat($('#' + rowId + '_floating_actual').val());
                
                // 如果是固定奖金人员，直接使用浮动部分值
                if (isFixedBonus) {
                    floatingActual = floating;

                    
                    // 标记该行需要在calculateAll中特殊处理扣罚
                    $('#' + rowId + '_needs_calculation').val("1");
                } 
                // 生产岗位，且不是固定奖金人员，使用公式计算应得浮动
                else if (dept === "生产" && !isFixedBonus) {
                    // 使用新公式计算生产岗位的应得浮动部分
                    // 这里只获取个人相关部分，其他部分需要在calculateAll函数中计算

                    
                    // 临时保存值，等待calculateAll函数计算完整公式
                    $('#' + rowId + '_needs_calculation').val("1");
                    
                    // 注意：这里不设置floatingActual的值，等待calculateAll完成计算
                    // 使用placeholder值，将在calculateAll中替换
                    floatingActual = -1; // 标记为需要计算的特殊值
                }
                // 监控岗位，且不是固定奖金人员，使用新的监控岗位浮动计算公式
                else if (dept === "监控" && !isFixedBonus) {
                    // 临时保存值，等待calculateAll函数计算完整公式
                    $('#' + rowId + '_needs_calculation').val("1");
                    
                    // 标记为需要计算的特殊值，将在calculateAll中替换
                    floatingActual = -1;
                }
                // CB26岗位，使用特殊计算方式
                else if (dept === "CB26") {
                    // 修改：区分固定奖金和非固定奖金人员
                    if (isFixedBonus) {
                        // CB26岗位固定奖金人员的浮动部分为0
                        floatingActual = 0;

                    } else {
                        // CB26岗位非固定奖金人员需要标记为需要计算
                        // 将在calculateAll中计算总奖励分担
                        $('#' + rowId + '_needs_calculation').val("1");
                        
                        // 计算非固定人员总数
                        let nonFixedPersonCount = 0;
                        let totalGeneralBonus = 0;
                        
                        // 统计各岗位固定奖金人员数量
                        let fixedBonusCount = 0;
                        let totalCount = 0;
                        
                        $(".data-row").each(function() {
                            const isFixedB = $(this).find("[id$='_is_fixed_bonus']").val() === "1";
                            const genBonus = parseInt($(this).find("[id$='_general_bonus']").val()) || 0;
                            
                            totalCount++;
                            if (isFixedB) {
                                fixedBonusCount++;
                            }
                            
                            totalGeneralBonus += genBonus;
                        });
                        
                        nonFixedPersonCount = totalCount - fixedBonusCount;
                        
                        // 立即计算浮动部分值，确保值被设置
                        if (nonFixedPersonCount > 0 && totalGeneralBonus > 0) {
                            floatingActual = -Math.floor(totalGeneralBonus / nonFixedPersonCount);

                        } else {
                            floatingActual = 0;
                        }
                        
                        // 立即更新浮动部分值
                        $('#' + rowId + '_floating_actual').val(floatingActual);
                        
                        // 更新总金额
                        const total = fixedActual - deduction + bonus + floatingActual;
                        $('#' + rowId + '_total').val(Math.floor(total));
                        
                        // 更新显示值
                        updateDisplayValues(row);
                        

                        
                        // 重要：即使标记了需要计算，也确保已经设置了正确的值
                        return; // 直接返回，跳过后续设置，避免覆盖我们刚设置的值
                    }
                }
                // 其他情况，暂时仍使用用户输入的浮动部分值
                else {
                    floatingActual = floating;

                }
                
                // 更新应得固定部分
                $('#' + rowId + '_fixed_actual').val(Math.floor(fixedActual));
                
                // 仅当不是需要计算的岗位时才更新应得浮动部分和总金额
                if (!(dept === "生产" && !isFixedBonus) && !(dept === "监控" && !isFixedBonus)) {
                $('#' + rowId + '_floating_actual').val(Math.floor(floatingActual));
                    
                    // 计算总金额
                    let total = 0;
                    if (dept === "CB26") {
                        // CB26岗位总金额=应得固定-扣罚+奖励
                        total = fixedActual - deduction + bonus;

                    } else {
                        // 2、总金额=应得固定+应得浮动
                        total = fixedActual + floatingActual;
                    }
                $('#' + rowId + '_total').val(Math.floor(total));
                }
                
                // 计算完成
            }
            
            // 计算所有行
            function calculateAll() {
                // 如果页面正在初始化，跳过计算
                if (window.isInitializing) {
                    return;
                }

                // 如果正在恢复数据，跳过自动计算
                if (window.isRestoringData) {
                    return;
                }

                // 防止多次触发calculateAll - 使用计算锁
                if (window.isCalculating) {
                    return;
                }
                
                // 设置计算锁
                window.isCalculating = true;
                
                try {
                    // 先更新岗位总额，确保基础数据是正确的
                    updateDepartmentTotals();
                    
                    // 收集哪些行需要计算
                    const rowsToCalculate = [];
                    $(".data-row").each(function() {
                        const rowId = $(this).attr('id');
                        if (!rowId) return;
                        
                        const dept = $('#' + rowId + '_dept').val();
                        const isFixedBonus = $('#' + rowId + '_is_fixed_bonus').val() === "1";
                        const needsCalculation = $('#' + rowId + '_needs_calculation').val() === "1";
                        const name = $('#' + rowId + '_name').val();
                        
                        // 需要特殊计算的情况：
                        // 1. 生产岗位和监控岗位非固定奖金人员
                        // 2. 任何岗位的固定奖金人员（需要处理扣罚）
                        // 3. CB26岗位非固定奖金人员（需要计算总奖励分担）
                        if (((dept === "生产" || dept === "监控") && !isFixedBonus) || 
                            (dept === "CB26" && isFixedBonus) ||
                            (dept !== "CB26" && isFixedBonus)) {
                            rowsToCalculate.push({
                                row: $(this),
                                rowId,
                                name,
                                dept,
                                isFixedBonus,
                                needsCalculation
                            });
                        }
                    });
                    

                    
                    // 先计算每一行的基础数据
                    $(".data-row").each(function() {
                        calculateRow($(this));
                    });
                    
                    // 收集各岗位的统计数据，用于公式计算
                    let stats = {
                        // 总体统计
                        totalPersonCount: 0,                  // 所有岗位总人数
                        
                        // 生产岗位统计
                        productionPersonCount: 0,             // 生产岗位总人数
                        productionFixedBonusCount: 0,         // 生产岗位固定奖金人数
                        productionFixedActualTotal: 0,        // 生产岗位应得固定部分总额
                        productionFixedDeductionTotal: 0,     // 生产岗位应得固定部分扣除总额
                        productionDeductionTotal: 0,          // 生产岗位扣罚总额
                        productionPersonalBonusTotal: 0,      // 生产岗位个人奖励总额(不含总金额中的奖励)
                        productionFloatingRatioSum: 0,        // 生产岗位浮动比例总和(不含固定奖金人员)
                        
                        // CB26岗位统计
                        cb26PersonCount: 0,                   // CB26岗位总人数
                        cb26DeductionTotal: 0,                // CB26岗位扣罚总额
                        cb26FixedDeductionTotal: 0,           // CB26岗位应得固定部分扣除总额

                        
                        // 监控岗位统计
                        monitoringPersonCount: 0,             // 监控岗位总人数
                        monitoringFixedBonusCount: 0,         // 监控岗位固定奖金人数
                        monitoringFixedTotal: 0,              // 监控岗位固定部分总额
                        monitoringFixedDeductionTotal: 0,     // 监控岗位应得固定部分扣除总额
                        monitoringDeductionTotal: 0,          // 监控岗位扣罚总额
                        monitoringBonusTotal: 0,              // 监控岗位奖励总额(不含总金额中奖励)
                        monitoringFloatingRatioSum: 0         // 监控岗位浮动比例总和(不含固定奖金人员)
                    };
                    
                    // 第一次遍历：收集统计数据
                    $(".data-row").each(function() {
                        const rowId = $(this).attr('id');
                        if (!rowId) return;
                        
                        const dept = $('#' + rowId + '_dept').val();
                        const isFixedBonus = $('#' + rowId + '_is_fixed_bonus').val() === "1";
                        const fixedActual = parseFloat($('#' + rowId + '_fixed_actual').val()) || 0;
                        const fixed = parseFloat($('#' + rowId + '_fixed').val()) || 0;
                        const deduction = parseInt($('#' + rowId + '_deduction').val()) || 0;
                        const personalBonus = parseInt($('#' + rowId + '_personal_bonus').val()) || 0;
                        const generalBonus = parseInt($('#' + rowId + '_general_bonus').val()) || 0;
                        const bonus = personalBonus + generalBonus;
                        const floatingRatio = isFixedBonus ? 0 : (parseFloat($('#' + rowId + '_floating_ratio').val()) || 0);
                        const days = parseInt($('#' + rowId + '_days').val()) || 0;
                        
                        // 计算应得固定部分扣除金额（固定部分 - 应得固定部分）
                        // 完全不统计固定奖金人员的固定部分扣除，因为他们的浮动部分是固定的
                        // 固定奖金人员的扣除会直接反映在其fixedActual中，不需要重新分配
                        const fixedDeduction = isFixedBonus ? 0 : (fixed - fixedActual);
                        
                        // 增加总人数计数
                        stats.totalPersonCount++;
                        
                        // 按岗位分类统计
                        if (dept === "生产") {
                            stats.productionPersonCount++;
                            stats.productionFixedActualTotal += fixedActual; // 使用应得固定总额
                            stats.productionFixedDeductionTotal += fixedDeduction;
                            stats.productionDeductionTotal += deduction;
                            stats.productionPersonalBonusTotal += personalBonus;
                            
                            if (isFixedBonus) {
                                stats.productionFixedBonusCount++;
                            } else {
                                stats.productionFloatingRatioSum += floatingRatio;
                            }
                        } 
                        else if (dept === "监控") {
                            stats.monitoringPersonCount++;
                            stats.monitoringFixedTotal += fixedActual; // 使用应得固定总额，不是固定部分总额
                            stats.monitoringFixedDeductionTotal += fixedDeduction;
                            stats.monitoringDeductionTotal += deduction;
                            stats.monitoringBonusTotal += personalBonus;
                            
                            if (isFixedBonus) {
                                stats.monitoringFixedBonusCount++;
                            } else {
                                stats.monitoringFloatingRatioSum += floatingRatio;
                            }
                        }
                        else if (dept === "CB26") {
                            stats.cb26PersonCount++;
                            stats.cb26DeductionTotal += deduction;
                            stats.cb26FixedDeductionTotal += fixedDeduction;
                            // CB26的个人奖励由生产岗位承担，已由单独变量cb26PersonalBonusTotal统计
                        }
                    });
                    
                    // 获取生产岗位总额和监控岗位总额
                    const productionTotal = parseInt($("#productionTotal").text()) || 0;
                    const monitoringTotal = parseInt($("#monitoringTotal").text()) || 0;
                    
                    // 计算生产和CB26岗位的扣罚和奖励差额
                    // 修改：CB26岗位的奖励都来自生产岗位总额的扣除，所以这里不再减去CB26岗位的奖励
                    const productionAndCB26DeductionDiff = 
                        (stats.productionDeductionTotal + stats.cb26DeductionTotal) - 
                        stats.productionPersonalBonusTotal;
                    
                    // 计算分配系数部分
                    const nonFixedPersonCount = stats.totalPersonCount - stats.cb26PersonCount - stats.productionFixedBonusCount - stats.monitoringFixedBonusCount;
                    const productionNonFixedPersonCount = stats.productionPersonCount - stats.productionFixedBonusCount;
                    const monitoringNonFixedPersonCount = stats.monitoringPersonCount - stats.monitoringFixedBonusCount;
                    
                    // 避免除以零
                    if (productionNonFixedPersonCount === 0 || stats.productionFloatingRatioSum === 0) {

                        window.isCalculating = false; // 释放计算锁
                        return;
                    }
                    
                    // 获取所有CB26岗位的奖励数据
                    let cb26GeneralBonusTotal = 0;
                    let cb26PersonalBonusTotal = 0;
                    
                    $(".data-row").each(function() {
                        const dept = $(this).find("[id$='_dept']").val();
                        if (dept === "CB26") {
                            const generalBonus = parseInt($(this).find("[id$='_general_bonus']").val()) || 0;
                            const personalBonus = parseInt($(this).find("[id$='_personal_bonus']").val()) || 0;
                            cb26GeneralBonusTotal += generalBonus;
                            cb26PersonalBonusTotal += personalBonus;
                        }
                    });
                    
                    // 修改生产岗位浮动基数计算：
                    // 生产岗位浮动基数 = 生产岗位总额 - 生产岗位应得固定总额 + 生产岗位扣罚总额 + CB26岗位扣罚总额 - 生产岗位奖励总额
                    // 注意：应得固定部分扣除总额已经反映在"生产岗位总额 - 生产岗位应得固定总额"中，不应再重复计算
                    const productionFloatingBase = productionTotal - stats.productionFixedActualTotal + 
                        stats.productionDeductionTotal + stats.cb26DeductionTotal + stats.cb26FixedDeductionTotal - 
                        stats.productionPersonalBonusTotal;
                    

                    

                    
                    // 只计算需要计算的行，避免无谓的重复计算
                    for (const rowData of rowsToCalculate) {
                        const { row, rowId, name, dept, isFixedBonus } = rowData;
                        const fixedActual = parseFloat($('#' + rowId + '_fixed_actual').val()) || 0;
                        const floatingRatio = parseFloat($('#' + rowId + '_floating_ratio').val()) || 0;
                        const deduction = parseInt($('#' + rowId + '_deduction').val()) || 0;
                        const personalBonus = parseInt($('#' + rowId + '_personal_bonus').val()) || 0;
                        const generalBonus = parseInt($('#' + rowId + '_general_bonus').val()) || 0;
                        const bonus = personalBonus + generalBonus;
                        const floating = parseFloat($('#' + rowId + '_floating').val()) || 0; // 原始浮动部分值
                        
                        let floatingActual = 0;
                        
                        if (isFixedBonus) {
                            // 固定奖金人员的特殊处理
                            // 根据所属岗位计算扣罚，但保持浮动部分固定
                            
                            // 先获取固定的浮动部分值
                            floatingActual = floating;
                            
                            // 根据所属岗位处理扣罚和奖励
                            if (dept === "生产") {
                                // 生产岗位固定奖金人员扣罚处理
                                floatingActual = floatingActual - deduction + bonus;
                            }
                            else if (dept === "监控") {
                                // 监控岗位固定奖金人员扣罚处理
                                floatingActual = floatingActual - deduction + bonus;
                            }
                            else if (dept === "CB26") {
                                // CB26岗位固定奖金人员扣罚处理 - 由于CB26有特殊公式，这里不需要额外处理
                                // 但保持floatingActual = 0 以便后续CB26特殊处理
                                floatingActual = 0;
                            }
                            

                        }
                        else if (dept === "生产") {
                        // 计算个人应得浮动部分 = 浮动基数 / 浮动比例总和 * 个人浮动比例 - 扣罚 + 奖励
                            floatingActual = Math.floor(
                            (productionFloatingBase / stats.productionFloatingRatioSum * floatingRatio) - 
                            deduction + bonus
                        );
                        

                        } 
                        else if (dept === "监控") {
                            // 监控岗位应得浮动计算 - 修改为只在监控岗位内部分配扣罚和奖励
                            // 监控岗位应得浮动 = (监控岗位总额 - 监控岗位固定部分总额 + 监控岗位扣罚总额 - 监控岗位奖励总额)
                            // / 监控岗位浮动比例总和 * 个人浮动比例 - 扣罚 + 奖励
                            // 注意：应得固定部分扣除总额已经反映在总额计算中，不应再重复添加
                            
                            // 监控岗位浮动基数 - 移除分配系数，使扣罚和奖励只在监控岗位内分配
                            const monitoringFloatingBase = monitoringTotal - stats.monitoringFixedTotal + 
                                stats.monitoringDeductionTotal - 
                                stats.monitoringBonusTotal;
                                
                            // 防止除以零
                            if (stats.monitoringFloatingRatioSum > 0) {
                                floatingActual = Math.floor(
                                    (monitoringFloatingBase / stats.monitoringFloatingRatioSum * floatingRatio) - 
                                    deduction + bonus
                                );
                            } else {
                                floatingActual = 0;
                            }
                            

                        }
                        else if (dept === "CB26" && !isFixedBonus) {
                            // CB26岗位非固定奖金人员已在calculateRow中处理完成
                            // 这里仅记录日志并跳过处理

                            
                            // 清除计算标记
                            $('#' + rowId + '_needs_calculation').val("0");
                            
                            // 跳过后续通用处理，避免覆盖calculateRow中设置的值
                            continue;
                        }
                        
                        // 更新浮动部分
                        $('#' + rowId + '_floating_actual').val(floatingActual);
                        
                        // 更新总金额，需要根据岗位选择正确的计算公式
                        let total = 0;
                        if (dept === "CB26") {
                            // CB26岗位总金额=应得固定-扣罚+奖励+浮动部分(如果是负数，表示要扣除的总奖励份额)
                            total = fixedActual - deduction + bonus + floatingActual;
                        } else {
                            // 其他岗位总金额=应得固定+应得浮动
                            total = fixedActual + floatingActual;
                        }
                        
                        $('#' + rowId + '_total').val(Math.floor(total));
                        
                        // 清除计算标记
                        $('#' + rowId + '_needs_calculation').val("0");
                        
                        // 更新显示值
                        updateDisplayValues(row);
                    }
                    

                    
                    // 再次更新岗位总额，确保总额显示正确
                    updateDepartmentTotals();
                } finally {
                    // 确保释放计算锁
                    window.isCalculating = false;
                }
            }
            
            // 岗位金额设置按钮点击事件
            $("#deptAmountSettingsBtn").on("click", function() {
                // 加载当前设置
                loadDeptSettings();
                // 显示模态框
                $("#deptSettingsModal").modal("show");
            });

            // 保存岗位金额设置
            $("#saveDeptSettingsBtn").on("click", function() {
                saveDeptSettings();
            });

            $(document).off("input", "[id$='_floating']");
            
            // 浮动部分输入处理
            $(document).on("input", "[id$='_floating']", function() {
                saveDataToLocalStorage();
            });

            // 浮动比例输入处理
            $(document).on("input change", "[id$='_floating_ratio']", function() {
                const row = $(this).closest("tr");
                const rowId = row.attr('id');
                const name = $('#' + rowId + '_name').val();
                const isFixedBonus = $('#' + rowId + '_is_fixed_bonus').val() === "1";
                const newValue = $(this).val();

                saveDataToLocalStorage();
            });
            

            
            // 更新岗位总额
            function updateDepartmentTotals() {
                // 如果页面正在初始化，跳过更新
                if (window.isInitializing) {
                    return;
                }

                // 如果正在恢复数据，跳过自动计算
                if (window.isRestoringData) {
                    return;
                }

                // 获取基础参数
                const workDays = parseInt($("#workersCount").val()) || 15;
                const jintieTotal = parseInt($("#jintieTotal").val()) || 10000;



                // 计算基础津贴总额
                const baseJintieTotal = jintieTotal;
                
                // 获取各岗位人数和计算员工总金额总和
                let monitoringCount = 0;
                let cb26Count = 0;
                let productionCount = 0;
                let totalCount = 0;
                
                // 计算总奖励金额和CB26岗位奖励总额
                let generalBonusTotal = 0;
                let cb26BonusTotal = 0;
                let cb26PersonalBonusTotal = 0;
                
                // 计算所有员工实际总金额的总和
                let totalEmployeeAmount = 0;
                
                // 统计固定奖金人员数量
                let fixedBonusCount = 0;
                let monitoringFixedBonusCount = 0;
                let cb26FixedBonusCount = 0;
                let productionFixedBonusCount = 0;
                
                $(".data-row").each(function() {
                    const dept = $(this).find("[id$='_dept']").val();
                    const generalBonus = parseInt($(this).find("[id$='_general_bonus']").val()) || 0;
                    const personalBonus = parseInt($(this).find("[id$='_personal_bonus']").val()) || 0;
                    const total = parseInt($(this).find("[id$='_total']").val()) || 0;
                    const isFixedBonus = $(this).find("[id$='_is_fixed_bonus']").val() === "1";
                    
                    // 累加员工总金额
                    totalEmployeeAmount += total;
                    
                    generalBonusTotal += generalBonus;
                    totalCount++;
                    
                    if (isFixedBonus) {
                        fixedBonusCount++;
                        if(dept === "监控") {
                            monitoringFixedBonusCount++;
                        } else if(dept === "CB26") {
                            cb26FixedBonusCount++;
                        } else if(dept === "生产") {
                            productionFixedBonusCount++;
                        }
                    }
                    
                    if(dept === "监控") {
                        monitoringCount++;
                    } else if(dept === "CB26") {
                        cb26Count++;
                        // 统计CB26岗位的总奖励和个人奖励
                        cb26BonusTotal += generalBonus;
                        cb26PersonalBonusTotal += personalBonus;
                    } else if(dept === "生产") {
                        productionCount++;
                    }
                });
                
                // 从全局变量获取基础金额（如果未设置则使用默认值）
                const baseMonitoringTotal = window.deptSettings ? window.deptSettings.monitoring_base_amount : 2500;
                const baseCB26Total = window.deptSettings ? window.deptSettings.cb26_base_amount : 400;
                

                
                // 中间计算值
                let monitoringTotal = 0;
                let cb26Total = 0;
                let productionTotal = 0;
                let actualTotal = baseJintieTotal;
                
                // 先计算基本岗位总额，不考虑总奖励影响
                monitoringTotal = baseMonitoringTotal;
                cb26Total = baseCB26Total;
                productionTotal = baseJintieTotal - monitoringTotal - cb26Total;
                
                // 然后从所有岗位总额中扣除总奖励
                if(generalBonusTotal > 0) {
                    // 计算非固定人员数量
                    const nonFixedCount = totalCount - fixedBonusCount;
                    if(nonFixedCount > 0) {
                        // 监控岗位的非固定人员承担总奖励份额
                        const monitoringNonFixedCount = monitoringCount - monitoringFixedBonusCount;
                        if(monitoringNonFixedCount > 0) {
                            monitoringTotal -= Math.floor((generalBonusTotal / nonFixedCount) * monitoringNonFixedCount);
                        }
                        
                        // CB26岗位的非固定人员承担总奖励份额
                        const cb26NonFixedCount = cb26Count - cb26FixedBonusCount;
                        if(cb26NonFixedCount > 0) {
                            cb26Total -= Math.floor((generalBonusTotal / nonFixedCount) * cb26NonFixedCount);
                        }
                        
                        // 生产岗位的非固定人员承担总奖励份额
                        const productionNonFixedCount = productionCount - productionFixedBonusCount;
                        if(productionNonFixedCount > 0) {
                            productionTotal -= Math.floor((generalBonusTotal / nonFixedCount) * productionNonFixedCount);
                        }
                    }
                }
                
                // 额外从生产岗位总额中扣除CB26岗位的个人奖励总额
                productionTotal -= cb26PersonalBonusTotal;
                
                // 确保所有金额为整数且不小于0
                monitoringTotal = Math.max(0, monitoringTotal);
                cb26Total = Math.max(0, cb26Total);
                productionTotal = Math.max(0, productionTotal);
                
                // 计算岗位总额之和（用于日志显示）
                const deptTotalSum = monitoringTotal + cb26Total + productionTotal;
                

                
                // 更新显示
                $("#monitoringTotal").text(monitoringTotal);
                $("#cb26Total").text(cb26Total);
                $("#productionTotal").text(productionTotal);
                $("#bonusTotal").text(Math.floor(generalBonusTotal)); // 显示总金额奖励总额
                
                // 更新分配总额 - 修正为所有员工总金额之和
                $("#distribution").val(Math.floor(totalEmployeeAmount));
                

            }
            
            // 计算所有员工总金额的总和
            function calculateTotalEmployeeAmount() {
                let totalAmount = 0;
                $(".data-row").each(function() {
                    const total = parseInt($(this).find("[id$='_total']").val()) || 0;
                    totalAmount += total;
                });
                return totalAmount;
            }

            // 更新所有显示值
            function updateAllDisplayValues() {
                $(".data-row").each(function() {
                    updateDisplayValues($(this));
                });
            }

            // 更新单行显示值
            function updateDisplayValues(row) {
                const rowId = row.attr('id');
                if (!rowId) return;

                // 更新各字段的显示值
                const fields = ['dept', 'name', 'fixed', 'floating', 'days', 'fixed_actual',
                               'floating_ratio', 'deduction', 'personal_bonus', 'general_bonus',
                               'floating_actual', 'total', 'remark'];

                fields.forEach(function(field) {
                    const input = $('#' + rowId + '_' + field);
                    const displaySpan = row.find('td').eq(getColumnIndex(field)).find('.display-value');

                    if (input.length && displaySpan.length) {
                        let value = input.val();

                        // 格式化数字显示
                        if (['fixed', 'floating', 'fixed_actual', 'floating_actual', 'total'].includes(field)) {
                            value = parseInt(value) || 0;
                            displaySpan.text(value.toLocaleString());
                        } else {
                            displaySpan.text(value);
                        }
                    }
                });
            }

            // 获取列索引
            function getColumnIndex(field) {
                const columnMap = {
                    'dept': 1, 'name': 2, 'fixed': 3, 'floating': 4, 'days': 5,
                    'fixed_actual': 6, 'floating_ratio': 7, 'deduction': 8,
                    'personal_bonus': 9, 'general_bonus': 9, 'floating_actual': 10,
                    'total': 11, 'remark': 12
                };
                return columnMap[field] || 0;
            }

            // 数据持久化功能
            function saveDataToLocalStorage() {
                // 如果正在清除数据，不保存
                if (window.isClearing) {
                    return;
                }

                const formData = {
                    timestamp: new Date().getTime(),
                    recordMonth: $("#recordMonth").val(),
                    recordDate: $("#recordDate").val(),
                    workersCount: $("#workersCount").val(),
                    jintieTotal: $("#jintieTotal").val(),
                    distribution: $("#distribution").val(),
                    statistics: {
                        productionTotal: $("#productionTotal").text() || "0",
                        monitoringTotal: $("#monitoringTotal").text() || "0",
                        cb26Total: $("#cb26Total").text() || "0",
                        bonusTotal: $("#bonusTotal").text() || "0"
                    },
                    deptSettings: window.deptSettings || {
                        jintie_total: 10000,
                        monitoring_base_amount: 2500,
                        cb26_base_amount: 400
                    },
                    employees: collectEmployeeData()
                };

                localStorage.setItem('jintie_form_data', JSON.stringify(formData));
            }

            function loadDataFromLocalStorage() {
                const savedData = localStorage.getItem('jintie_form_data');
                if (!savedData) {
                    return false;
                }

                try {
                    // 设置恢复标记，禁用所有自动计算
                    window.isRestoringData = true;
                    
                    const formData = JSON.parse(savedData);
                    const dataSource = formData.source || '未知来源';
                    const timestamp = formData.timestamp || 0;
                    const timeStr = timestamp ? new Date(timestamp).toLocaleString() : '未知时间';



                    // 恢复基础参数
                    if (formData.recordMonth) $("#recordMonth").val(formData.recordMonth);
                    if (formData.recordDate) $("#recordDate").val(formData.recordDate);
                    if (formData.workersCount) $("#workersCount").val(formData.workersCount);
                    if (formData.jintieTotal) $("#jintieTotal").val(formData.jintieTotal);
                    if (formData.distribution) $("#distribution").val(formData.distribution);

                    // 先清空所有现有行的数据
                    clearAllDataRows();

                    // 恢复员工数据
                    formData.employees.forEach(function(employee, index) {
                        const existingRows = $(".data-row");
                        if (index < existingRows.length) {
                            const rowId = existingRows.eq(index).attr('id');

                            $('#' + rowId + '_name').val(employee.name || '');
                            $('#' + rowId + '_dept').val(employee.dept || '生产');
                            $('#' + rowId + '_fixed').val(employee.fixed || 0);
                            $('#' + rowId + '_floating').val(employee.floating || 0);
                            $('#' + rowId + '_fixed_actual').val(employee.fixed_actual || 0);
                            $('#' + rowId + '_floating_actual').val(employee.floating_actual || 0);
                            $('#' + rowId + '_days').val(employee.days || 0);
                            $('#' + rowId + '_deduction').val(employee.deduction || 0);
                            $('#' + rowId + '_personal_bonus').val(employee.personal_bonus || 0);
                            $('#' + rowId + '_general_bonus').val(employee.general_bonus || 0);
                            $('#' + rowId + '_total').val(employee.total || 0);
                            $('#' + rowId + '_remark').val(employee.remark || '');
                            $('#' + rowId + '_is_fixed_bonus').val(employee.is_fixed_bonus ? '1' : '0');

                            // 直接使用本地存储中的浮动比例
                            $('#' + rowId + '_floating_ratio').val(employee.floating_ratio || 1);

                            // 更新行的岗位样式
                            const row = $('#' + rowId);
                            row.removeClass("row-production row-monitoring row-cb26");
                            if(employee.dept === "生产") {
                                row.addClass("row-production");
                            } else if(employee.dept === "监控") {
                                row.addClass("row-monitoring");
                            } else if(employee.dept === "CB26") {
                                row.addClass("row-cb26");
                            }

                            // 根据固定奖金状态设置字段的禁用状态
                            const isFixedBonus = (employee.is_fixed_bonus == '1' || employee.is_fixed_bonus === 1 || employee.is_fixed_bonus === true);
                            if (isFixedBonus) {
                                $('#' + rowId + '_floating').prop("disabled", true).addClass("disabled-field");
                                $('#' + rowId + '_floating_ratio').prop("disabled", true).addClass("disabled-field");
                            } else {
                                $('#' + rowId + '_floating').prop("disabled", false).removeClass("disabled-field");
                                $('#' + rowId + '_floating_ratio').prop("disabled", false).removeClass("disabled-field");
                            }
                        }
                    });

                    // 恢复统计数据
                    if (formData.statistics) {
                        $("#productionTotal").text(formData.statistics.productionTotal || "0");
                        $("#monitoringTotal").text(formData.statistics.monitoringTotal || "0");
                        $("#cb26Total").text(formData.statistics.cb26Total || "0");
                        $("#bonusTotal").text(formData.statistics.bonusTotal || "0");
                    } else {
                        // 如果没有保存的统计数据，重新计算
                        setTimeout(function() {
                            refreshMainTableCalculations();
                        }, 100);
                    }

                    // 恢复岗位设置数据
                    if (formData.deptSettings) {
                        window.deptSettings = formData.deptSettings;
                        // 更新页面显示的津贴总额
                        $("#jintieTotal").val(Math.round(window.deptSettings.jintie_total));
                        $("#hiddenJintieTotal").val(Math.round(window.deptSettings.jintie_total));
                    } else {
                        // 如果没有保存的岗位设置，使用默认值
                        window.deptSettings = {
                            jintie_total: 10000,
                            monitoring_base_amount: 2500,
                            cb26_base_amount: 400
                        };
                    }

                    // 清除恢复标记
                    window.isRestoringData = false;

                    // 恢复数据后设置固定奖金状态
                    setFixedBonusStates();

                    return true;
                } catch (e) {
                    console.error('加载本地存储数据失败:', e);
                    window.isRestoringData = false;
                    return false;
                }
            }

            function clearLocalStorage() {
                localStorage.removeItem('jintie_form_data');
            }

            // 强制保存数据到本地存储（用于历史数据加载后覆盖）
            function forceSaveToLocalStorage(source = '手动操作') {
                const formData = {
                    timestamp: new Date().getTime(),
                    source: source,
                    recordMonth: $("#recordMonth").val(),
                    recordDate: $("#recordDate").val(),
                    workersCount: $("#workersCount").val(),
                    jintieTotal: $("#jintieTotal").val(),
                    distribution: $("#distribution").val(),
                    statistics: {
                        productionTotal: $("#productionTotal").text() || "0",
                        monitoringTotal: $("#monitoringTotal").text() || "0",
                        cb26Total: $("#cb26Total").text() || "0",
                        bonusTotal: $("#bonusTotal").text() || "0"
                    },
                    deptSettings: window.deptSettings || {
                        jintie_total: 10000,
                        monitoring_base_amount: 2500,
                        cb26_base_amount: 400
                    },
                    employees: collectEmployeeData()
                };

                localStorage.setItem('jintie_form_data', JSON.stringify(formData));
            }

            // 监听基础参数变化，自动保存
            $(document).on("input change", "#recordMonth, #recordDate, #workersCount", function() {
                saveDataToLocalStorage();
            });

            // 固定奖金状态变化时改变输入框状态
            $(document).on("change", "[id$='_is_fixed_bonus']", function() {
                const row = $(this).closest("tr");
                const rowId = row.attr('id');
                const isFixedBonus = $(this).val() === "1";
                const name = $('#' + rowId + '_name').val();
                const currentRatio = $('#' + rowId + '_floating_ratio').val();

                if(isFixedBonus) {
                    $('#' + rowId + '_floating').prop("disabled", true).addClass("disabled-field");
                    $('#' + rowId + '_floating_ratio').prop("disabled", true).addClass("disabled-field");
                } else {
                    $('#' + rowId + '_floating').prop("disabled", false).removeClass("disabled-field");
                    $('#' + rowId + '_floating_ratio').prop("disabled", false).removeClass("disabled-field");
                }

                saveDataToLocalStorage();
            });
            
            // 岗位选择过滤
            $("#deptSelector").on("change", function() {
                const selectedDept = $(this).val();
                
                // 显示/隐藏行，基于选定的岗位
                if(selectedDept === "all") {
                    $(".data-row").show();
                } else if(selectedDept === "production") {
                    $(".data-row.row-production").show();
                    $(".data-row.row-monitoring, .data-row.row-cb26").hide();
                } else if(selectedDept === "monitoring") {
                    $(".data-row.row-monitoring").show();
                    $(".data-row.row-production, .data-row.row-cb26").hide();
                } else if(selectedDept === "cb26") {
                    $(".data-row.row-cb26").show();
                    $(".data-row.row-production, .data-row.row-monitoring").hide();
                }
                


                // 保存到本地存储
                saveDataToLocalStorage();
            });
            




            

            

            
            // 显示临时消息
            function showTemporaryMessage(message, type = "success") {
                const alertHTML = `<div class="alert alert-${type} alert-dismissible fade show floating-alert">
                    ${message}
                    <button type="button" class="close" data-dismiss="alert">&times;</button>
                </div>`;
                
                // 如果已有消息，移除它
                $(".floating-alert").remove();
                
                // 添加新消息
                $("body").append(alertHTML);
                
                // 3秒后自动关闭
                setTimeout(function() {
                    $('.floating-alert').addClass('hiding');
                    setTimeout(function() {
                        $('.floating-alert').alert('close');
                    }, 500);
                }, 3000);
            }


            
            // 监听"提取历史"按钮
            $("#loadHistoryBtn").click(function() {
                // 初始化年份选择器
                initHistoryYearSelector();

                // 显示历史记录模态框
                $("#historyModal").modal('show');

                // 加载当前年份的历史记录
                loadHistoryList();
            });

            // 监听"补齐差额"按钮
            $("#balanceBtn").click(function() {
                // 计算差额
                const jintieTotal = parseInt($("#jintieTotal").val()) || 0;
                const distributionTotal = parseInt($("#distribution").val()) || 0;
                const difference = jintieTotal - distributionTotal;
                
                if (difference <= 0) {
                    alert("无需补齐！当前分配总额已等于或超过津贴总额。");
                    return;
                }
                
                // 更新模态框中的差额显示
                $("#balanceDifference").text(difference);
                
                // 清空员工选择下拉框
                $("#balanceEmployee").empty();
                
                // 添加所有员工到选择下拉框
                $(".data-row").each(function() {
                    const rowId = $(this).attr('id');
                    const name = $('#' + rowId + '_name').val();
                    
                    if (name) {
                        $("#balanceEmployee").append(
                            $("<option></option>")
                                .attr("value", rowId)
                                .text(name)
                        );
                    }
                });
                
                // 显示模态框
                $("#balanceModal").modal("show");
            });

            // 监听"确认补齐"按钮
            $("#confirmBalanceBtn").on("click", function() {
                const selectedRowId = $("#balanceEmployee").val();
                if (!selectedRowId) {
                    alert("请选择一个员工进行差额补齐");
                    return;
                }
                
                // 获取差额
                const jintieTotal = parseInt($("#jintieTotal").val()) || 0;
                const distributionTotal = parseInt($("#distribution").val()) || 0;
                const difference = jintieTotal - distributionTotal;
                
                // 获取所选员工的浮动部分
                const currentFloatingActual = parseFloat($('#' + selectedRowId + '_floating_actual').val()) || 0;
                const currentTotal = parseFloat($('#' + selectedRowId + '_total').val()) || 0;
                
                // 增加浮动部分和总金额
                const newFloatingActual = currentFloatingActual + difference;
                const newTotal = currentTotal + difference;
                
                // 更新员工数据
                $('#' + selectedRowId + '_floating_actual').val(Math.floor(newFloatingActual));
                $('#' + selectedRowId + '_total').val(Math.floor(newTotal));

                // 更新显示值
                updateDisplayValues($('#' + selectedRowId));

                // 更新分配总额
                $("#distribution").val(jintieTotal);

                // 重新计算统计信息
                refreshMainTableCalculations();

                // 自动保存到本地存储
                saveDataToLocalStorage();

                // 关闭模态框
                $("#balanceModal").modal("hide");

                // 显示成功消息
                showTemporaryMessage("差额已成功补齐并自动保存！", "success");
            });

            // 加载岗位金额设置
            function loadDeptSettings(callback) {
                $.ajax({
                    url: "../api/ajax_dept_settings.php",
                    type: "POST",
                    data: {
                        action: "get_dept_settings"
                    },
                    dataType: "json",
                    success: function(response) {
                        if (response.success && response.settings) {
                            // 保存到全局变量
                            window.deptSettings = response.settings;

                            // 更新页面显示的津贴总额（显示为整数）
                            $("#jintieTotal").val(Math.round(response.settings.jintie_total));
                            $("#hiddenJintieTotal").val(Math.round(response.settings.jintie_total));

                            // 更新模态框中的值（显示为整数）
                            $("#modalJintieTotal").val(Math.round(response.settings.jintie_total));
                            $("#modalMonitoringAmount").val(Math.round(response.settings.monitoring_base_amount));
                            $("#modalCb26Amount").val(Math.round(response.settings.cb26_base_amount));



                            // 执行回调函数
                            if (typeof callback === 'function') {
                                callback();
                            }
                        } else {
                            // 使用默认值
                            window.deptSettings = {
                                jintie_total: 10000,
                                monitoring_base_amount: 2500,
                                cb26_base_amount: 400
                            };

                            // 执行回调函数
                            if (typeof callback === 'function') {
                                callback();
                            }
                        }
                    },
                    error: function(xhr, status, error) {
                        // 使用默认值
                        window.deptSettings = {
                            jintie_total: 10000,
                            monitoring_base_amount: 2500,
                            cb26_base_amount: 400
                        };

                        // 执行回调函数
                        if (typeof callback === 'function') {
                            callback();
                        }
                    }
                });
            }

            // 保存岗位金额设置
            function saveDeptSettings() {
                const jintieTotal = $("#modalJintieTotal").val();
                const monitoringAmount = $("#modalMonitoringAmount").val();
                const cb26Amount = $("#modalCb26Amount").val();

                // 验证输入
                if (!jintieTotal || !monitoringAmount || !cb26Amount) {
                    alert("请填写所有必填项");
                    return;
                }

                if (parseFloat(jintieTotal) <= 0 || parseFloat(monitoringAmount) <= 0 || parseFloat(cb26Amount) <= 0) {
                    alert("所有金额必须大于0");
                    return;
                }

                // 禁用保存按钮，防止重复提交
                $("#saveDeptSettingsBtn").prop("disabled", true).text("保存中...");

                $.ajax({
                    url: "../api/ajax_dept_settings.php",
                    type: "POST",
                    data: {
                        action: "save_dept_settings",
                        jintie_total: jintieTotal,
                        monitoring_base_amount: monitoringAmount,
                        cb26_base_amount: cb26Amount
                    },
                    dataType: "json",
                    success: function(response) {
                        if (response.success) {
                            // 更新全局变量
                            window.deptSettings = {
                                jintie_total: parseFloat(jintieTotal),
                                monitoring_base_amount: parseFloat(monitoringAmount),
                                cb26_base_amount: parseFloat(cb26Amount)
                            };

                            // 更新页面显示的津贴总额（显示为整数）
                            $("#jintieTotal").val(Math.round(jintieTotal));
                            $("#hiddenJintieTotal").val(Math.round(jintieTotal));



                            // 显示成功消息
                            showTemporaryMessage("岗位金额设置已保存", "success");

                            // 关闭模态框
                            $("#deptSettingsModal").modal("hide");


                        } else {
                            alert("保存失败: " + (response.message || "未知错误"));
                        }
                    },
                    error: function(xhr, status, error) {
                        alert("保存失败，请重试");
                    },
                    complete: function() {
                        // 恢复保存按钮
                        $("#saveDeptSettingsBtn").prop("disabled", false).text("保存设置");
                    }
                });
            }



            // 刷新主页面表格计算的函数
            function refreshMainTableCalculations() {
                const stats = calculateDepartmentStats();

                // 更新统计显示
                $("#productionTotal").text(stats.production.total);
                $("#monitoringTotal").text(stats.monitoring.total);
                $("#cb26Total").text(stats.cb26.total);
                $("#bonusTotal").text(stats.overall.bonus);
                $("#distribution").val(stats.overall.total);

                // 保存更新后的统计数据到本地存储
                setTimeout(function() {
                    saveDataToLocalStorage();
                }, 50);
            }

            // 更新部门统计的函数（简化版本，主要统计已在refreshMainTableCalculations中处理）
            function updateDepartmentStats() {
                // 主要的部门统计已经在refreshMainTableCalculations中更新
                // 这里可以添加额外的部门统计逻辑，如果需要的话
            }

            // 可编辑预览表格中的输入框变化事件 - 实时同步数据
            $(document).on('change', '#editablePreviewTableBody input, #editablePreviewTableBody select', function() {
                // 延迟同步，避免频繁操作
                clearTimeout(window.syncTimeout);
                window.syncTimeout = setTimeout(function() {
                    syncEditablePreviewDataToStorage();
                }, 500);
            });

            // 确认保存按钮事件（原有的只读预览模态框）
            $("#confirmSaveBtn").on("click", function() {
                // 创建表单提交保存
                const form = $("<form>")
                    .attr({
                        method: "post",
                        action: "jintie.php"
                    })
                    .append($("<input>").attr({
                        type: "hidden",
                        name: "save_data",
                        value: "1"
                    }))
                    .append($("<input>").attr({
                        type: "hidden",
                        name: "employee_data_json",
                        value: $("#employee_data_json").val()
                    }))
                    .append($("<input>").attr({
                        type: "hidden",
                        name: "workers_count",
                        value: $("#workersCount").val()
                    }))
                    .append($("<input>").attr({
                        type: "hidden",
                        name: "jintie_total",
                        value: $("#jintieTotal").val()
                    }))
                    .append($("<input>").attr({
                        type: "hidden",
                        name: "distribution",
                        value: $("#distribution").val()
                    }))
                    .append($("<input>").attr({
                        type: "hidden",
                        name: "record_month",
                        value: $("#recordMonth").val()
                    }))
                    .append($("<input>").attr({
                        type: "hidden",
                        name: "record_date",
                        value: $("#recordDate").val()
                    }));
                
                // 提交表单
                $("body").append(form);
                form.submit();
            });





            // 初始化历史年份选择器
            function initHistoryYearSelector() {
                const currentYear = new Date().getFullYear();
                const startYear = 2025; // 可以根据需要调整起始年份

                $("#historyYear").empty();

                for (let year = currentYear; year >= startYear; year--) {
                    const option = `<option value="${year}" ${year === currentYear ? 'selected' : ''}>${year}年</option>`;
                    $("#historyYear").append(option);
                }
            }

            // 加载历史记录列表
            function loadHistoryList() {
                const selectedYear = $("#historyYear").val() || new Date().getFullYear();

                // 显示加载状态
                $("#historyLoading").show();
                $("#historyEmpty").hide();
                $("#historyTableBody").empty();

                $.ajax({
                    url: '../api/get_history_list.php',
                    method: 'GET',
                    data: { year: selectedYear },
                    dataType: 'json',
                    success: function(response) {
                        $("#historyLoading").hide();

                        if (response.success && response.data && response.data.length > 0) {
                            displayHistoryList(response.data);
                        } else {
                            $("#historyEmpty").show();
                        }
                    },
                    error: function(xhr, status, error) {
                        $("#historyLoading").hide();
                        $("#historyEmpty").show();
                        alert('加载历史记录失败：' + error);
                    }
                });
            }

            // 显示历史记录列表
            function displayHistoryList(historyData) {
                $("#historyTableBody").empty();

                historyData.forEach(function(record, index) {
                    const monthText = record.month_year ? formatMonthYear(record.month_year) : '未知';
                    const row = `
                        <tr>
                            <td>${index + 1}</td>
                            <td>${monthText}</td>
                            <td>${record.record_date}</td>
                            <td>${record.record_count}</td>
                            <td>${record.jintie_total ? Math.round(record.jintie_total) : '-'}</td>
                            <td>
                                <button type="button" class="btn btn-primary btn-sm load-history-record"
                                        data-date="${record.record_date}">
                                    <i class="fas fa-download"></i> 加载
                                </button>
                                <button type="button" class="btn btn-danger btn-sm delete-history-record ml-1"
                                        data-date="${record.record_date}">
                                    <i class="fas fa-trash"></i> 删除
                                </button>
                            </td>
                        </tr>
                    `;
                    $("#historyTableBody").append(row);
                });
            }

            // 格式化年月显示
            function formatMonthYear(monthYear) {
                if (!monthYear) return '未知';
                const parts = monthYear.split('-');
                if (parts.length >= 2) {
                    return `${parts[0]}年${parseInt(parts[1])}月`;
                }
                return monthYear;
            }

            // 监听年份选择变化
            $("#historyYear").on("change", function() {
                loadHistoryList();
            });

            // 监听刷新历史列表按钮
            $("#refreshHistoryBtn").click(function() {
                loadHistoryList();
            });

            // 监听加载历史记录按钮
            $(document).on("click", ".load-history-record", function() {
                const recordDate = $(this).data('date');

                if (confirm(`确认加载 ${recordDate} 的历史数据吗？\n\n当前数据将被覆盖。`)) {
                    $("#historyModal").modal('hide');
                    loadHistoryData(recordDate);
                }
            });

            // 监听删除历史记录按钮
            $(document).on("click", ".delete-history-record", function() {
                const recordDate = $(this).data('date');
                const row = $(this).closest('tr');

                if (confirm(`确认删除 ${recordDate} 的历史记录吗？\n\n此操作不可恢复！`)) {
                    // 显示删除中状态
                    const deleteBtn = $(this);
                    const originalText = deleteBtn.html();
                    deleteBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> 删除中...');

                    // 发送删除请求
                    $.ajax({
                        url: '../api/delete_history_record.php',
                        method: 'POST',
                        data: { record_date: recordDate },
                        dataType: 'json',
                        success: function(response) {
                            if (response.success) {
                                // 删除成功，移除表格行
                                row.fadeOut(300, function() {
                                    $(this).remove();

                                    // 检查是否还有记录
                                    if ($("#historyTableBody tr").length === 0) {
                                        $("#historyEmpty").show();
                                    }
                                });

                                showTemporaryMessage(`成功删除 ${recordDate} 的历史记录`, "success");
                            } else {
                                alert('删除失败：' + (response.message || '未知错误'));
                                deleteBtn.prop('disabled', false).html(originalText);
                            }
                        },
                        error: function(xhr, status, error) {
                            alert('删除失败：' + error);
                            deleteBtn.prop('disabled', false).html(originalText);
                        }
                    });
                }
            });



            // 平衡模态框关闭按钮事件
            $(document).on('click', '#balanceModal .close, #balanceModal [data-dismiss="modal"]', function() {
                $('#balanceModal').modal('hide');
            });
            
            // 确保Bootstrap 4和Bootstrap 5模态框关闭功能兼容
            $(document).on('click', '[data-dismiss="modal"]', function() {
                $(this).closest('.modal').modal('hide');
            });

            $(document).on('click', '[data-bs-dismiss="modal"]', function() {
                $(this).closest('.modal').modal('hide');
            });

            // 同步更新人员管理数据库中的固定奖金状态
            function updatePersonnelFixedBonus(name, isFixed) {

                $.ajax({
                    url: 'jintie.php',
                    type: 'POST',
                    data: {
                        action: 'update_personnel_fixed_bonus',
                        name: name,
                        is_fixed_bonus: isFixed ? 1 : 0
                    },
                    dataType: 'json',
                    success: function(response) {
                        if (response.success) {
                            // 同步成功
                        } else {
                            alert('同步失败: ' + response.message);
                        }
                    },
                    error: function(xhr, status, error) {
                        alert('同步人员管理数据失败，请刷新页面重试');
                    }
                });
            }

            // 页面加载完成后的初始化已在上面的setFixedBonusStates中处理

        });
    </script>
    
    <!-- 补齐差额模态框 -->
    <div class="modal fade" id="balanceModal" tabindex="-1" role="dialog" aria-hidden="true">
        <div class="modal-dialog modal-sm" role="document">
            <div class="modal-content">
                <div class="modal-header py-2">
                    <h6 class="modal-title mb-0"><i class="fas fa-balance-scale mr-2"></i>补齐差额</h6>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body py-3">
                    <div class="text-center mb-4">
                        <p class="mb-2 text-muted">差额金额</p>
                        <div class="d-flex align-items-center justify-content-center">
                            <h3 id="balanceDifference" class="font-weight-bold text-danger mb-0 mr-1">0</h3>
                            <span class="text-muted">元</span>
                        </div>
                    </div>
                    <div class="form-group mb-3">
                        <label for="balanceEmployee" class="form-label mb-2">选择员工：</label>
                        <select id="balanceEmployee" class="form-control form-control-sm">
                            <!-- 动态添加员工选项 -->
                        </select>
                    </div>
                </div>
                <div class="modal-footer py-2">
                    <button type="button" class="btn btn-secondary btn-sm" data-dismiss="modal">
                        <i class="fas fa-times mr-1"></i>取消
                    </button>
                    <button type="button" class="btn btn-primary btn-sm" id="confirmBalanceBtn">
                        <i class="fas fa-check mr-1"></i>确认补齐
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 岗位金额设置模态框 -->
    <div class="modal fade" id="deptSettingsModal" tabindex="-1" role="dialog" aria-hidden="true">
        <div class="modal-dialog modal-sm" role="document">
            <div class="modal-content">
                <div class="modal-header py-2">
                    <h6 class="modal-title mb-0">岗位金额设置</h6>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body py-3">
                    <form id="deptSettingsForm">
                        <div class="form-group mb-3">
                            <label for="modalJintieTotal" class="form-label mb-1">津贴总额</label>
                            <div class="d-flex justify-content-center">
                                <input type="number" class="form-control form-control-sm text-center" style="width: 120px;" id="modalJintieTotal" name="jintie_total" step="1" min="0" required>
                            </div>
                        </div>
                        <div class="form-group mb-3">
                            <label for="modalMonitoringAmount" class="form-label mb-1">监控岗位基础金额</label>
                            <div class="d-flex justify-content-center">
                                <input type="number" class="form-control form-control-sm text-center" style="width: 120px;" id="modalMonitoringAmount" name="monitoring_base_amount" step="1" min="0" required>
                            </div>
                        </div>
                        <div class="form-group mb-2">
                            <label for="modalCb26Amount" class="form-label mb-1">CB26岗位基础金额</label>
                            <div class="d-flex justify-content-center">
                                <input type="number" class="form-control form-control-sm text-center" style="width: 120px;" id="modalCb26Amount" name="cb26_base_amount" step="1" min="0" required>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer py-2">
                    <button type="button" class="btn btn-secondary btn-sm" data-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary btn-sm" id="saveDeptSettingsBtn">保存设置</button>
                </div>
            </div>
        </div>
    </div>





    <!-- 历史记录模态框 -->
    <div class="modal fade" id="historyModal" tabindex="-1" role="dialog" aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">选择历史记录</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="historyYear">选择年份：</label>
                            <select id="historyYear" class="form-control">
                                <!-- 动态生成年份选项 -->
                            </select>
                        </div>
                        <div class="col-md-6">
                            <button type="button" id="refreshHistoryBtn" class="btn btn-primary" style="margin-top: 32px;">
                                <i class="fas fa-sync-alt"></i> 刷新列表
                            </button>
                        </div>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover">
                            <thead class="thead-light apple-thead">
                                <tr>
                                    <th width="50" style="background: #f9f9f9 !important; color: #1d1d1f !important; font-weight: 600 !important; font-size: 15px !important; padding: 16px 20px !important; text-align: center !important; border-color: rgba(0, 0, 0, 0.04) !important;">序号</th>
                                    <th width="100" style="background: #f9f9f9 !important; color: #1d1d1f !important; font-weight: 600 !important; font-size: 15px !important; padding: 16px 20px !important; text-align: center !important; border-color: rgba(0, 0, 0, 0.04) !important;">年月</th>
                                    <th width="120" style="background: #f9f9f9 !important; color: #1d1d1f !important; font-weight: 600 !important; font-size: 15px !important; padding: 16px 20px !important; text-align: center !important; border-color: rgba(0, 0, 0, 0.04) !important;">日期</th>
                                    <th width="80" style="background: #f9f9f9 !important; color: #1d1d1f !important; font-weight: 600 !important; font-size: 15px !important; padding: 16px 20px !important; text-align: center !important; border-color: rgba(0, 0, 0, 0.04) !important;">记录数</th>
                                    <th width="100" style="background: #f9f9f9 !important; color: #1d1d1f !important; font-weight: 600 !important; font-size: 15px !important; padding: 16px 20px !important; text-align: center !important; border-color: rgba(0, 0, 0, 0.04) !important;">津贴总额</th>
                                    <th width="140" style="background: #f9f9f9 !important; color: #1d1d1f !important; font-weight: 600 !important; font-size: 15px !important; padding: 16px 20px !important; text-align: center !important; border-color: rgba(0, 0, 0, 0.04) !important;">操作</th>
                                </tr>
                            </thead>
                            <tbody id="historyTableBody">
                                <!-- 动态生成历史记录 -->
                            </tbody>
                        </table>
                    </div>
                    <div id="historyLoading" class="text-center" style="display: none;">
                        <i class="fas fa-spinner fa-spin"></i> 加载中...
                    </div>
                    <div id="historyEmpty" class="text-center text-muted" style="display: none;">
                        暂无历史记录
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

</div>
</body>
</html>