<?php
require '../includes/config.php';

// 确保 $pdo 变量在全局作用域中可用
global $pdo;

// 检查用户是否已登录且为管理员
requireRole('admin');

// 处理AJAX请求
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['ajax'])) {
    header('Content-Type: application/json');
    
    try {
        $action = $_POST['action'] ?? '';
        
        switch($action) {
            case 'get_schedules':
                $year = $_POST['year'] ?? date('Y');
                $month = $_POST['month'] ?? '';
                $station = $_POST['station'] ?? '';

                $where = ["year = ?"];
                $params = [$year];

                if (!empty($month)) {
                    $where[] = "month = ?";
                    $params[] = $month;
                }

                if (!empty($station)) {
                    $where[] = "station LIKE ?";
                    $params[] = "%{$station}%";
                }

                $whereClause = implode(' AND ', $where);

                // 获取数据（升序排列，不分页）
                $stmt = $pdo->prepare("
                    SELECT id, year, month, station,
                           DATE_FORMAT(start_date, '%Y-%m-%d') as start_date,
                           DATE_FORMAT(end_date, '%Y-%m-%d') as end_date,
                           days,
                           DATE_FORMAT(created_at, '%Y-%m-%d %H:%i') as created_at
                    FROM shift_schedules
                    WHERE {$whereClause}
                    ORDER BY year ASC, month ASC, start_date ASC
                ");
                $stmt->execute($params);
                $schedules = $stmt->fetchAll();

                echo json_encode([
                    'status' => 'success',
                    'data' => $schedules
                ]);
                break;
                
            case 'add_schedule':
                $required = ['station', 'start_date', 'end_date'];
                foreach ($required as $field) {
                    if (empty($_POST[$field])) {
                        die(json_encode(['status'=>'error','message'=>"请填写{$field}"]));
                    }
                }
                
                try {
                    $start = new DateTime($_POST['start_date']);
                    $end = new DateTime($_POST['end_date']);
                    
                    if ($start > $end) {
                        die(json_encode(['status'=>'error','message'=>'开始日期不能晚于结束日期']));
                    }
                    
                    $days = $start->diff($end)->days + 1;
                } catch(Exception $e) {
                    die(json_encode(['status'=>'error','message'=>'日期格式错误']));
                }
                
                $stmt = $pdo->prepare("
                    INSERT INTO shift_schedules (year, month, station, start_date, end_date, days)
                    VALUES (?, ?, ?, ?, ?, ?)
                ");
                
                $stmt->execute([
                    $start->format('Y'),
                    $start->format('n'),
                    $_POST['station'],
                    $_POST['start_date'],
                    $_POST['end_date'],
                    $days
                ]);
                
                logAction('添加排班', 'shift_schedules', $_POST);
                echo json_encode(['status' => 'success', 'message' => '排班添加成功']);
                break;
                
            case 'update_schedule':
                $id = $_POST['id'] ?? '';
                if (!is_numeric($id)) {
                    die(json_encode(['status'=>'error','message'=>'无效的ID']));
                }
                
                $required = ['station', 'start_date', 'end_date'];
                foreach ($required as $field) {
                    if (empty($_POST[$field])) {
                        die(json_encode(['status'=>'error','message'=>"请填写{$field}"]));
                    }
                }
                
                try {
                    $start = new DateTime($_POST['start_date']);
                    $end = new DateTime($_POST['end_date']);
                    
                    if ($start > $end) {
                        die(json_encode(['status'=>'error','message'=>'开始日期不能晚于结束日期']));
                    }
                    
                    $days = $start->diff($end)->days + 1;
                } catch(Exception $e) {
                    die(json_encode(['status'=>'error','message'=>'日期格式错误']));
                }
                
                $stmt = $pdo->prepare("
                    UPDATE shift_schedules SET
                        year = ?, month = ?, station = ?, 
                        start_date = ?, end_date = ?, days = ?,
                        updated_at = NOW()
                    WHERE id = ?
                ");
                
                $stmt->execute([
                    $start->format('Y'),
                    $start->format('n'),
                    $_POST['station'],
                    $_POST['start_date'],
                    $_POST['end_date'],
                    $days,
                    $id
                ]);
                
                logAction('更新排班', 'shift_schedules', $_POST);
                echo json_encode(['status' => 'success', 'message' => '排班更新成功']);
                break;
                
            case 'delete_schedule':
                $id = $_POST['id'] ?? '';
                if (!is_numeric($id)) {
                    die(json_encode(['status'=>'error','message'=>'无效的ID']));
                }
                
                $stmt = $pdo->prepare("DELETE FROM shift_schedules WHERE id = ?");
                $stmt->execute([$id]);
                
                logAction('删除排班', 'shift_schedules', ['id' => $id]);
                echo json_encode(['status' => 'success', 'message' => '排班删除成功']);
                break;
                
            case 'get_remarks':
                $year = $_POST['year'] ?? date('Y');
                
                $stmt = $pdo->prepare("SELECT month, remark FROM shift_month_remarks WHERE year = ? ORDER BY month");
                $stmt->execute([$year]);
                $remarks = [];
                while ($row = $stmt->fetch()) {
                    $remarks[$row['month']] = $row['remark'];
                }
                
                echo json_encode(['status' => 'success', 'remarks' => $remarks]);
                break;
                
            case 'update_remark':
                $year = $_POST['year'] ?? '';
                $month = $_POST['month'] ?? '';
                $remark = $_POST['remark'] ?? '';

                if (!$year || !$month) {
                    die(json_encode(['status'=>'error','message'=>'年份和月份不能为空']));
                }

                $stmt = $pdo->prepare("
                    INSERT INTO shift_month_remarks (year, month, remark)
                    VALUES (?, ?, ?)
                    ON DUPLICATE KEY UPDATE remark = VALUES(remark), updated_at = NOW()
                ");
                $stmt->execute([$year, $month, $remark]);

                logAction('更新月度备注', 'shift_month_remarks', $_POST);
                echo json_encode(['status' => 'success', 'message' => '备注保存成功']);
                break;

            case 'get_analysis_data':
                $year = $_POST['year'] ?? date('Y');
                $compareYear = $_POST['compare_year'] ?? '';
                $analysisMode = $_POST['analysis_mode'] ?? 'single';
                $multiYearRange = $_POST['multi_year_range'] ?? '';
                $startYear = $_POST['start_year'] ?? '';
                $endYear = $_POST['end_year'] ?? '';

                try {
                    // 获取指定年份的基础统计数据
                    $stmt = $pdo->prepare("
                        SELECT
                            COUNT(*) as total_schedules,
                            SUM(days) as total_days,
                            SUM(CASE WHEN station LIKE '%一站%' THEN days ELSE 0 END) as station1_days,
                            SUM(CASE WHEN station LIKE '%二站%' THEN days ELSE 0 END) as station2_days,
                            AVG(days) as avg_days,
                            MAX(days) as max_days,
                            MIN(days) as min_days
                        FROM shift_schedules
                        WHERE year = ?
                    ");
                    $stmt->execute([$year]);
                    $yearStats = $stmt->fetch();

                    // 如果有对比年份，获取对比年份的数据
                    $compareStats = null;
                    $compareMonthlyData = null;
                    if (!empty($compareYear) && $compareYear != $year) {
                        $stmt->execute([$compareYear]);
                        $compareStats = $stmt->fetch();

                        // 获取对比年份的月度数据
                        $stmt = $pdo->prepare("
                            SELECT
                                month,
                                station,
                                SUM(days) as total_days
                            FROM shift_schedules
                            WHERE year = ?
                            GROUP BY month, station
                            ORDER BY month
                        ");
                        $stmt->execute([$compareYear]);
                        $compareMonthlyRaw = $stmt->fetchAll();

                        $compareStation1Monthly = array_fill(0, 12, 0);
                        $compareStation2Monthly = array_fill(0, 12, 0);

                        foreach ($compareMonthlyRaw as $data) {
                            $monthIndex = $data['month'] - 1;
                            if (strpos($data['station'], '一站') !== false) {
                                $compareStation1Monthly[$monthIndex] = (int)$data['total_days'];
                            } elseif (strpos($data['station'], '二站') !== false) {
                                $compareStation2Monthly[$monthIndex] = (int)$data['total_days'];
                            }
                        }

                        $compareMonthlyData = [
                            'station1' => $compareStation1Monthly,
                            'station2' => $compareStation2Monthly
                        ];
                    }

                    // 获取月度数据
                    $stmt = $pdo->prepare("
                        SELECT
                            month,
                            station,
                            SUM(days) as total_days
                        FROM shift_schedules
                        WHERE year = ?
                        GROUP BY month, station
                        ORDER BY month
                    ");
                    $stmt->execute([$year]);
                    $monthlyData = $stmt->fetchAll();

                    // 处理月度数据
                    $station1Monthly = array_fill(0, 12, 0);
                    $station2Monthly = array_fill(0, 12, 0);

                    foreach ($monthlyData as $data) {
                        $monthIndex = $data['month'] - 1;
                        if (strpos($data['station'], '一站') !== false) {
                            $station1Monthly[$monthIndex] = (int)$data['total_days'];
                        } elseif (strpos($data['station'], '二站') !== false) {
                            $station2Monthly[$monthIndex] = (int)$data['total_days'];
                        }
                    }

                    // 获取班次时长分布
                    $stmt = $pdo->prepare("
                        SELECT
                            CASE
                                WHEN days <= 3 THEN '1-3天'
                                WHEN days <= 7 THEN '4-7天'
                                WHEN days <= 15 THEN '8-15天'
                                WHEN days <= 30 THEN '16-30天'
                                ELSE '30天以上'
                            END as duration_range,
                            COUNT(*) as count
                        FROM shift_schedules
                        WHERE year = ?
                        GROUP BY duration_range
                        ORDER BY MIN(days)
                    ");
                    $stmt->execute([$year]);
                    $durationData = $stmt->fetchAll();

                    // 处理时长分布数据
                    $durationLabels = ['1-3天', '4-7天', '8-15天', '16-30天', '30天以上'];
                    $durationValues = array_fill(0, 5, 0);

                    foreach ($durationData as $data) {
                        $index = array_search($data['duration_range'], $durationLabels);
                        if ($index !== false) {
                            $durationValues[$index] = (int)$data['count'];
                        }
                    }

                    // 获取多年度趋势数据
                    $stmt = $pdo->prepare("
                        SELECT
                            year,
                            SUM(days) as total_days,
                            SUM(CASE WHEN station LIKE '%一站%' THEN days ELSE 0 END) as station1_days,
                            SUM(CASE WHEN station LIKE '%二站%' THEN days ELSE 0 END) as station2_days
                        FROM shift_schedules
                        WHERE year >= ? - 6 AND year <= ?
                        GROUP BY year
                        ORDER BY year
                    ");
                    $stmt->execute([$year, $year]);
                    $yearlyTrend = $stmt->fetchAll();

                    $trendYears = [];
                    $trendTotalDays = [];
                    $trendStation1Days = [];
                    $trendStation2Days = [];

                    foreach ($yearlyTrend as $trend) {
                        $trendYears[] = $trend['year'];
                        $trendTotalDays[] = (int)$trend['total_days'];
                        $trendStation1Days[] = (int)$trend['station1_days'];
                        $trendStation2Days[] = (int)$trend['station2_days'];
                    }

                    // 获取最长和最短班次详情
                    $stmt = $pdo->prepare("
                        SELECT station, days, DATE_FORMAT(start_date, '%Y年%m月') as period
                        FROM shift_schedules
                        WHERE year = ? AND days = ?
                        LIMIT 1
                    ");

                    // 最长班次
                    $stmt->execute([$year, $yearStats['max_days']]);
                    $longestShift = $stmt->fetch();

                    // 最短班次
                    $stmt->execute([$year, $yearStats['min_days']]);
                    $shortestShift = $stmt->fetch();

                    // 构建详细统计数据
                    $detailStats = [
                        [
                            'item' => '最长单次班次',
                            'value' => $yearStats['max_days'] . '天',
                            'description' => ($longestShift ? $longestShift['period'] . $longestShift['station'] . '班次' : '暂无数据')
                        ],
                        [
                            'item' => '最短单次班次',
                            'value' => $yearStats['min_days'] . '天',
                            'description' => ($shortestShift ? $shortestShift['period'] . $shortestShift['station'] . '班次' : '暂无数据')
                        ],
                        [
                            'item' => '平均班次天数',
                            'value' => round($yearStats['avg_days'], 1) . '天',
                            'description' => '单次出海的平均天数'
                        ],
                        [
                            'item' => '一站占比',
                            'value' => ($yearStats['total_days'] > 0 ? round($yearStats['station1_days']/$yearStats['total_days']*100, 1) : 0) . '%',
                            'description' => '一站天数占总天数比例'
                        ],
                        [
                            'item' => '二站占比',
                            'value' => ($yearStats['total_days'] > 0 ? round($yearStats['station2_days']/$yearStats['total_days']*100, 1) : 0) . '%',
                            'description' => '二站天数占总天数比例'
                        ],
                        [
                            'item' => '年度班次总数',
                            'value' => $yearStats['total_schedules'] . '次',
                            'description' => '全年总共出海班次数'
                        ]
                    ];

                    // 处理多年份对比
                    $multiYearData = null;
                    if ($analysisMode === 'multi') {
                        $years = [];

                        if ($multiYearRange === 'custom' && $startYear && $endYear) {
                            for ($y = $startYear; $y <= $endYear; $y++) {
                                $years[] = $y;
                            }
                        } else {
                            $rangeCount = (int)$multiYearRange ?: 3;
                            for ($i = 0; $i < $rangeCount; $i++) {
                                $years[] = $year - $i;
                            }
                            $years = array_reverse($years);
                        }

                        // 获取多年份数据
                        $stmt = $pdo->prepare("
                            SELECT
                                year,
                                COUNT(*) as total_schedules,
                                SUM(days) as total_days,
                                SUM(CASE WHEN station LIKE '%一站%' THEN days ELSE 0 END) as station1_days,
                                SUM(CASE WHEN station LIKE '%二站%' THEN days ELSE 0 END) as station2_days,
                                AVG(days) as avg_days
                            FROM shift_schedules
                            WHERE year IN (" . implode(',', array_fill(0, count($years), '?')) . ")
                            GROUP BY year
                            ORDER BY year
                        ");
                        $stmt->execute($years);
                        $multiYearStats = $stmt->fetchAll();

                        // 获取多年份月度数据
                        $stmt = $pdo->prepare("
                            SELECT
                                year,
                                month,
                                station,
                                SUM(days) as total_days
                            FROM shift_schedules
                            WHERE year IN (" . implode(',', array_fill(0, count($years), '?')) . ")
                            GROUP BY year, month, station
                            ORDER BY year, month
                        ");
                        $stmt->execute($years);
                        $multiMonthlyData = $stmt->fetchAll();

                        $multiYearData = [
                            'years' => $years,
                            'stats' => $multiYearStats,
                            'monthlyData' => $multiMonthlyData
                        ];
                    }

                    $analysisData = [
                        'analysisMode' => $analysisMode,
                        'year' => $year,
                        'compareYear' => $compareYear,
                        'totalDays' => (int)$yearStats['total_days'],
                        'station1Days' => (int)$yearStats['station1_days'],
                        'station2Days' => (int)$yearStats['station2_days'],
                        'avgDays' => round($yearStats['avg_days'], 1),
                        'monthlyData' => [
                            'station1' => $station1Monthly,
                            'station2' => $station2Monthly
                        ],
                        'stationData' => [
                            'station1Days' => (int)$yearStats['station1_days'],
                            'station2Days' => (int)$yearStats['station2_days']
                        ],
                        'durationData' => [
                            'labels' => $durationLabels,
                            'values' => $durationValues
                        ],
                        'yearlyTrend' => [
                            'years' => $trendYears,
                            'totalDays' => $trendTotalDays,
                            'station1Days' => $trendStation1Days,
                            'station2Days' => $trendStation2Days
                        ],
                        'detailStats' => $detailStats,
                        'multiYearData' => $multiYearData
                    ];

                    // 如果有对比数据，添加到返回结果中
                    if ($compareStats && $compareMonthlyData) {
                        $analysisData['compareData'] = [
                            'totalDays' => (int)$compareStats['total_days'],
                            'station1Days' => (int)$compareStats['station1_days'],
                            'station2Days' => (int)$compareStats['station2_days'],
                            'avgDays' => round($compareStats['avg_days'], 1),
                            'monthlyData' => $compareMonthlyData,
                            'totalSchedules' => (int)$compareStats['total_schedules']
                        ];

                        // 添加对比统计到详细信息中
                        $totalDaysDiff = (int)$yearStats['total_days'] - (int)$compareStats['total_days'];
                        $station1Diff = (int)$yearStats['station1_days'] - (int)$compareStats['station1_days'];
                        $station2Diff = (int)$yearStats['station2_days'] - (int)$compareStats['station2_days'];

                        $analysisData['detailStats'][] = [
                            'item' => '总天数对比',
                            'value' => ($totalDaysDiff >= 0 ? '+' : '') . $totalDaysDiff . '天',
                            'description' => "相比{$compareYear}年" . ($totalDaysDiff >= 0 ? '增加' : '减少') . abs($totalDaysDiff) . '天'
                        ];

                        $analysisData['detailStats'][] = [
                            'item' => '一站天数对比',
                            'value' => ($station1Diff >= 0 ? '+' : '') . $station1Diff . '天',
                            'description' => "相比{$compareYear}年" . ($station1Diff >= 0 ? '增加' : '减少') . abs($station1Diff) . '天'
                        ];

                        $analysisData['detailStats'][] = [
                            'item' => '二站天数对比',
                            'value' => ($station2Diff >= 0 ? '+' : '') . $station2Diff . '天',
                            'description' => "相比{$compareYear}年" . ($station2Diff >= 0 ? '增加' : '减少') . abs($station2Diff) . '天'
                        ];
                    }

                    echo json_encode(['status' => 'success', 'data' => $analysisData]);

                } catch (PDOException $e) {
                    error_log("Analysis Data Error: " . $e->getMessage());
                    echo json_encode(['status'=>'error','message'=>'获取分析数据失败，请稍后重试']);
                }
                break;
                
            default:
                echo json_encode(['status'=>'error','message'=>'无效的操作']);
        }
        
    } catch (PDOException $e) {
        error_log("Shift Schedule Error: " . $e->getMessage());
        echo json_encode(['status'=>'error','message'=>'操作失败，请稍后重试']);
    } catch (Exception $e) {
        error_log("Shift Schedule Error: " . $e->getMessage());
        echo json_encode(['status'=>'error','message'=>$e->getMessage()]);
    }
    
    exit();
}

// 获取统计数据
try {
    $currentYear = date('Y');
    
    // 今年排班总数
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM shift_schedules WHERE year = ?");
    $stmt->execute([$currentYear]);
    $currentYearCount = $stmt->fetch()['count'];
    
    // 本月排班数
    $currentMonth = date('n');
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM shift_schedules WHERE year = ? AND month = ?");
    $stmt->execute([$currentYear, $currentMonth]);
    $currentMonthCount = $stmt->fetch()['count'];
    
    // 总排班数
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM shift_schedules");
    $totalCount = $stmt->fetch()['count'];
    
    // 有备注的月份数
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM shift_month_remarks WHERE remark IS NOT NULL AND remark != ''");
    $remarksCount = $stmt->fetch()['count'];
    
} catch (PDOException $e) {
    $currentYearCount = $currentMonthCount = $totalCount = $remarksCount = 0;
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>倒班时间管理</title>

    <!-- 预加载字体文件 -->
    <link rel="preload" href="../assets/webfonts/fa-solid-900.woff2" as="font" type="font/woff2" crossorigin="anonymous">

    <link rel="stylesheet" href="../assets/css/bootstrap.min.css">
    <link href="../assets/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f2f2f7;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            color: #1d1d1f;
            line-height: 1.47059;
            font-weight: 400;
        }

        .container-fluid {
            padding: 24px;
        }
        
        .page-header {
            background: #ffffff;
            border-radius: 12px;
            padding: 24px 32px;
            margin-bottom: 24px;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.04);
            border: 1px solid rgba(0, 0, 0, 0.04);
        }

        .page-header h1 {
            font-size: 28px;
            font-weight: 600;
            color: #1d1d1f;
            margin: 0;
            letter-spacing: -0.003em;
        }
        
        .stats-container {
            margin-bottom: 24px;
        }
        
        .stat-card {
            background: #ffffff;
            border-radius: 16px;
            padding: 28px 24px;
            text-align: center;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);
            border: 1px solid rgba(0, 0, 0, 0.04);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .stat-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 32px rgba(0, 0, 0, 0.12);
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
        }

        .stat-number {
            font-size: 32px;
            font-weight: 700;
            color: #1d1d1f;
            margin-bottom: 8px;
            line-height: 1.1;
        }

        .stat-label {
            color: #86868b;
            font-size: 14px;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .main-card {
            background: #ffffff;
            border-radius: 12px;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.04);
            border: 1px solid rgba(0, 0, 0, 0.04);
            overflow: hidden;
            margin-bottom: 24px;
        }

        .card-header {
            background: #ffffff;
            border-bottom: 1px solid rgba(0, 0, 0, 0.04);
            padding: 20px 24px;
            font-size: 17px;
            font-weight: 600;
            color: #1d1d1f;
            letter-spacing: -0.022em;
        }

        .filter-section {
            background: #f9f9f9;
            padding: 20px 24px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.04);
        }
        
        .btn-apple {
            background: #007aff;
            border: none;
            border-radius: 8px;
            color: white;
            font-weight: 500;
            padding: 8px 16px;
            transition: all 0.2s ease;
        }
        
        .btn-apple:hover {
            background: #0056b3;
            color: white;
            transform: translateY(-1px);
        }
        
        .btn-apple-success {
            background: #34c759;
            border: none;
            border-radius: 8px;
            color: white;
            font-weight: 500;
            padding: 8px 16px;
            transition: all 0.2s ease;
        }
        
        .btn-apple-success:hover {
            background: #28a745;
            color: white;
            transform: translateY(-1px);
        }
        
        .btn-apple-danger {
            background: #ff3b30;
            border: none;
            border-radius: 8px;
            color: white;
            font-weight: 500;
            padding: 8px 16px;
            transition: all 0.2s ease;
        }
        
        .btn-apple-danger:hover {
            background: #dc3545;
            color: white;
            transform: translateY(-1px);
        }
        
        .form-control {
            border-radius: 8px;
            border: 1px solid #d1d1d6;
            padding: 8px 12px;
        }

        .form-control:focus {
            border-color: #007aff;
            box-shadow: 0 0 0 0.2rem rgba(0, 122, 255, 0.25);
        }

        /* 筛选框样式优化 */
        .filter-section .form-control {
            font-size: 14px;
        }

        /* 表格文字显示优化 */
        .table th {
            font-size: 14px;
        }

        .table td {
            font-size: 13px;
        }
        
        .table {
            margin-bottom: 0;
        }
        
        .table th {
            background-color: #f8f9fa;
            border-top: none;
            font-weight: 600;
            color: #495057;
            text-align: center;
            vertical-align: middle;
            padding: 16px 8px;
            white-space: nowrap;
            overflow: visible;
            text-overflow: clip;
            max-width: none;
        }

        .table td {
            text-align: center;
            vertical-align: middle;
            padding: 12px 8px;
            border-top: 1px solid #e9ecef;
            white-space: nowrap;
            overflow: visible;
            text-overflow: clip;
            max-width: none;
        }
        
        .table tbody tr:hover {
            background-color: #f8f9fa;
        }
        
        .badge-station {
            color: white;
            padding: 4px 8px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 500;
        }

        .badge-station-1 {
            background-color: #007aff; /* 蓝色 - 一站 */
        }

        .badge-station-2 {
            background-color: #34c759; /* 绿色 - 二站 */
        }

        .station-radios {
            padding: 8px 0;
        }

        .custom-control-inline {
            margin-right: 2rem;
        }

        .custom-radio .custom-control-label::before {
            border-radius: 50%;
        }

        .custom-radio .custom-control-input:checked ~ .custom-control-label::before {
            background-color: #007aff;
            border-color: #007aff;
        }

        .btn-outline-success {
            color: #34c759;
            border-color: #34c759;
        }

        .btn-outline-success:hover {
            background-color: #34c759;
            border-color: #34c759;
            color: white;
            transform: translateY(-1px);
        }

        .stat-item {
            padding: 8px 0;
        }

        .stat-item h5 {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 4px;
        }

        .card-footer {
            border-top: 1px solid #e9ecef;
            background-color: #f8f9fa !important;
            transition: all 0.3s ease;
        }

        .stat-item h5 {
            transition: all 0.3s ease;
        }

        .stat-item:hover h5 {
            transform: scale(1.1);
        }

        .btn-outline-primary {
            color: #007aff;
            border-color: #007aff;
        }

        .btn-outline-primary:hover {
            background-color: #007aff;
            border-color: #007aff;
            color: white;
            transform: translateY(-1px);
        }

        .btn-outline-info {
            color: #17a2b8;
            border-color: #17a2b8;
        }

        .btn-outline-info:hover {
            background-color: #17a2b8;
            border-color: #17a2b8;
            color: white;
            transform: translateY(-1px);
        }

        .btn-outline-warning {
            color: #ff9500;
            border-color: #ff9500;
        }

        .btn-outline-warning:hover {
            background-color: #ff9500;
            border-color: #ff9500;
            color: white;
            transform: translateY(-1px);
        }

        .custom-file-label {
            border-radius: 8px;
        }

        .custom-file-label::after {
            content: "浏览";
            background-color: #007aff;
            border-color: #007aff;
            color: white;
            border-radius: 0 8px 8px 0;
        }

        .custom-file-input:focus ~ .custom-file-label {
            border-color: #007aff;
            box-shadow: 0 0 0 0.2rem rgba(0, 122, 255, 0.25);
        }

        .custom-file-label:hover::after {
            background-color: #0056b3;
            border-color: #0056b3;
        }

        .custom-file {
            position: relative;
            display: inline-block;
            width: 100%;
            height: calc(1.5em + 0.75rem + 2px);
            margin-bottom: 0;
        }

        .progress {
            border-radius: 8px;
        }

        .progress-bar {
            border-radius: 8px;
        }
        
        .pagination {
            margin-top: 20px;
            justify-content: center;
        }
        
        .page-link {
            border-radius: 6px;
            margin: 0 2px;
            border: 1px solid #d1d1d6;
            color: #007aff;
        }
        
        .page-link:hover {
            background-color: #007aff;
            border-color: #007aff;
            color: white;
        }
        
        .page-item.active .page-link {
            background-color: #007aff;
            border-color: #007aff;
        }
        
        .remarks-section {
            margin-top: 24px;
        }
        
        .remark-item {
            background: white;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 12px;
            border: 1px solid #e9ecef;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }
        
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #666;
        }
        
        .empty-state i {
            font-size: 48px;
            margin-bottom: 16px;
            color: #ccc;
        }

        /* 响应式优化 */
        @media (max-width: 768px) {
            .filter-section .col-auto {
                margin-bottom: 10px;
                min-width: auto !important;
            }

            .filter-section .form-control {
                width: auto !important;
                min-width: 90px;
            }

            .table th, .table td {
                font-size: 12px;
                padding: 8px 4px;
            }

            .btn {
                font-size: 12px;
                padding: 6px 12px;
            }
        }

        /* 确保表格内容不会被截断 */
        .table-responsive {
            overflow-x: auto;
        }

        .table {
            min-width: 800px;
        }

        /* 数据分析模态框样式 */
        #analysisModal .modal-dialog {
            max-width: 95%;
        }

        #analysisModal .stat-card {
            margin-bottom: 0;
        }

        #analysisModal .main-card {
            margin-bottom: 0;
        }

        #analysisModal canvas {
            max-height: 300px;
        }

        /* 确保出海天数统计表格区域完全显示 */
        #analysisModal .main-card {
            overflow: visible;
        }

        #analysisModal .card-body {
            overflow: visible;
            height: auto;
        }

        .badge {
            font-size: 0.9em;
            padding: 0.4em 0.8em;
        }

        /* 对比信息样式 */
        .stat-compare {
            font-size: 12px;
            margin-top: 4px;
            font-weight: 500;
        }

        .stat-compare.positive {
            color: #28a745;
        }

        .stat-compare.negative {
            color: #dc3545;
        }

        .stat-compare.neutral {
            color: #6c757d;
        }

        /* 出海天数统计表格样式 */
        .stats-table-container {
            width: 100%;
            height: 100%;
        }

        .stats-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 16px;
        }

        /* 单年和双年模式 - 固定较小高度 */
        .stats-table.single-mode {
            height: 180px;
        }

        .stats-table.compare-mode {
            height: 240px;
        }

        /* 多年模式 - 使用容器完整高度 */
        .stats-table.multi-mode {
            height: 100%;
        }

        .stats-table th {
            background-color: #f8f9fa;
            font-weight: 600;
            text-align: center;
            border-bottom: 2px solid #dee2e6;
            font-size: 16px;
            vertical-align: middle;
        }

        .stats-table td {
            text-align: center;
            border-bottom: 1px solid #dee2e6;
            vertical-align: middle;
        }

        .stats-table .table-active {
            background-color: rgba(0, 122, 255, 0.1);
        }

        .stats-table .badge {
            font-size: 14px;
            padding: 8px 12px;
        }

        /* 确保表格容器不产生滚动条 */
        #daysStatsTable tbody {
            overflow: visible;
        }

        /* 移除可能的高度限制 */
        .main-card .card-body {
            overflow: visible;
            max-height: none;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .container-fluid {
                padding: 16px;
            }

            .page-header {
                padding: 20px 24px;
            }

            .page-header h1 {
                font-size: 24px;
            }

            .stat-card {
                padding: 20px 16px;
                margin-bottom: 16px;
            }

            .stat-number {
                font-size: 28px;
            }

            .main-card {
                margin-bottom: 16px;
            }

            .card-header,
            .filter-section {
                padding: 16px 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <!-- 页面标题 -->
        <div class="page-header">
            <h1><i class="fas fa-clock mr-3"></i>倒班时间管理</h1>
            <p class="mb-0 mt-2" style="font-size: 17px; color: #86868b; font-weight: 400;">管理倒班排班计划和月度备注信息</p>
        </div>
        <!-- 统计卡片 -->
        <div class="stats-container">
            <div class="row">
                <div class="col-md-3 mb-3">
                    <div class="stat-card">
                        <div class="stat-number text-primary"><?= $currentYearCount ?></div>
                        <div class="stat-label">今年排班</div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="stat-card">
                        <div class="stat-number text-success"><?= $currentMonthCount ?></div>
                        <div class="stat-label">本月排班</div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="stat-card">
                        <div class="stat-number text-info"><?= $totalCount ?></div>
                        <div class="stat-label">总排班数</div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="stat-card">
                        <div class="stat-number text-warning"><?= $remarksCount ?></div>
                        <div class="stat-label">备注月份</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="main-card">
            <!-- 筛选和操作区域 -->
            <div class="filter-section">
                <div class="row align-items-end">
                    <div class="col-auto" style="min-width: 100px;">
                        <label class="form-label">年份</label>
                        <select class="form-control" id="filterYear" style="width: 100px;">
                            <?php for($y = date('Y') + 1; $y >= 2019; $y--): ?>
                                <option value="<?= $y ?>" <?= $y == date('Y') ? 'selected' : '' ?>><?= $y ?>年</option>
                            <?php endfor; ?>
                        </select>
                    </div>
                    <div class="col-auto" style="min-width: 120px;">
                        <label class="form-label">月份</label>
                        <select class="form-control" id="filterMonth" style="width: 120px;">
                            <option value="">全部月份</option>
                            <?php for($m = 1; $m <= 12; $m++): ?>
                                <option value="<?= $m ?>"><?= $m ?>月</option>
                            <?php endfor; ?>
                        </select>
                    </div>
                    <div class="col-auto" style="min-width: 120px;">
                        <label class="form-label">班次</label>
                        <select class="form-control" id="filterStation" style="width: 120px;">
                            <option value="">全部班次</option>
                            <option value="一站">一站</option>
                            <option value="二站">二站</option>
                        </select>
                    </div>
                    <div class="col">
                        <button type="button" class="btn btn-apple" onclick="loadSchedules()">
                            <i class="fas fa-search mr-1"></i>查询
                        </button>
                        <button type="button" class="btn btn-apple-success ml-2" onclick="showAddModal()">
                            <i class="fas fa-plus mr-1"></i>添加排班
                        </button>
                        <button type="button" class="btn btn-outline-success ml-2" onclick="exportExcel()">
                            <i class="fas fa-file-excel mr-1"></i>导出Excel
                        </button>
                        <button type="button" class="btn btn-outline-primary ml-2" onclick="showImportModal()">
                            <i class="fas fa-file-import mr-1"></i>导入Excel
                        </button>
                        <button type="button" class="btn btn-outline-info ml-2" onclick="downloadTemplate()">
                            <i class="fas fa-download mr-1"></i>下载模板
                        </button>
                        <button type="button" class="btn btn-outline-warning ml-2" onclick="showAnalysisModal()">
                            <i class="fas fa-chart-bar mr-1"></i>数据分析
                        </button>
                        <button type="button" class="btn btn-outline-secondary ml-2 float-right" onclick="toggleRemarks()">
                            <i class="fas fa-sticky-note mr-1"></i>月度备注
                        </button>
                    </div>
                </div>
            </div>

            <!-- 排班列表 -->
            <div class="card-body p-0">
                <div id="schedulesList">
                    <div class="loading">
                        <i class="fas fa-spinner fa-spin"></i> 加载中...
                    </div>
                </div>
            </div>

            <!-- 统计信息 -->
            <div class="card-footer bg-light" id="statisticsSection" style="display: none;">
                <div class="row text-center">
                    <div class="col-md-3">
                        <div class="stat-item">
                            <h5 class="mb-1 text-primary" id="station1Days">0</h5>
                            <small class="text-muted">一站总天数</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-item">
                            <h5 class="mb-1 text-success" id="station2Days">0</h5>
                            <small class="text-muted">二站总天数</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-item">
                            <h5 class="mb-1 text-info" id="totalDays">0</h5>
                            <small class="text-muted">总天数</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-item">
                            <h5 class="mb-1 text-warning" id="totalSchedules">0</h5>
                            <small class="text-muted">排班次数</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 月度备注区域 -->
        <div class="remarks-section" id="remarksSection" style="display: none;">
            <div class="main-card">
                <div class="card-header">
                    <i class="fas fa-sticky-note mr-2"></i>月度备注管理
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <label class="form-label">选择年份</label>
                            <select class="form-control" id="remarkYear" onchange="loadRemarks()">
                                <?php for($y = date('Y') + 1; $y >= 2019; $y--): ?>
                                    <option value="<?= $y ?>" <?= $y == date('Y') ? 'selected' : '' ?>><?= $y ?>年</option>
                                <?php endfor; ?>
                            </select>
                        </div>
                    </div>
                    <div id="remarksList" class="mt-3">
                        <div class="loading">
                            <i class="fas fa-spinner fa-spin"></i> 加载备注中...
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 添加/编辑排班模态框 -->
    <div class="modal fade" id="scheduleModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-clock mr-2"></i>
                        <span id="modalTitle">添加排班</span>
                    </h5>
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                </div>
                <form id="scheduleForm">
                    <div class="modal-body">
                        <input type="hidden" id="scheduleId" name="id">

                        <div class="form-group">
                            <label>班次/站点 <span class="text-danger">*</span></label>
                            <div class="station-radios mt-2">
                                <div class="custom-control custom-radio custom-control-inline">
                                    <input type="radio" class="custom-control-input" id="station1" name="station" value="一站" required>
                                    <label class="custom-control-label" for="station1">一站</label>
                                </div>
                                <div class="custom-control custom-radio custom-control-inline">
                                    <input type="radio" class="custom-control-input" id="station2" name="station" value="二站" required>
                                    <label class="custom-control-label" for="station2">二站</label>
                                </div>
                            </div>
                            <small class="form-text text-muted">请选择一个班次</small>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="startDate">开始日期 <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control" id="startDate" name="start_date" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="endDate">结束日期 <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control" id="endDate" name="end_date" required>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label>天数</label>
                            <input type="text" class="form-control" id="daysDisplay" readonly
                                   placeholder="将根据日期自动计算">
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                        <button type="submit" class="btn btn-apple">
                            <i class="fas fa-save mr-1"></i>保存
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 备注编辑模态框 -->
    <div class="modal fade" id="remarkModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-sticky-note mr-2"></i>
                        编辑月度备注
                    </h5>
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                </div>
                <form id="remarkForm">
                    <div class="modal-body">
                        <input type="hidden" id="remarkYear" name="year">
                        <input type="hidden" id="remarkMonth" name="month">

                        <div class="form-group">
                            <label id="remarkTitle">备注内容</label>
                            <textarea class="form-control" id="remarkContent" name="remark" rows="4"
                                      placeholder="请输入本月的备注信息..."></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                        <button type="submit" class="btn btn-apple">
                            <i class="fas fa-save mr-1"></i>保存备注
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 导入Excel模态框 -->
    <div class="modal fade" id="importModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-file-import mr-2"></i>
                        导入排班数据
                    </h5>
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                </div>
                <form id="importForm" enctype="multipart/form-data">
                    <div class="modal-body">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle mr-2"></i>
                            <strong>导入说明：</strong>
                            <ul class="mb-0 mt-2">
                                <li>支持 .xlsx 和 .xls 格式的Excel文件</li>
                                <li>文件大小不能超过5MB</li>
                                <li>请先下载模板，按照模板格式填写数据</li>
                                <li>只需填写：班次、开始日期、结束日期</li>
                                <li>系统会根据开始日期自动识别年份和月份</li>
                                <li>跨月排班按开始日期的月份归类</li>
                                <li>日期格式支持：YYYY-MM-DD 或 YYYY/MM/DD</li>
                                <li>也可以直接在Excel中输入日期，系统会自动识别</li>
                            </ul>
                        </div>

                        <div class="form-group">
                            <label for="excelFile">选择Excel文件 <span class="text-danger">*</span></label>
                            <div class="custom-file">
                                <input type="file" class="custom-file-input" id="excelFile" name="excel_file"
                                       accept=".xlsx,.xls" required>
                                <label class="custom-file-label" for="excelFile">请选择文件...</label>
                            </div>
                        </div>

                        <div class="form-group">
                            <div class="d-flex justify-content-between align-items-center">
                                <span>还没有模板？</span>
                                <button type="button" class="btn btn-outline-info btn-sm" onclick="downloadTemplate()">
                                    <i class="fas fa-download mr-1"></i>下载导入模板
                                </button>
                            </div>
                        </div>

                        <div id="importProgress" style="display: none;">
                            <div class="progress mb-3">
                                <div class="progress-bar progress-bar-striped progress-bar-animated"
                                     role="progressbar" style="width: 0%"></div>
                            </div>
                            <div class="text-center">
                                <small class="text-muted">正在导入数据，请稍候...</small>
                            </div>
                        </div>

                        <div id="importResult" style="display: none;"></div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                        <button type="submit" class="btn btn-apple" id="importBtn">
                            <i class="fas fa-upload mr-1"></i>开始导入
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 数据分析模态框 -->
    <div class="modal fade" id="analysisModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-chart-bar mr-2"></i>
                        倒班数据分析
                    </h5>
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                </div>
                <div class="modal-body">
                    <!-- 分析年份选择 -->
                    <div class="row mb-4">
                        <div class="col-md-2">
                            <label class="form-label">分析模式</label>
                            <select class="form-control" id="analysisMode" onchange="toggleAnalysisMode()">
                                <option value="single">单年分析</option>
                                <option value="compare">双年对比</option>
                                <option value="multi">多年对比</option>
                            </select>
                        </div>
                        <div class="col-md-2" id="singleYearSection">
                            <label class="form-label">分析年份</label>
                            <select class="form-control" id="analysisYear" onchange="loadAnalysisData()">
                                <?php for($y = date('Y'); $y >= 2019; $y--): ?>
                                    <option value="<?= $y ?>" <?= $y == date('Y') ? 'selected' : '' ?>><?= $y ?>年</option>
                                <?php endfor; ?>
                            </select>
                        </div>
                        <div class="col-md-2" id="compareYearSection" style="display: none;">
                            <label class="form-label">对比年份</label>
                            <select class="form-control" id="compareYear" onchange="loadAnalysisData()">
                                <option value="">选择对比年份</option>
                                <?php for($y = date('Y'); $y >= 2019; $y--): ?>
                                    <option value="<?= $y ?>"><?= $y ?>年</option>
                                <?php endfor; ?>
                            </select>
                        </div>
                        <div class="col-md-3" id="multiYearSection" style="display: none;">
                            <label class="form-label">多年对比范围</label>
                            <select class="form-control" id="multiYearRange" onchange="loadAnalysisData()">
                                <option value="3">最近3年</option>
                                <option value="4">最近4年</option>
                                <option value="5">最近5年</option>
                                <option value="custom">自定义范围</option>
                            </select>
                        </div>
                        <div class="col-md-2" id="customStartYear" style="display: none;">
                            <label class="form-label">起始年份</label>
                            <select class="form-control" id="startYear" onchange="loadAnalysisData()">
                                <?php for($y = 2019; $y <= date('Y'); $y++): ?>
                                    <option value="<?= $y ?>"><?= $y ?>年</option>
                                <?php endfor; ?>
                            </select>
                        </div>
                        <div class="col-md-2" id="customEndYear" style="display: none;">
                            <label class="form-label">结束年份</label>
                            <select class="form-control" id="endYear" onchange="loadAnalysisData()">
                                <?php for($y = 2019; $y <= date('Y'); $y++): ?>
                                    <option value="<?= $y ?>" <?= $y == date('Y') ? 'selected' : '' ?>><?= $y ?>年</option>
                                <?php endfor; ?>
                            </select>
                        </div>
                    </div>

                    <!-- 统计卡片 -->
                    <div class="row mb-4" id="analysisStats">
                        <div class="col-md-3">
                            <div class="stat-card">
                                <div class="stat-number text-primary" id="totalDaysAnalysis">0</div>
                                <div class="stat-label">总出海天数</div>
                                <div class="stat-compare" id="totalDaysCompare" style="display: none;"></div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-card">
                                <div class="stat-number text-success" id="station1DaysAnalysis">0</div>
                                <div class="stat-label">一站天数</div>
                                <div class="stat-compare" id="station1DaysCompare" style="display: none;"></div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-card">
                                <div class="stat-number text-info" id="station2DaysAnalysis">0</div>
                                <div class="stat-label">二站天数</div>
                                <div class="stat-compare" id="station2DaysCompare" style="display: none;"></div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-card">
                                <div class="stat-number text-warning" id="avgDaysAnalysis">0</div>
                                <div class="stat-label">一站二站相差天数</div>
                                <div class="stat-compare" id="avgDaysCompare" style="display: none;"></div>
                            </div>
                        </div>
                    </div>

                    <!-- 图表区域 -->
                    <div class="row">
                        <!-- 月度天数对比图 -->
                        <div class="col-md-6 mb-4">
                            <div class="main-card">
                                <div class="card-header">
                                    <h6 class="mb-0">月度出海天数对比</h6>
                                </div>
                                <div class="card-body">
                                    <canvas id="monthlyChart" height="300"></canvas>
                                </div>
                            </div>
                        </div>

                        <!-- 班次分布饼图 -->
                        <div class="col-md-6 mb-4">
                            <div class="main-card">
                                <div class="card-header">
                                    <h6 class="mb-0">班次分布</h6>
                                </div>
                                <div class="card-body">
                                    <canvas id="stationChart" height="300"></canvas>
                                </div>
                            </div>
                        </div>

                        <!-- 出海天数统计 -->
                        <div class="col-md-6 mb-4">
                            <div class="main-card">
                                <div class="card-header">
                                    <h6 class="mb-0">出海天数统计</h6>
                                </div>
                                <div class="card-body" style="height: 350px; padding: 15px;">
                                    <div class="stats-table-container">
                                        <table class="stats-table" id="daysStatsTable">
                                            <thead>
                                                <tr>
                                                    <th>年份</th>
                                                    <th>一站</th>
                                                    <th>二站</th>
                                                    <th>对比</th>
                                                </tr>
                                            </thead>
                                            <tbody id="daysStatsTableBody">
                                                <tr>
                                                    <td colspan="4" class="text-center text-muted">暂无数据</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 年度趋势对比 -->
                        <div class="col-md-6 mb-4">
                            <div class="main-card">
                                <div class="card-header">
                                    <h6 class="mb-0">年度趋势对比</h6>
                                </div>
                                <div class="card-body">
                                    <canvas id="yearlyTrendChart" height="300"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 详细统计表格 -->
                    <div class="main-card" id="detailStatsCard">
                        <div class="card-header">
                            <h6 class="mb-0">详细统计信息</h6>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>统计项目</th>
                                            <th>数值</th>
                                            <th>说明</th>
                                        </tr>
                                    </thead>
                                    <tbody id="detailStatsTable">
                                        <!-- 动态填充 -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">关闭</button>
                    <button type="button" class="btn btn-apple" onclick="exportAnalysisReport()">
                        <i class="fas fa-file-export mr-1"></i>导出分析报告
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 提示框 -->
    <div class="alert alert-success" id="alertBox" style="position: fixed; top: 20px; right: 20px; z-index: 9999; display: none; min-width: 300px; box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);"></div>

    <script src="../assets/js/jquery.min.js"></script>
    <script src="../assets/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/chart.js"></script>
    <script>
        let isLoading = false;

        $(document).ready(function() {
            loadSchedules();

            // 日期变化时自动计算天数
            $('#startDate, #endDate').on('change', calculateDays);

            // 表单提交
            $('#scheduleForm').on('submit', function(e) {
                e.preventDefault();
                saveSchedule();
            });

            $('#remarkForm').on('submit', function(e) {
                e.preventDefault();
                saveRemark();
            });

            $('#importForm').on('submit', function(e) {
                e.preventDefault();
                importExcel();
            });

            // 文件选择变化
            $('#excelFile').on('change', function() {
                const fileName = $(this).val().split('\\').pop();
                $(this).next('.custom-file-label').text(fileName || '请选择文件...');
            });

            // 多年份范围变化
            $('#multiYearRange').on('change', function() {
                toggleMultiYearCustom();
                loadAnalysisData();
            });
        });

        // 加载排班列表
        function loadSchedules() {
            if (isLoading) return;
            isLoading = true;

            const data = {
                ajax: 1,
                action: 'get_schedules',
                year: $('#filterYear').val(),
                month: $('#filterMonth').val(),
                station: $('#filterStation').val()
            };

            $('#schedulesList').html('<div class="loading"><i class="fas fa-spinner fa-spin"></i> 加载中...</div>');

            $.post('', data, function(response) {
                if (response.status === 'success') {
                    renderSchedulesList(response.data);
                } else {
                    showAlert('加载失败：' + response.message, 'danger');
                }
            }).fail(function() {
                showAlert('网络错误，请稍后重试', 'danger');
            }).always(function() {
                isLoading = false;
            });
        }

        // 获取班次颜色类
        function getStationColorClass(station) {
            if (station.includes('一站') || station.includes('1站')) {
                return 'badge-station-1';
            } else if (station.includes('二站') || station.includes('2站')) {
                return 'badge-station-2';
            } else {
                return 'badge-station-1'; // 默认使用一站颜色
            }
        }

        // 渲染排班列表
        function renderSchedulesList(schedules) {
            if (schedules.length === 0) {
                $('#schedulesList').html(`
                    <div class="empty-state">
                        <i class="fas fa-calendar-times"></i>
                        <h5>暂无排班数据</h5>
                        <p>点击"添加排班"按钮创建第一个排班计划</p>
                    </div>
                `);
                $('#statisticsSection').hide();
                return;
            }

            let html = `
                <div class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr>
                                <th style="width: 6%; min-width: 50px;">序号</th>
                                <th style="width: 10%; min-width: 80px;">年份</th>
                                <th style="width: 10%; min-width: 80px;">月份</th>
                                <th style="width: 12%; min-width: 100px;">班次</th>
                                <th style="width: 16%; min-width: 120px;">开始日期</th>
                                <th style="width: 16%; min-width: 120px;">结束日期</th>
                                <th style="width: 10%; min-width: 80px;">天数</th>
                                <th style="width: 20%; min-width: 150px;">操作</th>
                            </tr>
                        </thead>
                        <tbody>
            `;

            // 计算统计信息
            let station1Days = 0;
            let station2Days = 0;
            let totalDays = 0;

            schedules.forEach((schedule, index) => {
                const rowNumber = index + 1;
                const stationColorClass = getStationColorClass(schedule.station);
                const days = parseInt(schedule.days) || 0;

                // 累计天数统计
                totalDays += days;
                if (schedule.station.includes('一站')) {
                    station1Days += days;
                } else if (schedule.station.includes('二站')) {
                    station2Days += days;
                }

                html += `
                    <tr>
                        <td>${rowNumber}</td>
                        <td>${schedule.year}</td>
                        <td>${schedule.month}月</td>
                        <td><span class="badge-station ${stationColorClass}">${schedule.station}</span></td>
                        <td>${schedule.start_date}</td>
                        <td>${schedule.end_date}</td>
                        <td><strong>${schedule.days}天</strong></td>
                        <td>
                            <button class="btn btn-sm btn-apple" onclick="editSchedule(${schedule.id})">
                                <i class="fas fa-edit"></i> 编辑
                            </button>
                            <button class="btn btn-sm btn-apple-danger ml-1" onclick="deleteSchedule(${schedule.id})">
                                <i class="fas fa-trash"></i> 删除
                            </button>
                        </td>
                    </tr>
                `;
            });

            html += '</tbody></table></div>';
            $('#schedulesList').html(html);

            // 更新统计信息
            updateStatistics(station1Days, station2Days, totalDays, schedules.length);
        }

        // 更新统计信息
        function updateStatistics(station1Days, station2Days, totalDays, totalSchedules) {
            // 数字动画效果
            animateNumber('#station1Days', station1Days);
            animateNumber('#station2Days', station2Days);
            animateNumber('#totalDays', totalDays);
            animateNumber('#totalSchedules', totalSchedules);

            // 平滑显示统计区域
            if ($('#statisticsSection').is(':hidden')) {
                $('#statisticsSection').slideDown(300);
            }
        }

        // 数字动画函数
        function animateNumber(selector, targetNumber) {
            const element = $(selector);
            const currentNumber = parseInt(element.text()) || 0;

            if (currentNumber === targetNumber) return;

            const increment = targetNumber > currentNumber ? 1 : -1;
            const duration = Math.min(Math.abs(targetNumber - currentNumber) * 20, 500);
            const steps = Math.abs(targetNumber - currentNumber);
            const stepDuration = duration / steps;

            let current = currentNumber;
            const timer = setInterval(() => {
                current += increment;
                element.text(current);

                if (current === targetNumber) {
                    clearInterval(timer);
                }
            }, stepDuration);
        }



        // 显示添加模态框
        function showAddModal() {
            $('#modalTitle').text('添加排班');
            $('#scheduleForm')[0].reset();
            $('#scheduleId').val('');
            $('#daysDisplay').val('');
            // 清除单选框选择
            $('input[name="station"]').prop('checked', false);
            $('#scheduleModal').modal('show');
        }

        // 编辑排班
        function editSchedule(id) {
            // 从表格中获取数据
            const row = $(`button[onclick="editSchedule(${id})"]`).closest('tr');
            const cells = row.find('td');
            const stationText = $(cells[3]).find('.badge-station').text();

            $('#modalTitle').text('编辑排班');
            $('#scheduleId').val(id);

            // 清除所有单选框选择
            $('input[name="station"]').prop('checked', false);
            // 根据班次文本选择对应的单选框
            $(`input[name="station"][value="${stationText}"]`).prop('checked', true);

            $('#startDate').val($(cells[4]).text());
            $('#endDate').val($(cells[5]).text());

            calculateDays();
            $('#scheduleModal').modal('show');
        }

        // 计算天数
        function calculateDays() {
            const startDate = $('#startDate').val();
            const endDate = $('#endDate').val();

            if (startDate && endDate) {
                const start = new Date(startDate);
                const end = new Date(endDate);
                const diffTime = Math.abs(end - start);
                const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1;
                $('#daysDisplay').val(diffDays + ' 天');
            } else {
                $('#daysDisplay').val('');
            }
        }

        // 保存排班
        function saveSchedule() {
            const formData = $('#scheduleForm').serialize();
            const isEdit = $('#scheduleId').val() !== '';
            const action = isEdit ? 'update_schedule' : 'add_schedule';

            $.post('', formData + '&ajax=1&action=' + action, function(response) {
                if (response.status === 'success') {
                    showAlert(response.message, 'success');
                    $('#scheduleModal').modal('hide');
                    loadSchedules();
                } else {
                    showAlert(response.message, 'danger');
                }
            }).fail(function() {
                showAlert('网络错误，请稍后重试', 'danger');
            });
        }

        // 删除排班
        function deleteSchedule(id) {
            if (!confirm('确定要删除这个排班记录吗？')) {
                return;
            }

            $.post('', {
                ajax: 1,
                action: 'delete_schedule',
                id: id
            }, function(response) {
                if (response.status === 'success') {
                    showAlert(response.message, 'success');
                    loadSchedules();
                } else {
                    showAlert(response.message, 'danger');
                }
            }).fail(function() {
                showAlert('网络错误，请稍后重试', 'danger');
            });
        }

        // 切换备注区域显示
        function toggleRemarks() {
            const remarksSection = $('#remarksSection');
            if (remarksSection.is(':visible')) {
                remarksSection.slideUp();
            } else {
                remarksSection.slideDown();
                loadRemarks();
            }
        }

        // 加载备注
        function loadRemarks() {
            const year = $('#remarkYear').val();

            $('#remarksList').html('<div class="loading"><i class="fas fa-spinner fa-spin"></i> 加载备注中...</div>');

            $.post('', {
                ajax: 1,
                action: 'get_remarks',
                year: year
            }, function(response) {
                if (response.status === 'success') {
                    renderRemarksList(response.remarks, year);
                } else {
                    showAlert('加载备注失败：' + response.message, 'danger');
                }
            }).fail(function() {
                showAlert('网络错误，请稍后重试', 'danger');
            });
        }

        // 渲染备注列表
        function renderRemarksList(remarks, year) {
            let html = '<div class="row">';

            for (let month = 1; month <= 12; month++) {
                const remark = remarks[month] || '';
                const hasRemark = remark.trim() !== '';

                html += `
                    <div class="col-md-6 col-lg-4 mb-3">
                        <div class="remark-item">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <h6 class="mb-0">${year}年${month}月</h6>
                                <button class="btn btn-sm btn-apple" onclick="editRemark(${year}, ${month}, '${remark.replace(/'/g, "\\'")}')">
                                    <i class="fas fa-edit"></i> ${hasRemark ? '编辑' : '添加'}
                                </button>
                            </div>
                            <div class="text-muted small">
                                ${hasRemark ? remark : '暂无备注'}
                            </div>
                        </div>
                    </div>
                `;
            }

            html += '</div>';
            $('#remarksList').html(html);
        }

        // 编辑备注
        function editRemark(year, month, remark) {
            $('#remarkYear').val(year);
            $('#remarkMonth').val(month);
            $('#remarkContent').val(remark);
            $('#remarkTitle').text(`${year}年${month}月备注`);
            $('#remarkModal').modal('show');
        }

        // 保存备注
        function saveRemark() {
            const formData = $('#remarkForm').serialize();

            $.post('', formData + '&ajax=1&action=update_remark', function(response) {
                if (response.status === 'success') {
                    showAlert(response.message, 'success');
                    $('#remarkModal').modal('hide');
                    loadRemarks();
                } else {
                    showAlert(response.message, 'danger');
                }
            }).fail(function() {
                showAlert('网络错误，请稍后重试', 'danger');
            });
        }

        // 导出Excel
        function exportExcel() {
            const year = $('#filterYear').val();
            const month = $('#filterMonth').val();
            const station = $('#filterStation').val();

            // 构建导出URL
            let exportUrl = '../api/export_shift_excel.php?year=' + year;
            if (month) {
                exportUrl += '&month=' + month;
            }
            if (station) {
                exportUrl += '&station=' + encodeURIComponent(station);
            }

            // 显示导出提示
            showAlert('正在生成Excel文件，请稍候...', 'info');

            // 创建隐藏的下载链接
            const link = document.createElement('a');
            link.href = exportUrl;
            link.style.display = 'none';
            document.body.appendChild(link);

            // 触发下载
            link.click();

            // 清理
            document.body.removeChild(link);

            // 延迟显示成功消息
            setTimeout(function() {
                showAlert('Excel文件导出成功！', 'success');
            }, 1000);
        }

        // 下载导入模板
        function downloadTemplate() {
            showAlert('正在生成模板文件，请稍候...', 'info');

            const link = document.createElement('a');
            link.href = '../api/download_shift_template.php';
            link.style.display = 'none';
            document.body.appendChild(link);

            link.click();
            document.body.removeChild(link);

            setTimeout(function() {
                showAlert('模板文件下载成功！', 'success');
            }, 1000);
        }

        // 显示导入模态框
        function showImportModal() {
            $('#importForm')[0].reset();
            $('#excelFile').next('.custom-file-label').text('请选择文件...');
            $('#importProgress').hide();
            $('#importResult').hide();
            $('#importBtn').prop('disabled', false);
            $('#importModal').modal('show');
        }

        // 导入Excel
        function importExcel() {
            const fileInput = $('#excelFile')[0];
            if (!fileInput.files.length) {
                showAlert('请选择要导入的Excel文件', 'warning');
                return;
            }

            const formData = new FormData();
            formData.append('excel_file', fileInput.files[0]);

            // 显示进度条
            $('#importProgress').show();
            $('#importResult').hide();
            $('#importBtn').prop('disabled', true);

            // 模拟进度条动画
            let progress = 0;
            const progressInterval = setInterval(() => {
                progress += Math.random() * 30;
                if (progress > 90) progress = 90;
                $('.progress-bar').css('width', progress + '%');
            }, 200);

            $.ajax({
                url: '../api/import_shift_excel.php',
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function(response) {
                    clearInterval(progressInterval);
                    $('.progress-bar').css('width', '100%');

                    setTimeout(() => {
                        $('#importProgress').hide();
                        displayImportResult(response);

                        if (response.status === 'success') {
                            // 刷新列表
                            loadSchedules();
                        }
                    }, 500);
                },
                error: function(xhr, status, error) {
                    clearInterval(progressInterval);
                    $('#importProgress').hide();
                    $('#importBtn').prop('disabled', false);

                    console.log('AJAX Error:', {
                        status: xhr.status,
                        statusText: xhr.statusText,
                        responseText: xhr.responseText,
                        error: error
                    });

                    let errorMessage = '导入失败，请稍后重试';

                    if (xhr.status === 0) {
                        errorMessage = '网络连接失败，请检查网络连接';
                    } else if (xhr.status === 404) {
                        errorMessage = '导入接口未找到，请联系管理员';
                    } else if (xhr.status === 500) {
                        errorMessage = '服务器内部错误，请联系管理员';
                    } else {
                        try {
                            const response = JSON.parse(xhr.responseText);
                            errorMessage = response.message || errorMessage;
                        } catch (e) {
                            errorMessage = '服务器返回了无效的响应：' + xhr.responseText.substring(0, 100);
                        }
                    }

                    showAlert(errorMessage, 'danger');
                }
            });
        }

        // 显示导入结果
        function displayImportResult(response) {
            let html = '';

            if (response.status === 'success') {
                html = `
                    <div class="alert alert-success">
                        <h6><i class="fas fa-check-circle mr-2"></i>导入成功！</h6>
                        <p class="mb-2">${response.message}</p>
                        <ul class="mb-0">
                            <li>成功导入：${response.data.success_count} 条</li>
                            <li>跳过空行：${response.data.skip_count} 条</li>
                            <li>导入失败：${response.data.error_count} 条</li>
                        </ul>
                `;

                if (response.data.errors.length > 0) {
                    html += `
                        <hr>
                        <h6>错误详情：</h6>
                        <ul class="mb-0">
                    `;
                    response.data.errors.forEach(error => {
                        html += `<li class="text-danger">${error}</li>`;
                    });
                    html += '</ul>';
                }

                html += '</div>';
            } else {
                html = `
                    <div class="alert alert-danger">
                        <h6><i class="fas fa-exclamation-triangle mr-2"></i>导入失败</h6>
                        <p class="mb-0">${response.message}</p>
                    </div>
                `;
            }

            $('#importResult').html(html).show();
            $('#importBtn').prop('disabled', false);
        }

        // 显示数据分析模态框
        function showAnalysisModal() {
            $('#analysisModal').modal('show');
            loadAnalysisData();
        }

        // 切换分析模式
        function toggleAnalysisMode() {
            const mode = $('#analysisMode').val();

            // 隐藏所有区域
            $('#singleYearSection, #compareYearSection, #multiYearSection, #customStartYear, #customEndYear').hide();

            if (mode === 'single') {
                $('#singleYearSection').show();
            } else if (mode === 'compare') {
                $('#singleYearSection, #compareYearSection').show();
            } else if (mode === 'multi') {
                $('#multiYearSection').show();
                toggleMultiYearCustom();
            }

            loadAnalysisData();
        }

        // 切换多年份自定义范围
        function toggleMultiYearCustom() {
            const range = $('#multiYearRange').val();
            if (range === 'custom') {
                $('#customStartYear, #customEndYear').show();
            } else {
                $('#customStartYear, #customEndYear').hide();
            }
        }

        // 加载分析数据
        function loadAnalysisData() {
            const mode = $('#analysisMode').val();
            let postData = {
                ajax: 1,
                action: 'get_analysis_data',
                analysis_mode: mode
            };

            if (mode === 'single' || mode === 'compare') {
                postData.year = $('#analysisYear').val();
                if (mode === 'compare') {
                    postData.compare_year = $('#compareYear').val();
                }
            } else if (mode === 'multi') {
                const currentYear = new Date().getFullYear();
                postData.year = currentYear;

                const range = $('#multiYearRange').val();
                if (range === 'custom') {
                    postData.start_year = $('#startYear').val();
                    postData.end_year = $('#endYear').val();
                } else {
                    postData.multi_year_range = range;
                }
            }

            showAlert('正在加载分析数据...', 'info');

            $.post('', postData, function(response) {
                if (response.status === 'success') {
                    renderAnalysisData(response.data);
                    showAlert('分析数据加载完成', 'success');
                } else {
                    showAlert('加载分析数据失败：' + response.message, 'danger');
                }
            }).fail(function() {
                showAlert('网络错误，请稍后重试', 'danger');
            });
        }



        // 渲染分析数据
        function renderAnalysisData(data) {
            // 首先隐藏详细统计信息（默认隐藏）
            $('#detailStatsCard').hide();

            if (data.analysisMode === 'multi' && data.multiYearData) {
                renderMultiYearAnalysis(data);
                // 多年对比模式：保持隐藏详细统计信息
            } else {
                // 单年或双年对比模式
                animateNumber('#totalDaysAnalysis', data.totalDays || 0);
                animateNumber('#station1DaysAnalysis', data.station1Days || 0);
                animateNumber('#station2DaysAnalysis', data.station2Days || 0);

                // 计算一站二站相差天数
                const station1Days = data.station1Days || 0;
                const station2Days = data.station2Days || 0;
                const diffDays = Math.abs(station1Days - station2Days);
                $('#avgDaysAnalysis').text(diffDays);

                // 显示或隐藏对比信息
                if (data.compareData && data.compareYear) {
                    showCompareInfo(data, data.compareData, data.compareYear);
                    // 双年对比模式：保持隐藏详细统计信息
                } else {
                    hideCompareInfo();
                    // 单年模式：显示详细统计信息
                    $('#detailStatsCard').show();
                    // 渲染详细统计表格
                    renderDetailStatsTable(data.detailStats);
                }

                // 渲染图表
                renderMonthlyChart(data.monthlyData, data.compareData ? data.compareData.monthlyData : null, data.year, data.compareYear);
                renderStationChart(data.stationData, data.compareData, data.year, data.compareYear);
                renderDaysStatsTable(data, 'single');
                renderYearlyTrendChart(data.yearlyTrend);
            }
        }

        // 渲染多年份分析
        function renderMultiYearAnalysis(data) {
            const multiData = data.multiYearData;

            // 计算多年份总计
            let totalDays = 0, station1Days = 0, station2Days = 0, totalSchedules = 0;
            multiData.stats.forEach(stat => {
                totalDays += parseInt(stat.total_days) || 0;
                station1Days += parseInt(stat.station1_days) || 0;
                station2Days += parseInt(stat.station2_days) || 0;
                totalSchedules += parseInt(stat.total_schedules) || 0;
            });

            // 计算一站二站相差天数
            const diffDays = Math.abs(station1Days - station2Days);

            // 更新统计卡片
            animateNumber('#totalDaysAnalysis', totalDays);
            animateNumber('#station1DaysAnalysis', station1Days);
            animateNumber('#station2DaysAnalysis', station2Days);
            $('#avgDaysAnalysis').text(diffDays);

            hideCompareInfo();

            // 渲染多年份图表
            renderMultiYearCharts(multiData);
        }

        // 渲染多年份图表
        function renderMultiYearCharts(multiData) {
            // 准备年度趋势数据
            const years = multiData.years.map(y => y + '年');
            const totalDaysData = multiData.stats.map(s => parseInt(s.total_days) || 0);
            const station1DaysData = multiData.stats.map(s => parseInt(s.station1_days) || 0);
            const station2DaysData = multiData.stats.map(s => parseInt(s.station2_days) || 0);

            // 渲染年度趋势图
            renderYearlyTrendChart({
                years: years,
                totalDays: totalDaysData,
                station1Days: station1DaysData,
                station2Days: station2DaysData
            });

            // 渲染多年份月度平均图
            renderMultiYearMonthlyChart(multiData);

            // 渲染多年份班次分布
            renderMultiYearStationChart(multiData);

            // 渲染多年份出海天数统计表格
            renderDaysStatsTable({multiYearData: multiData}, 'multi');
        }

        // 显示对比信息
        function showCompareInfo(currentData, compareData, compareYear) {
            const totalDiff = currentData.totalDays - compareData.totalDays;
            const station1Diff = currentData.station1Days - compareData.station1Days;
            const station2Diff = currentData.station2Days - compareData.station2Days;
            const avgDiff = currentData.avgDays - compareData.avgDays;

            updateCompareElement('#totalDaysCompare', totalDiff, '天', compareYear);
            updateCompareElement('#station1DaysCompare', station1Diff, '天', compareYear);
            updateCompareElement('#station2DaysCompare', station2Diff, '天', compareYear);
            updateCompareElement('#avgDaysCompare', avgDiff.toFixed(1), '天', compareYear);
        }

        // 更新对比元素
        function updateCompareElement(selector, diff, unit, compareYear) {
            const element = $(selector);
            const prefix = diff > 0 ? '+' : '';
            const className = diff > 0 ? 'positive' : diff < 0 ? 'negative' : 'neutral';
            const text = diff === 0 ? `与${compareYear}年持平` : `${prefix}${diff}${unit} vs ${compareYear}年`;

            element.removeClass('positive negative neutral')
                   .addClass(className)
                   .text(text)
                   .show();
        }

        // 隐藏对比信息
        function hideCompareInfo() {
            $('.stat-compare').hide();
        }

        // 月度对比图表
        let monthlyChart = null;
        function renderMonthlyChart(data, compareData, year, compareYear) {
            const ctx = document.getElementById('monthlyChart').getContext('2d');

            if (monthlyChart) {
                monthlyChart.destroy();
            }

            const datasets = [{
                label: `${year}年一站天数`,
                data: data.station1 || Array(12).fill(0),
                backgroundColor: 'rgba(0, 122, 255, 0.8)',
                borderColor: '#007aff',
                borderWidth: 1
            }, {
                label: `${year}年二站天数`,
                data: data.station2 || Array(12).fill(0),
                backgroundColor: 'rgba(52, 199, 89, 0.8)',
                borderColor: '#34c759',
                borderWidth: 1
            }];

            // 如果有对比数据，添加对比年份的数据集
            if (compareData && compareYear) {
                datasets.push({
                    label: `${compareYear}年一站天数`,
                    data: compareData.station1 || Array(12).fill(0),
                    backgroundColor: 'rgba(0, 122, 255, 0.4)',
                    borderColor: '#007aff',
                    borderWidth: 1,
                    borderDash: [5, 5]
                });
                datasets.push({
                    label: `${compareYear}年二站天数`,
                    data: compareData.station2 || Array(12).fill(0),
                    backgroundColor: 'rgba(52, 199, 89, 0.4)',
                    borderColor: '#34c759',
                    borderWidth: 1,
                    borderDash: [5, 5]
                });
            }

            monthlyChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
                    datasets: datasets
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    },
                    plugins: {
                        legend: {
                            position: 'top'
                        },
                        title: {
                            display: compareData ? true : false,
                            text: compareData ? `${year}年 vs ${compareYear}年 月度对比` : ''
                        }
                    }
                }
            });
        }

        // 班次分布饼图
        let stationChart = null;
        function renderStationChart(data, compareData, year, compareYear) {
            const ctx = document.getElementById('stationChart').getContext('2d');

            if (stationChart) {
                stationChart.destroy();
            }

            if (compareData && compareYear) {
                // 如果有对比数据，显示双饼图对比
                stationChart = new Chart(ctx, {
                    type: 'doughnut',
                    data: {
                        labels: [`${year}年一站`, `${year}年二站`, `${compareYear}年一站`, `${compareYear}年二站`],
                        datasets: [{
                            label: `${year}年`,
                            data: [data.station1Days || 0, data.station2Days || 0, 0, 0],
                            backgroundColor: ['#007aff', '#34c759', 'transparent', 'transparent'],
                            borderWidth: 2,
                            borderColor: '#fff'
                        }, {
                            label: `${compareYear}年`,
                            data: [0, 0, compareData.station1Days || 0, compareData.station2Days || 0],
                            backgroundColor: ['transparent', 'transparent', 'rgba(0, 122, 255, 0.5)', 'rgba(52, 199, 89, 0.5)'],
                            borderWidth: 2,
                            borderColor: '#fff'
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom'
                            },
                            title: {
                                display: true,
                                text: `${year}年 vs ${compareYear}年 班次分布对比`
                            }
                        }
                    }
                });
            } else {
                // 单年份显示
                stationChart = new Chart(ctx, {
                    type: 'doughnut',
                    data: {
                        labels: ['一站', '二站'],
                        datasets: [{
                            data: [data.station1Days || 0, data.station2Days || 0],
                            backgroundColor: ['#007aff', '#34c759'],
                            borderWidth: 2,
                            borderColor: '#fff'
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom'
                            }
                        }
                    }
                });
            }
        }

        // 出海天数统计表格
        function renderDaysStatsTable(data, mode) {
            let html = '';
            let tableMode = '';
            let rowCount = 0;

            if (mode === 'single') {
                // 单年分析或双年对比：显示年度数据
                if (data.compareData) {
                    // 双年对比模式
                    tableMode = 'compare-mode';
                    rowCount = 2; // 2年数据

                    const years = [data.year, data.compareYear];
                    const yearData = [
                        {year: data.year, station1Days: data.station1Days || 0, station2Days: data.station2Days || 0},
                        {year: data.compareYear, station1Days: data.compareData.station1Days || 0, station2Days: data.compareData.station2Days || 0}
                    ];

                    yearData.forEach(item => {
                        const diff = item.station1Days - item.station2Days;
                        const diffText = diff > 0 ? `一站多${diff}天` : diff < 0 ? `二站多${Math.abs(diff)}天` : '相等';
                        const diffClass = diff > 0 ? 'text-primary' : diff < 0 ? 'text-success' : 'text-secondary';

                        html += `
                            <tr>
                                <td><strong>${item.year}年</strong></td>
                                <td><span class="badge badge-primary">${item.station1Days}天</span></td>
                                <td><span class="badge badge-success">${item.station2Days}天</span></td>
                                <td><span class="${diffClass}"><strong>${diffText}</strong></span></td>
                            </tr>
                        `;
                    });
                } else {
                    // 单年分析模式
                    tableMode = 'single-mode';
                    rowCount = 1; // 1年数据

                    const station1Days = data.station1Days || 0;
                    const station2Days = data.station2Days || 0;
                    const diff = station1Days - station2Days;
                    const diffText = diff > 0 ? `一站多${diff}天` : diff < 0 ? `二站多${Math.abs(diff)}天` : '相等';
                    const diffClass = diff > 0 ? 'text-primary' : diff < 0 ? 'text-success' : 'text-secondary';

                    html = `
                        <tr>
                            <td><strong>${data.year}年</strong></td>
                            <td><span class="badge badge-primary">${station1Days}天</span></td>
                            <td><span class="badge badge-success">${station2Days}天</span></td>
                            <td><span class="${diffClass}"><strong>${diffText}</strong></span></td>
                        </tr>
                    `;
                }

            } else if (mode === 'multi' && data.multiYearData) {
                // 多年分析：显示每年数据
                tableMode = 'multi-mode';
                const multiData = data.multiYearData;
                rowCount = multiData.stats.length + 1; // 年份数据 + 总计行

                let totalStation1 = 0, totalStation2 = 0;

                multiData.stats.forEach(yearStat => {
                    const station1Days = parseInt(yearStat.station1_days) || 0;
                    const station2Days = parseInt(yearStat.station2_days) || 0;
                    const diff = station1Days - station2Days;
                    const diffText = diff > 0 ? `一站多${diff}天` : diff < 0 ? `二站多${Math.abs(diff)}天` : '相等';
                    const diffClass = diff > 0 ? 'text-primary' : diff < 0 ? 'text-success' : 'text-secondary';

                    totalStation1 += station1Days;
                    totalStation2 += station2Days;

                    const rowClass = (station1Days + station2Days) > 0 ? '' : 'text-muted';
                    html += `
                        <tr class="${rowClass}">
                            <td><strong>${yearStat.year}年</strong></td>
                            <td><span class="badge badge-primary">${station1Days}天</span></td>
                            <td><span class="badge badge-success">${station2Days}天</span></td>
                            <td><span class="${diffClass}"><strong>${diffText}</strong></span></td>
                        </tr>
                    `;
                });

                // 添加总计行
                const totalDiff = totalStation1 - totalStation2;
                const totalDiffText = totalDiff > 0 ? `一站多${totalDiff}天` : totalDiff < 0 ? `二站多${Math.abs(totalDiff)}天` : '相等';
                const totalDiffClass = totalDiff > 0 ? 'text-primary' : totalDiff < 0 ? 'text-success' : 'text-secondary';

                html += `
                    <tr class="table-active">
                        <td><strong>总计</strong></td>
                        <td><span class="badge badge-dark">${totalStation1}天</span></td>
                        <td><span class="badge badge-dark">${totalStation2}天</span></td>
                        <td><span class="${totalDiffClass}"><strong>${totalDiffText}</strong></span></td>
                    </tr>
                `;

            } else {
                tableMode = 'single-mode';
                rowCount = 1;
                html = '<tr><td colspan="4" class="text-center text-muted">暂无出海天数数据</td></tr>';
            }

            // 设置表格模式
            const table = $('#daysStatsTable');
            table.removeClass('single-mode compare-mode multi-mode').addClass(tableMode);

            // 动态计算行高
            setTableRowHeight(tableMode, rowCount);

            $('#daysStatsTableBody').html(html);
        }

        // 动态设置表格行高
        function setTableRowHeight(tableMode, rowCount) {
            let tableHeight, headerHeight, rowHeight;

            if (tableMode === 'single-mode') {
                headerHeight = 32;
                rowHeight = 70; // 固定行高70px
                tableHeight = headerHeight + (rowHeight * rowCount);
            } else if (tableMode === 'compare-mode') {
                headerHeight = 32;
                rowHeight = 70; // 固定行高70px
                tableHeight = headerHeight + (rowHeight * rowCount);
            } else if (tableMode === 'multi-mode') {
                // 容器高度350px，内边距30px(上下各15px)，可用高度320px
                // 但要保守一点，使用300px确保不超出
                tableHeight = 300;
                headerHeight = 35;
                rowHeight = (tableHeight - headerHeight) / rowCount;

                // 限制最大行高为70px，最小行高为20px
                if (rowHeight > 70) {
                    rowHeight = 70;
                    tableHeight = headerHeight + (rowHeight * rowCount);
                } else if (rowHeight < 20) {
                    rowHeight = 20;
                    tableHeight = headerHeight + (rowHeight * rowCount);
                    // 如果计算出的高度超过可用空间，强制压缩
                    if (tableHeight > 300) {
                        tableHeight = 300;
                        rowHeight = (tableHeight - headerHeight) / rowCount;
                    }
                }
            }

            // 设置动态样式
            const style = `
                .stats-table.${tableMode} {
                    height: ${tableHeight}px !important;
                }
                .stats-table.${tableMode} th {
                    height: ${headerHeight}px;
                    padding: ${Math.max(6, headerHeight * 0.25)}px 10px;
                    font-size: ${Math.max(12, Math.min(16, headerHeight * 0.35))}px;
                }
                .stats-table.${tableMode} td {
                    height: ${rowHeight}px;
                    padding: ${Math.max(6, rowHeight * 0.25)}px 10px;
                    font-size: ${Math.max(12, Math.min(16, rowHeight * 0.35))}px;
                }
                .stats-table.${tableMode} .badge {
                    font-size: ${Math.max(10, Math.min(14, rowHeight * 0.3))}px;
                    padding: ${Math.max(3, rowHeight * 0.15)}px ${Math.max(6, rowHeight * 0.25)}px;
                }
            `;

            // 移除旧样式并添加新样式
            $('#dynamicTableStyle').remove();
            $('<style id="dynamicTableStyle">' + style + '</style>').appendTo('head');
        }

        // 显示提示信息
        function showAlert(message, type = 'success') {
            const alertBox = $('#alertBox');
            alertBox.removeClass('alert-success alert-danger alert-warning alert-info')
                    .addClass('alert-' + type)
                    .html('<i class="fas fa-' + (type === 'success' ? 'check-circle' : (type === 'info' ? 'info-circle' : 'exclamation-triangle')) + ' mr-2"></i>' + message)
                    .fadeIn();

            setTimeout(function() {
                alertBox.fadeOut();
            }, 3000);
        }

        // 年度趋势对比
        let yearlyTrendChart = null;
        function renderYearlyTrendChart(data) {
            const ctx = document.getElementById('yearlyTrendChart').getContext('2d');

            if (yearlyTrendChart) {
                yearlyTrendChart.destroy();
            }

            yearlyTrendChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: data.years || [],
                    datasets: [{
                        label: '总出海天数',
                        data: data.totalDays || [],
                        borderColor: '#007aff',
                        backgroundColor: 'rgba(0, 122, 255, 0.1)',
                        tension: 0.4,
                        fill: false
                    }, {
                        label: '一站天数',
                        data: data.station1Days || [],
                        borderColor: '#34c759',
                        backgroundColor: 'rgba(52, 199, 89, 0.1)',
                        tension: 0.4,
                        fill: false
                    }, {
                        label: '二站天数',
                        data: data.station2Days || [],
                        borderColor: '#ff9500',
                        backgroundColor: 'rgba(255, 149, 0, 0.1)',
                        tension: 0.4,
                        fill: false
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    },
                    plugins: {
                        legend: {
                            position: 'top'
                        }
                    }
                }
            });
        }

        // 渲染详细统计表格
        function renderDetailStatsTable(stats) {
            let html = '';
            if (stats && stats.length > 0) {
                stats.forEach(stat => {
                    html += `
                        <tr>
                            <td><strong>${stat.item}</strong></td>
                            <td><span class="badge badge-primary">${stat.value}</span></td>
                            <td class="text-muted">${stat.description}</td>
                        </tr>
                    `;
                });
            } else {
                html = '<tr><td colspan="3" class="text-center text-muted">暂无详细统计数据</td></tr>';
            }
            $('#detailStatsTable').html(html);
        }

        // 渲染多年份月度平均图
        function renderMultiYearMonthlyChart(multiData) {
            const ctx = document.getElementById('monthlyChart').getContext('2d');

            if (monthlyChart) {
                monthlyChart.destroy();
            }

            // 计算每年的月度数据
            const datasets = [];
            const colors = ['#007aff', '#34c759', '#ff9500', '#ff3b30', '#5856d6', '#af52de'];

            multiData.years.forEach((year, index) => {
                const station1Monthly = Array(12).fill(0);
                const station2Monthly = Array(12).fill(0);

                multiData.monthlyData.forEach(data => {
                    if (data.year == year) {
                        const monthIndex = data.month - 1;
                        if (data.station.includes('一站')) {
                            station1Monthly[monthIndex] = parseInt(data.total_days) || 0;
                        } else if (data.station.includes('二站')) {
                            station2Monthly[monthIndex] = parseInt(data.total_days) || 0;
                        }
                    }
                });

                const color = colors[index % colors.length];
                datasets.push({
                    label: `${year}年总计`,
                    data: station1Monthly.map((v, i) => v + station2Monthly[i]),
                    borderColor: color,
                    backgroundColor: color + '20',
                    tension: 0.4,
                    fill: false
                });
            });

            monthlyChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
                    datasets: datasets
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    },
                    plugins: {
                        legend: {
                            position: 'top'
                        },
                        title: {
                            display: true,
                            text: '多年份月度出海天数趋势对比'
                        }
                    }
                }
            });
        }

        // 渲染多年份班次分布
        function renderMultiYearStationChart(multiData) {
            const ctx = document.getElementById('stationChart').getContext('2d');

            if (stationChart) {
                stationChart.destroy();
            }

            const years = multiData.years.map(y => y + '年');
            const station1Data = multiData.stats.map(s => parseInt(s.station1_days) || 0);
            const station2Data = multiData.stats.map(s => parseInt(s.station2_days) || 0);

            stationChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: years,
                    datasets: [{
                        label: '一站天数',
                        data: station1Data,
                        backgroundColor: 'rgba(0, 122, 255, 0.8)',
                        borderColor: '#007aff',
                        borderWidth: 1,
                        borderRadius: 4,
                        borderSkipped: false
                    }, {
                        label: '二站天数',
                        data: station2Data,
                        backgroundColor: 'rgba(52, 199, 89, 0.8)',
                        borderColor: '#34c759',
                        borderWidth: 1,
                        borderRadius: 4,
                        borderSkipped: false
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        x: {
                            stacked: false,  // 改为并列显示
                            grid: {
                                display: false
                            }
                        },
                        y: {
                            stacked: false,  // 改为并列显示
                            beginAtZero: true,
                            grid: {
                                color: 'rgba(0,0,0,0.1)'
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                usePointStyle: true,
                                padding: 20
                            }
                        },
                        title: {
                            display: true,
                            text: '多年份班次分布对比',
                            font: {
                                size: 14,
                                weight: 'bold'
                            },
                            padding: {
                                top: 10,
                                bottom: 20
                            }
                        }
                    },
                    interaction: {
                        intersect: false,
                        mode: 'index'
                    },
                    animation: {
                        duration: 1000,
                        easing: 'easeInOutQuart'
                    }
                }
            });
        }

        // 导出分析报告
        function exportAnalysisReport() {
            const analysisMode = $('#analysisMode').val();
            const year = $('#analysisYear').val();
            const compareYear = $('#compareYear').val();

            // 获取多年度参数
            let years = '';
            if (analysisMode === 'multi') {
                const multiYearRange = $('#multiYearRange').val();
                if (multiYearRange === 'custom') {
                    const startYear = $('#startYear').val();
                    const endYear = $('#endYear').val();
                    if (startYear && endYear) {
                        const yearsList = [];
                        for (let y = parseInt(startYear); y <= parseInt(endYear); y++) {
                            yearsList.push(y);
                        }
                        years = yearsList.join(',');
                    }
                } else {
                    // 最近N年
                    const currentYear = new Date().getFullYear();
                    const yearCount = parseInt(multiYearRange);
                    const yearsList = [];
                    for (let i = 0; i < yearCount; i++) {
                        yearsList.push(currentYear - i);
                    }
                    years = yearsList.reverse().join(',');
                }
            }

            if (!analysisMode) {
                showAlert('请先选择分析模式', 'warning');
                return;
            }

            // 根据分析模式验证参数
            if (analysisMode === 'single' && !year) {
                showAlert('请选择分析年份', 'warning');
                return;
            }

            if (analysisMode === 'compare' && (!year || !compareYear)) {
                showAlert('请选择对比年份', 'warning');
                return;
            }

            if (analysisMode === 'multi' && !years) {
                showAlert('请选择分析年份', 'warning');
                return;
            }

            showAlert('正在生成分析报告...', 'info');

            // 创建表单并提交
            const form = $('<form>', {
                'method': 'POST',
                'action': '../api/export_analysis_report.php',
                'target': '_blank'
            });

            // 添加参数
            form.append($('<input>', {
                'type': 'hidden',
                'name': 'analysisMode',
                'value': analysisMode
            }));

            if (year) {
                form.append($('<input>', {
                    'type': 'hidden',
                    'name': 'year',
                    'value': year
                }));
            }

            if (compareYear) {
                form.append($('<input>', {
                    'type': 'hidden',
                    'name': 'compareYear',
                    'value': compareYear
                }));
            }

            if (years) {
                form.append($('<input>', {
                    'type': 'hidden',
                    'name': 'years',
                    'value': years
                }));
            }

            // 提交表单
            form.appendTo('body').submit().remove();

            setTimeout(() => {
                showAlert('分析报告导出完成', 'success');
            }, 2000);
        }
    </script>
</body>
</html>
