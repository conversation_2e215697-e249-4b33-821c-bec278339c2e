<?php
require_once '../includes/config.php';
requireRole(['admin','manager']);

// 处理表单提交
if($_SERVER['REQUEST_METHOD'] == 'POST') {
    $powerId = $_POST['power_id'];
    $name = trim($_POST['name']);
    $location = trim($_POST['location']);
    $remark = trim($_POST['remark']);

    try {
        // 检查名称唯一性
        $check = $pdo->prepare("SELECT id FROM rcp_cabinet WHERE name = ?");
        $check->execute([$name]);
        if($check->fetch() && empty($_POST['id'])) {
            die(json_encode(['status'=>'error','message'=>'RCP柜名称已存在']));
        }

        if(isset($_POST['id']) && !empty($_POST['id'])) {
            // 更新
            $stmt = $pdo->prepare("UPDATE rcp_cabinet SET 
                power_id=?, name=?, location=?, remark=? 
                WHERE id=?");
            $stmt->execute([$powerId, $name, $location, $remark, $_POST['id']]);
            logAction('update', 'rcp_cabinet', ['id' => $_POST['id']]);
            
            // 添加调试信息
            error_log("更新RCP柜成功: ID=" . $_POST['id']);
        } else {
            // 新建
            $stmt = $pdo->prepare("INSERT INTO rcp_cabinet 
                (power_id, name, location, remark) 
                VALUES (?,?,?,?)");
            $stmt->execute([$powerId, $name, $location, $remark]);
            $newId = $pdo->lastInsertId();
            logAction('create', 'rcp_cabinet', ['name' => $name, 'id' => $newId]);
            
            // 添加调试信息
            error_log("创建RCP柜成功: ID=" . $newId);
        }
        
        // 检查是否是AJAX请求
        if(!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
            echo json_encode(['status'=>'success', 'message'=>'操作成功']);
        } else {
            // 直接表单提交，重定向到第一页
            header("Location: rcp_cabinet.php?added=1");
        }
        exit();
    } catch(PDOException $e) {
        error_log("数据库错误: " . $e->getMessage());
        die(json_encode(['status'=>'error','message'=>$e->getMessage()]));
    }
}

// 删除处理
if(isset($_GET['delete'])) {
    $stmt = $pdo->prepare("DELETE FROM rcp_cabinet WHERE id = ?");
    $stmt->execute([$_GET['delete']]);
    logAction('delete', 'rcp_cabinet', ['id' => $_GET['delete']]);
    
    // 保持分页和搜索状态
    $page = isset($_GET['page']) ? intval($_GET['page']) : 1;
    $search = isset($_GET['search']) ? urlencode($_GET['search']) : '';
    header("Location: rcp_cabinet.php?page=$page&search=$search&deleted=1");
    exit();
}

// 获取所有电源柜
$powerCabinets = $pdo->query("SELECT * FROM power_cabinet")->fetchAll();

// 分页和搜索参数
$page = isset($_GET['page']) ? intval($_GET['page']) : 1;
$limit = 10; // 每页显示10条
$offset = ($page - 1) * $limit;
$search = isset($_GET['search']) ? trim($_GET['search']) : '';

// 获取RCP柜数据（添加搜索和分页）
$params = [];
$whereClause = "";

if (!empty($search)) {
    $whereClause = " WHERE r.name LIKE ? OR p.name LIKE ? OR r.location LIKE ?";
    $params = ["%$search%", "%$search%", "%$search%"];
}

// 获取总记录数
$countSql = "SELECT COUNT(*) FROM rcp_cabinet r
             JOIN power_cabinet p ON r.power_id = p.id" . $whereClause;
$stmt = $pdo->prepare($countSql);
$stmt->execute($params);
$totalRecords = $stmt->fetchColumn();
$totalPages = ceil($totalRecords / $limit);

// 获取分页数据
$sql = "SELECT r.*, p.name AS power_name
        FROM rcp_cabinet r
        JOIN power_cabinet p ON r.power_id = p.id" . 
        $whereClause . " ORDER BY r.id ASC LIMIT $offset, $limit";
$stmt = $pdo->prepare($sql);
$stmt->execute($params);
$rcps = $stmt->fetchAll();
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RCP柜管理</title>
    <link rel="stylesheet" href="../assets/css/bootstrap.min.css">
    <link href="../assets/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/apple-style.css" rel="stylesheet">
    <script src="../assets/js/jquery.min.js"></script>
    <script src="../assets/js/bootstrap.bundle.min.js"></script>
    <style>
        /* 表格文字居中 */
        .table th, .table td {
            text-align: center;
            vertical-align: middle;
        }
        
        /* RCP柜名称样式 */
        .rcp-name {
            cursor: pointer;
            color: #28a745;
            text-decoration: underline;
        }
        
        .rcp-name:hover {
            color: #218838;
        }
        
        /* 位号列表样式 */
        .tag-list {
            display: none;
            margin-top: 10px;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 4px;
            border: 1px solid #dee2e6;
        }
        
        .tag-list table {
            margin-bottom: 0;
        }
    </style>
</head>
<body>
<div class="container-fluid px-4">
    <!-- 页面标题 -->
    <div class="page-header">
        <h3><i class="fas fa-server mr-2"></i>RCP柜管理</h3>
        <button class="btn btn-primary" data-toggle="modal" data-target="#rcpModal">
            <i class="fas fa-plus"></i> 添加RCP柜
        </button>
    </div>

    <div class="row mb-3">
        <div class="col-md-6">
        </div>
        <div class="col-md-6">
            <form class="form-inline justify-content-end" method="get">
                <small class="form-text text-muted mr-2">提示：可使用空格分隔多个关键词进行组合搜索</small>
                <div class="input-group">
                    <input type="text" class="form-control" name="search" placeholder="搜索RCP柜..." value="<?= htmlspecialchars($search ?? '') ?>">
                    <div class="input-group-append">
                        <button class="btn btn-outline-secondary" type="submit">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <table class="table table-bordered table-hover mt-3">
        <thead class="thead-light">
            <tr>
                <th>序号</th>
                <th>所属电源柜</th>
                <th>RCP柜名称</th>
                <th>位置</th>
                <th>备注</th>
                <th>操作</th>
            </tr>
        </thead>
        <tbody>
            <?php 
            $index = $offset + 1;
            foreach($rcps as $rcp): 
            ?>
            <tr>
                <td><?= $index++ ?></td>
                <td><?= htmlspecialchars($rcp['power_name'] ?? '') ?></td>
                <td>
                    <span class="rcp-name" data-rcp-id="<?= $rcp['id'] ?>"><?= htmlspecialchars($rcp['name'] ?? '') ?></span>
                    <div id="rcp-tags-<?= $rcp['id'] ?>" class="tag-list">
                        <div class="text-center">
                            <div class="spinner-border spinner-border-sm" role="status">
                                <span class="sr-only">加载中...</span>
                            </div>
                            <span>加载位号数据...</span>
                        </div>
                    </div>
                </td>
                <td><?= htmlspecialchars($rcp['location'] ?? '') ?></td>
                <td><?= htmlspecialchars($rcp['remark'] ?? '') ?></td>
                <td>
                    <button class="btn btn-sm btn-warning edit-btn"
                            data-id="<?= $rcp['id'] ?>"
                            data-power-id="<?= $rcp['power_id'] ?>"
                            data-name="<?= htmlspecialchars($rcp['name'] ?? '') ?>"
                            data-location="<?= htmlspecialchars($rcp['location'] ?? '') ?>"
                            data-remark="<?= htmlspecialchars($rcp['remark'] ?? '') ?>">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-sm btn-danger delete-btn" 
                            data-id="<?= $rcp['id'] ?>">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            </tr>
            <?php endforeach; ?>
            
            <?php if(count($rcps) == 0): ?>
            <tr>
                <td colspan="6" class="text-center">没有找到匹配的记录</td>
            </tr>
            <?php endif; ?>
        </tbody>
    </table>

    <!-- 分页导航 -->
    <?php if($totalPages > 1): ?>
    <nav>
        <ul class="pagination justify-content-center">
            <li class="page-item <?= ($page <= 1) ? 'disabled' : '' ?>">
                <a class="page-link" href="?page=<?= $page-1 ?>&search=<?= urlencode($search) ?>">上一页</a>
            </li>
            
            <?php for($i = 1; $i <= $totalPages; $i++): ?>
            <li class="page-item <?= ($page == $i) ? 'active' : '' ?>">
                <a class="page-link" href="?page=<?= $i ?>&search=<?= urlencode($search) ?>"><?= $i ?></a>
            </li>
            <?php endfor; ?>
            
            <li class="page-item <?= ($page >= $totalPages) ? 'disabled' : '' ?>">
                <a class="page-link" href="?page=<?= $page+1 ?>&search=<?= urlencode($search) ?>">下一页</a>
            </li>
        </ul>
    </nav>
    <?php endif; ?>

    <!-- 模态框 -->
    <div class="modal fade" id="rcpModal">
        <div class="modal-dialog">
            <div class="modal-content">
                <form id="rcpForm" method="post" action="rcp_cabinet.php">
                    <input type="hidden" name="id" id="rcpId">
                    <div class="modal-header">
                        <h5 class="modal-title">RCP柜信息</h5>
                        <button type="button" class="close" data-dismiss="modal">&times;</button>
                    </div>
                    <div class="modal-body">
                        <div class="form-group">
                            <label>所属电源柜 *</label>
                            <select name="power_id" class="form-control" required>
                                <?php foreach($powerCabinets as $power): ?>
                                <option value="<?= $power['id'] ?>"><?= $power['name'] ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>RCP柜名称 *</label>
                            <input type="text" name="name" class="form-control" required>
                        </div>
                        <div class="form-group">
                            <label>位置信息 *</label>
                            <input type="text" name="location" class="form-control" required>
                        </div>
                        <div class="form-group">
                            <label>备注说明</label>
                            <textarea name="remark" class="form-control"></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                        <button type="button" id="saveButton" class="btn btn-primary">保存</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
// 修复编辑按钮 - 使用data属性而不是JSON
$('.edit-btn').click(function(){
    const id = $(this).data('id');
    const powerId = $(this).data('power-id');
    const name = $(this).data('name');
    const location = $(this).data('location');
    const remark = $(this).data('remark');
    
    $('#rcpId').val(id);
    $('select[name="power_id"]').val(powerId);
    $('input[name="name"]').val(name);
    $('input[name="location"]').val(location);
    $('textarea[name="remark"]').val(remark);
    $('#rcpModal').modal('show');
});

// 删除确认 - 修复事件绑定和阻止事件冒泡
$('.delete-btn').click(function(e){
    // 阻止事件冒泡
    e.stopPropagation();
    
    if(confirm('确定删除此RCP柜及其所有关联数据？')) {
        window.location.href = 'rcp_cabinet.php?delete=' + $(this).data('id') + '&page=<?= $page ?>&search=<?= urlencode($search) ?>';
    }
});

// 清空模态框（添加新RCP柜时）
$('[data-target="#rcpModal"]').click(function() {
    $('#rcpId').val('');
    $('#rcpForm')[0].reset();
    // 确保第一个电源柜被选中
    if($('select[name="power_id"] option').length > 0) {
        $('select[name="power_id"]').val($('select[name="power_id"] option:first').val());
    }
});

// 使用直接表单提交而不是AJAX
$('#saveButton').click(function() {
    // 表单验证
    const name = $('input[name="name"]').val().trim();
    const location = $('input[name="location"]').val().trim();
    
    if(!name) {
        alert('RCP柜名称不能为空');
        return false;
    }
    
    if(!location) {
        alert('位置信息不能为空');
        return false;
    }
    
    // 显示加载状态
    const submitBtn = $(this);
    const originalText = submitBtn.html();
    submitBtn.html('<i class="fas fa-spinner fa-spin"></i> 处理中...').prop('disabled', true);
    
    // 直接提交表单
    $('#rcpForm').submit();
});

// 添加成功和删除成功的提示
$(document).ready(function() {
    // 检查URL参数
    const urlParams = new URLSearchParams(window.location.search);
    
    // 添加成功提示
    if (urlParams.get('added') === '1') {
        alert('RCP柜添加成功！');
    }
    
    // 删除成功提示
    if (urlParams.get('deleted') === '1') {
        alert('删除成功！');
    }
});

// 点击RCP柜名称显示位号
$(document).on('click', '.rcp-name', function() {
    const rcpId = $(this).data('rcp-id');
    const tagList = $(`#rcp-tags-${rcpId}`);
    const isVisible = tagList.is(':visible');
    
    // 先关闭所有打开的位号列表
    $('.tag-list').slideUp();
    
    // 如果当前点击的位号列表已经是打开状态，则不需要再次打开
    if (isVisible) {
        return;
    }
    
    // 打开当前点击的位号列表
    tagList.slideDown();
    
    // 检查是否已加载数据
    if (tagList.data('loaded')) {
        return;
    }
    
    // 加载位号数据
    $.ajax({
        url: '../api/get_rcp_tags.php',
        type: 'GET',
        data: { rcp_id: rcpId },
        dataType: 'json',
        success: function(response) {
            if (response.status === 'success') {
                // 构建位号表格
                let tableHtml = `
                    <table class="table table-sm table-striped">
                        <thead>
                            <tr>
                                <th>序号</th>
                                <th>位号</th>
                                <th>位号名称</th>
                                <th>备注</th>
                            </tr>
                        </thead>
                        <tbody>
                `;
                
                if (response.data.length > 0) {
                    response.data.forEach((tag, index) => {
                        tableHtml += `
                            <tr>
                                <td>${index + 1}</td>
                                <td>${tag.tag_number || ''}</td>
                                <td>${tag.tag_name || ''}</td>
                                <td>${tag.remark || ''}</td>
                            </tr>
                        `;
                    });
                } else {
                    tableHtml += `
                        <tr>
                            <td colspan="4" class="text-center">该RCP柜下没有直接关联的位号</td>
                        </tr>
                    `;
                }
                
                tableHtml += `
                        </tbody>
                    </table>
                `;
                
                tagList.html(tableHtml);
                tagList.data('loaded', true);
            } else {
                tagList.html(`<div class="alert alert-danger">${response.message || '加载位号失败'}</div>`);
            }
        },
        error: function(xhr, status, error) {
            console.error('加载位号失败:', error);
            console.log('响应:', xhr.responseText);
            tagList.html('<div class="alert alert-danger">加载位号失败，请稍后再试</div>');
        }
    });
});
</script>

<!-- 添加错误修复脚本 -->
<script src="../assets/js/fix_frontend_errors.js"></script>
</body>
</html>