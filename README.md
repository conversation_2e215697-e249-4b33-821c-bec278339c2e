# 工艺业务流艰苦津贴计算系统

一个专业的企业津贴计算管理系统，支持多岗位津贴计算、数据管理、Excel导出等功能。

## 项目文件结构

```
project/
├── index.php                 # 主入口文件
├── README.md                 # 项目说明文档
├── pages/                    # 页面文件夹
│   ├── dashboard.php         # 控制面板
│   ├── jintie.php           # 津贴计算主页面
│   ├── login.php            # 登录页面
│   ├── users.php            # 用户管理
│   ├── personnel_manage.php  # 人员管理
│   ├── power_cabinet.php    # 电源柜管理
│   ├── rcp_cabinet.php      # RCP柜管理
│   ├── junction_box.php     # 分线箱管理
│   ├── terminal.php         # 位号管理
│   ├── valve.php            # 阀门管理
│   ├── operation_logs.php   # 操作日志
│   ├── change_password.php  # 修改密码
│   ├── reset_password.php   # 重置密码
│   └── hardship_allowance.php # 艰苦津贴
├── api/                     # API接口文件夹
│   ├── ajax_dept_settings.php    # 部门设置API
│   ├── api.php                   # 通用API
│   ├── check_existing_data.php   # 检查现有数据
│   ├── delete_history_record.php # 删除历史记录
│   ├── export_jintie_excel.php   # Excel导出API (新增)
│   ├── get_history_list.php      # 获取历史记录列表
│   ├── get_rcp.php              # 获取RCP数据
│   ├── get_rcp_tags.php         # 获取RCP标签
│   ├── get_tags.php             # 获取标签
│   ├── load_jintie_data.php     # 加载津贴数据
│   ├── save_jintie_data.php     # 保存津贴数据
│   └── save_jintie_data_new.php # 保存津贴数据(新版)
├── includes/                # 配置和公共文件夹
│   ├── config.php           # 数据库配置和公共函数
│   ├── do_login.php         # 登录处理
│   └── logout.php           # 退出登录
├── assets/                  # 静态资源文件夹
│   ├── css/                 # CSS样式文件
│   │   ├── bootstrap.min.css
│   │   └── all.min.css
│   ├── js/                  # JavaScript文件
│   │   ├── jquery.min.js
│   │   ├── bootstrap.bundle.min.js
│   │   └── fix_frontend_errors.js
│   ├── webfonts/           # 字体文件
│   └── templates/          # 模板文件
├── database/               # 数据库相关文件夹
│   ├── create_jintie_tables.sql  # 创建津贴表SQL
│   └── init_database.php         # 数据库初始化
└── utils/                  # 工具脚本文件夹
    └── remove_console_logs.php   # 移除调试代码工具
```

## 文件夹说明

### pages/ - 页面文件夹
存放所有用户界面页面文件，包括：
- 主要功能页面（津贴计算、设备管理等）
- 用户管理页面
- 登录和认证相关页面

### api/ - API接口文件夹
存放所有后端API接口文件，负责：
- 数据的增删改查操作
- 业务逻辑处理
- 与前端的数据交互

### includes/ - 配置和公共文件夹
存放配置文件和公共函数：
- 数据库连接配置
- 会话管理
- 公共函数库
- 认证处理

### assets/ - 静态资源文件夹
存放前端静态资源：
- CSS样式文件
- JavaScript脚本文件
- 字体文件
- 图片和模板文件

### database/ - 数据库相关文件夹
存放数据库相关文件：
- SQL建表脚本
- 数据库初始化脚本
- 数据迁移脚本

### utils/ - 工具脚本文件夹
存放开发和维护工具：
- 代码清理工具
- 数据处理脚本
- 其他辅助工具

## 路径引用规则

### 页面文件 (pages/)
- 引用配置文件: `../includes/config.php`
- 引用CSS文件: `../assets/css/`
- 引用JS文件: `../assets/js/`
- 引用模板文件: `../assets/templates/`
- 引用Composer包: `../../vendor/autoload.php`
- 调用API: `../api/`

### API文件 (api/)
- 引用配置文件: `../includes/config.php`

### 主入口文件 (index.php)
- 引用配置文件: `includes/config.php`
- 引用CSS文件: `assets/css/`
- 引用JS文件: `assets/js/`
- 加载页面: `pages/`

## 外部依赖

### Composer包管理
项目使用PhpSpreadsheet等Composer包，vendor目录位于：
```
D:\WZ\Apache\Apache24\htdocs\vendor\
```

页面文件中引用vendor的路径为：
```php
require_once __DIR__.'/../../vendor/autoload.php';
```

## 系统特性

### 津贴计算系统 (核心功能)
- **多岗位支持**: 生产、监控、CB26等不同岗位
- **智能计算**: 固定津贴、浮动津贴、请假扣除、奖励计算
- **可配置参数**: 支持岗位基础金额、人员数量等参数设置
- **预览编辑**: 计算结果预览，支持手动调整
- **历史数据管理**: 按日期保存、加载、删除历史记录
- **Excel导出**: 专业格式Excel文件，支持岗位颜色区分
- **数据验证**: 覆盖提醒、数据完整性检查
- **一键复制**: 支持姓名、金额、月度信息快速复制

### 用户管理
- 角色权限控制
- 登录安全机制
- 操作日志记录

### 设备管理
- 电源柜管理
- RCP柜管理
- 分线箱管理
- 阀门管理

## 安全特性
- 会话管理
- 登录失败锁定
- 操作日志记录
- 权限验证

## 技术栈
- **后端**: PHP 7.4+
- **数据库**: MySQL 5.7+
- **前端**: Bootstrap 4, jQuery
- **图标**: Font Awesome
- **Excel处理**: PhpOffice/PhpSpreadsheet
- **包管理**: Composer

## 津贴计算系统详细功能

### 🧮 计算功能
- **固定津贴**: 根据岗位设置基础金额
- **浮动津贴**: 按人员数量和分配比例计算
- **请假扣除**: 支持请假天数自动扣除
- **奖励计算**: 个人奖励和总金额奖励
- **实时预览**: 计算结果实时显示和调整

### 📊 数据管理
- **月度管理**: 按月度和日期组织数据
- **历史记录**: 完整的历史数据保存和查询
- **数据覆盖**: 智能检测重复数据并提醒
- **批量操作**: 支持批量数据处理

### 📋 Excel导出功能
- **专业格式**: 标题、日期、表头完整格式
- **岗位颜色**: 不同岗位显示不同背景颜色
  - 生产岗位: 浅绿色 (#E8F5E8)
  - 监控岗位: 浅蓝色 (#E8F4FD)
  - CB26岗位: 浅橙色 (#FFF2E8)
- **完整样式**: 边框、对齐、字体、列宽等
- **智能命名**: 文件名包含月度和日期信息

### 🎛️ 参数配置
- **岗位设置**: 监控岗位和CB26岗位基础金额
- **人员配置**: 总人员数量设置
- **津贴总额**: 可配置的津贴总金额
- **分配比例**: 灵活的分配比例设置

### 📱 用户界面
- **响应式设计**: 支持各种屏幕尺寸
- **直观操作**: 简洁明了的操作界面
- **实时反馈**: 操作状态实时显示
- **错误处理**: 完善的错误提示和处理

### 🔒 安全特性
- **权限控制**: 基于角色的访问控制
- **会话管理**: 安全的用户会话处理
- **数据验证**: 严格的输入数据验证
- **操作日志**: 完整的操作记录

## 安装和配置

### 环境要求
- PHP 7.4 或更高版本
- MySQL 5.7 或更高版本
- Apache/Nginx Web服务器
- Composer (用于依赖管理)

### 安装步骤
1. 克隆项目到Web服务器目录
2. 运行 `composer install` 安装依赖
3. 配置数据库连接 (`includes/config.php`)
4. 导入数据库表结构 (`database/create_jintie_tables.sql`)
5. 设置Web服务器权限

### 目录权限
确保以下目录具有写入权限：
- `assets/` (静态资源)
- `database/` (数据库文件)

## 使用说明

### 津贴计算流程
1. **设置参数**: 配置岗位基础金额、人员数量等
2. **选择日期**: 选择计算的月度和具体日期
3. **输入数据**: 输入员工出勤天数等信息
4. **执行计算**: 点击计算按钮进行津贴计算
5. **预览调整**: 在预览界面进行必要的手动调整
6. **保存数据**: 确认后保存到数据库
7. **导出Excel**: 生成专业格式的Excel报表

### 历史数据管理
- **查看历史**: 按年份和月份查看历史记录
- **加载数据**: 加载历史数据进行查看或修改
- **删除记录**: 删除不需要的历史记录

## 更新日志

### v2.0 (2025-06-25)
- ✅ 新增专业Excel导出功能 (使用PhpSpreadsheet)
- ✅ 添加岗位颜色区分显示
- ✅ 优化数据获取和处理逻辑
- ✅ 改进用户界面和交互体验
- ✅ 增强错误处理和调试功能

### v1.0 (初始版本)
- ✅ 基础津贴计算功能
- ✅ 用户管理和权限控制
- ✅ 设备管理模块
- ✅ 基础数据导出功能

## 维护和支持

### 常见问题
1. **Excel导出失败**: 检查PhpSpreadsheet库是否正确安装
2. **权限错误**: 确认用户角色和权限设置
3. **数据丢失**: 检查数据库连接和表结构

### 技术支持
如有技术问题，请检查：
- PHP错误日志
- 数据库连接状态
- 文件权限设置
- Composer依赖安装

---

**项目维护**: 持续更新和改进中
**最后更新**: 2025年6月25日
