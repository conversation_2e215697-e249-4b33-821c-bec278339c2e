<?php
// 批量删除console.log的脚本

$file = 'jintie.php';
$content = file_get_contents($file);

// 删除所有console.log语句（包括多行的）
$patterns = [
    '/\s*console\.log\([^;]*\);\s*\n?/',
    '/\s*console\.error\([^;]*\);\s*\n?/',
    '/\s*console\.warn\([^;]*\);\s*\n?/',
    '/\s*console\.info\([^;]*\);\s*\n?/',
];

foreach ($patterns as $pattern) {
    $content = preg_replace($pattern, '', $content);
}

// 删除空行（连续的空行合并为一个）
$content = preg_replace('/\n\s*\n\s*\n/', "\n\n", $content);

// 保存文件
file_put_contents($file, $content);

echo "已删除所有console.log语句\n";
?>
