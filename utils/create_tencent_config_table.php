<?php
/**
 * 创建腾讯云配置表并初始化数据
 */

// 引入数据库配置
require_once __DIR__ . '/../includes/config.php';

try {
    // 创建腾讯云配置表
    $sql = "CREATE TABLE IF NOT EXISTS `tencent_config` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `config_key` varchar(50) NOT NULL COMMENT '配置键名',
        `config_value` text NOT NULL COMMENT '配置值',
        `config_desc` varchar(200) DEFAULT NULL COMMENT '配置描述',
        `is_encrypted` tinyint(1) DEFAULT 0 COMMENT '是否加密存储',
        `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
        `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        UNIQUE KEY `uk_config_key` (`config_key`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='腾讯云配置表'";
    
    $pdo->exec($sql);
    echo "腾讯云配置表创建成功！\n";
    
    // 检查是否已有配置数据
    $checkSql = "SELECT COUNT(*) FROM tencent_config";
    $stmt = $pdo->query($checkSql);
    $count = $stmt->fetchColumn();
    
    if ($count == 0) {
        // 插入初始配置数据
        $configs = [
            [
                'config_key' => 'secret_id',
                'config_value' => 'AKIDeASZS8aqtiYC92jx24vabbO1fXt79NPR',
                'config_desc' => '腾讯云API密钥ID',
                'is_encrypted' => 1
            ],
            [
                'config_key' => 'secret_key',
                'config_value' => 'rVv75pMfkcEeqoY4ViiU3OVO4hAIMF5r',
                'config_desc' => '腾讯云API密钥Key',
                'is_encrypted' => 1
            ],
            [
                'config_key' => 'region',
                'config_value' => 'ap-beijing',
                'config_desc' => '腾讯云服务地域',
                'is_encrypted' => 0
            ]
        ];
        
        $insertSql = "INSERT INTO tencent_config (config_key, config_value, config_desc, is_encrypted) VALUES (?, ?, ?, ?)";
        $stmt = $pdo->prepare($insertSql);
        
        foreach ($configs as $config) {
            // 对敏感信息进行简单加密
            $value = $config['is_encrypted'] ? base64_encode($config['config_value']) : $config['config_value'];
            $stmt->execute([
                $config['config_key'],
                $value,
                $config['config_desc'],
                $config['is_encrypted']
            ]);
        }
        
        echo "初始配置数据插入成功！\n";
    } else {
        echo "配置数据已存在，跳过初始化。\n";
    }
    
    echo "腾讯云配置表初始化完成！\n";
    
} catch (Exception $e) {
    echo "初始化失败: " . $e->getMessage() . "\n";
}
?>
